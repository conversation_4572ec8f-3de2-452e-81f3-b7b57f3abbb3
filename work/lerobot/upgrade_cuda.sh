#!/bin/bash

echo "=== NVIDIA CUDA 包升级脚本 ==="

# 备份当前包列表
echo "备份当前环境..."
pip freeze > backup_packages_$(date +%Y%m%d_%H%M%S).txt

# 显示当前 NVIDIA 包版本
echo "当前 NVIDIA CUDA 包版本："
pip list | grep nvidia

echo ""
echo "开始升级..."

# 方法1：逐个升级主要的 NVIDIA CUDA 包
echo "升级核心 NVIDIA CUDA 包..."
pip install --upgrade \
    nvidia-cublas-cu12 \
    nvidia-cuda-runtime-cu12 \
    nvidia-cudnn-cu12 \
    nvidia-cufft-cu12 \
    nvidia-curand-cu12 \
    nvidia-cusolver-cu12 \
    nvidia-cusparse-cu12 \
    nvidia-nccl-cu12 \
    nvidia-nvtx-cu12

# 方法2：自动检测并升级所有 nvidia 包
echo "自动升级所有 NVIDIA 包..."
NVIDIA_PACKAGES=$(pip list | grep "^nvidia-" | awk '{print $1}' | tr '\n' ' ')

if [ -n "$NVIDIA_PACKAGES" ]; then
    echo "找到的 NVIDIA 包: $NVIDIA_PACKAGES"
    pip install --upgrade $NVIDIA_PACKAGES
else
    echo "未找到 NVIDIA 包"
fi

echo ""
echo "升级完成！新的版本："
pip list | grep nvidia

echo ""
echo "验证 PyTorch CUDA 功能..."
python3 -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU数量: {torch.cuda.device_count()}')
    # 简单测试
    try:
        x = torch.randn(100, 100).cuda()
        y = torch.mm(x, x)
        print('CUDA功能测试通过')
    except Exception as e:
        print(f'CUDA测试失败: {e}')
else:
    print('CUDA不可用')
"

echo "脚本执行完成！"