# Inspired by
# https://github.com/huggingface/peft/blob/main/.github/workflows/build_docker_images.yml
name: Builds

on:
  workflow_dispatch:
  workflow_call:
  schedule:
    - cron: "0 1 * * *"

env:
  PYTHON_VERSION: "3.10"

jobs:
  latest-cpu:
    name: CPU
    runs-on:
      group: aws-general-8-plus
    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install git-lfs
          git lfs install

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Check out code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push CPU
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/lerobot-cpu/Dockerfile
          push: true
          tags: huggingface/lerobot-cpu
          build-args: PYTHON_VERSION=${{ env.PYTHON_VERSION }}


  latest-cuda:
    name: GPU
    runs-on:
      group: aws-general-8-plus
    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install git-lfs
          git lfs install

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Check out code
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push GPU
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/lerobot-gpu/Dockerfile
          push: true
          tags: huggingface/lerobot-gpu
          build-args: PYTHON_VERSION=${{ env.PYTHON_VERSION }}


  latest-cuda-dev:
    name: GPU Dev
    runs-on:
      group: aws-general-8-plus
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Check out code
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push GPU dev
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/lerobot-gpu-dev/Dockerfile
          push: true
          tags: huggingface/lerobot-gpu:dev
          build-args: PYTHON_VERSION=${{ env.PYTHON_VERSION }}
