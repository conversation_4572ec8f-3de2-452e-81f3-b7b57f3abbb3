name: Tests

on:
  pull_request:
    branches:
      - main
    paths:
      - "lerobot/**"
      - "tests/**"
      - "examples/**"
      - ".github/**"
      - "poetry.lock"
      - "Makefile"
      - ".cache/**"
  push:
    branches:
      - main
    paths:
      - "lerobot/**"
      - "tests/**"
      - "examples/**"
      - ".github/**"
      - "poetry.lock"
      - "Makefile"
      - ".cache/**"

jobs:
  pytest:
    name: Pytest
    runs-on: ubuntu-latest
    env:
      DATA_DIR: tests/data
      MUJOCO_GL: egl
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true  # Ensure LFS files are pulled

      - name: Install apt dependencies
      # portaudio19-dev is needed to install pyaudio
        run: |
          sudo apt-get update && \
          sudo apt-get install -y libegl1-mesa-dev ffmpeg portaudio19-dev

      - name: Install poetry
        run: |
          pipx install poetry && poetry config virtualenvs.in-project true
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH

      # TODO(rcaden<PERSON>, aliberts): python 3.12 seems to be used in the tests, not python 3.10
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: "poetry"

      - name: Install poetry dependencies
        run: |
          poetry install --all-extras

      - name: Test with pytest
        run: |
          pytest tests -v --cov=./lerobot --durations=0 \
            -W ignore::DeprecationWarning:imageio_ffmpeg._utils:7 \
            -W ignore::UserWarning:torch.utils.data.dataloader:558 \
            -W ignore::UserWarning:gymnasium.utils.env_checker:247 \
            && rm -rf tests/outputs outputs

  pytest-minimal:
    name: Pytest (minimal install)
    runs-on: ubuntu-latest
    env:
      DATA_DIR: tests/data
      MUJOCO_GL: egl
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true  # Ensure LFS files are pulled

      - name: Install apt dependencies
        run: sudo apt-get update && sudo apt-get install -y ffmpeg

      - name: Install poetry
        run: |
          pipx install poetry && poetry config virtualenvs.in-project true
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH

      # TODO(rcadene, aliberts): python 3.12 seems to be used in the tests, not python 3.10
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install poetry dependencies
        run: |
          poetry install --extras "test"

      - name: Test with pytest
        run: |
          pytest tests -v --cov=./lerobot --durations=0 \
            -W ignore::DeprecationWarning:imageio_ffmpeg._utils:7 \
            -W ignore::UserWarning:torch.utils.data.dataloader:558 \
            -W ignore::UserWarning:gymnasium.utils.env_checker:247 \
            && rm -rf tests/outputs outputs


  end-to-end:
    name: End-to-end
    runs-on: ubuntu-latest
    env:
      DATA_DIR: tests/data
      MUJOCO_GL: egl
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true  # Ensure LFS files are pulled

      - name: Install apt dependencies
      # portaudio19-dev is needed to install pyaudio
        run: |
          sudo apt-get update && \
          sudo apt-get install -y libegl1-mesa-dev portaudio19-dev

      - name: Install poetry
        run: |
          pipx install poetry && poetry config virtualenvs.in-project true
          echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: "poetry"

      - name: Install poetry dependencies
        run: |
          poetry install --all-extras

      - name: Test end-to-end
        run: |
          make test-end-to-end \
            && rm -rf outputs
