{"citation": "", "description": "", "features": {"observation.images.cam_high": {"_type": "VideoFrame"}, "observation.images.cam_left_wrist": {"_type": "VideoFrame"}, "observation.images.cam_low": {"_type": "VideoFrame"}, "observation.images.cam_right_wrist": {"_type": "VideoFrame"}, "observation.state": {"feature": {"dtype": "float32", "_type": "Value"}, "length": 14, "_type": "Sequence"}, "observation.effort": {"feature": {"dtype": "float32", "_type": "Value"}, "length": 14, "_type": "Sequence"}, "action": {"feature": {"dtype": "float32", "_type": "Value"}, "length": 14, "_type": "Sequence"}, "episode_index": {"dtype": "int64", "_type": "Value"}, "frame_index": {"dtype": "int64", "_type": "Value"}, "timestamp": {"dtype": "float32", "_type": "Value"}, "next.done": {"dtype": "bool", "_type": "Value"}, "index": {"dtype": "int64", "_type": "Value"}}, "homepage": "", "license": ""}