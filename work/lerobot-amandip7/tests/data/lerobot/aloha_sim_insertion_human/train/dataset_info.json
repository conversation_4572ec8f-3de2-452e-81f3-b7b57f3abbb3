{"citation": "", "description": "", "features": {"observation.images.top": {"_type": "VideoFrame"}, "observation.state": {"feature": {"dtype": "float32", "_type": "Value"}, "length": 14, "_type": "Sequence"}, "action": {"feature": {"dtype": "float32", "_type": "Value"}, "length": 14, "_type": "Sequence"}, "episode_index": {"dtype": "int64", "_type": "Value"}, "frame_index": {"dtype": "int64", "_type": "Value"}, "timestamp": {"dtype": "float32", "_type": "Value"}, "next.done": {"dtype": "bool", "_type": "Value"}, "index": {"dtype": "int64", "_type": "Value"}}, "homepage": "", "license": ""}