# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Inspired by
# https://github.com/huggingface/peft/blob/main/.github/workflows/build_docker_images.yml
name: Builds

on:
  workflow_dispatch:
  workflow_call:
  schedule:
    - cron: "0 1 * * *"

permissions: {}

env:
  PYTHON_VERSION: "3.10"

jobs:
  latest-cpu:
    name: CPU
    runs-on:
      group: aws-general-8-plus
    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install git-lfs
          git lfs install

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          cache-binary: false

      - name: Check out code
        uses: actions/checkout@v4
        with:
          lfs: true
          persist-credentials: false

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push CPU
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/lerobot-cpu/Dockerfile
          push: true
          tags: huggingface/lerobot-cpu
          build-args: PYTHON_VERSION=${{ env.PYTHON_VERSION }}


  latest-cuda:
    name: GPU
    runs-on:
      group: aws-general-8-plus
    steps:
      - name: Install Git LFS
        run: |
          sudo apt-get update
          sudo apt-get install git-lfs
          git lfs install

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          cache-binary: false

      - name: Check out code
        uses: actions/checkout@v4
        with:
          lfs: true
          persist-credentials: false

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push GPU
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/lerobot-gpu/Dockerfile
          push: true
          tags: huggingface/lerobot-gpu
          build-args: PYTHON_VERSION=${{ env.PYTHON_VERSION }}


  latest-cuda-dev:
    name: GPU Dev
    runs-on:
      group: aws-general-8-plus
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          cache-binary: false

      - name: Check out code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push GPU dev
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/lerobot-gpu-dev/Dockerfile
          push: true
          tags: huggingface/lerobot-gpu:dev
          build-args: PYTHON_VERSION=${{ env.PYTHON_VERSION }}
