#!/usr/bin/env python

"""
最终的DC评估脚本
使用方法:
conda activate lerobot && export PYTHONPATH=/home/<USER>/work/lerobot-add_transformer:$PYTHONPATH && python run_dc_eval_final.py
"""

import os
import json
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

def main():
    print("=== LeRobot DC评估脚本 ===")
    
    # 固定路径配置
    base_model_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000/pretrained_model"
    dc_pth_path = "/home/<USER>/work/lerobot-add_transformer/dc_eval_setup/model.pth"
    
    # 验证路径
    if not Path(base_model_path).exists():
        print(f"❌ 基础模型路径不存在: {base_model_path}")
        return 1
    
    if not Path(dc_pth_path).exists():
        print(f"❌ DC权重文件不存在: {dc_pth_path}")
        return 1
    
    config_path = Path(base_model_path) / "config.json"
    safetensors_path = Path(base_model_path) / "model.safetensors"
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return 1
    
    if not safetensors_path.exists():
        print(f"❌ Safetensors文件不存在: {safetensors_path}")
        return 1
    
    print(f"✅ 所有文件验证成功")
    print(f"   基础模型: {base_model_path}")
    print(f"   DC权重: {dc_pth_path}")
    
    # 读取配置文件
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print(f"\n=== 当前模型配置 ===")
    print(f"模型类型: {config.get('type', 'unknown')}")
    print(f"use_dc: {config.get('use_dc', False)}")
    print(f"use_mmdit_dc: {config.get('use_mmdit_dc', False)}")
    print(f"n_dc_tokens: {config.get('n_dc_tokens', 'not set')}")
    print(f"n_dc_layers: {config.get('n_dc_layers', 'not set')}")
    print(f"use_dc_t: {config.get('use_dc_t', 'not set')}")
    
    # 检查DC配置
    needs_dc_config = not config.get('use_dc', False)
    
    if needs_dc_config:
        print(f"\n=== 需要启用DC配置 ===")
        config['use_dc'] = True
        config['use_mmdit_dc'] = False  # 使用TransformerForDiffusion_DC
        
        print(f"✓ 已设置 use_dc = True")
        print(f"✓ 已设置 use_mmdit_dc = False")
    else:
        print(f"\n=== DC配置已启用 ===")
    
    # 获取当前Python解释器路径
    python_executable = sys.executable
    print(f"\n=== 环境信息 ===")
    print(f"Python解释器: {python_executable}")
    print(f"当前工作目录: {Path.cwd()}")
    
    # 创建临时目录用于修改的配置
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_model_path = Path(temp_dir) / "model_with_dc"
        temp_model_path.mkdir()
        
        # 复制文件
        shutil.copy2(safetensors_path, temp_model_path / "model.safetensors")
        
        # 保存修改的配置
        temp_config_path = temp_model_path / "config.json"
        with open(temp_config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n=== 临时模型目录 ===")
        print(f"路径: {temp_model_path}")
        
        # 构建评估命令
        current_dir = Path.cwd()
        
        eval_command = [
            "bash", "-c",
            f"""
            cd {current_dir} &&
            export PYTHONPATH={current_dir}:$PYTHONPATH &&
            CUDA_VISIBLE_DEVICES=3 {python_executable} lerobot/scripts/eval.py \\
                --policy.pretrained_path={temp_model_path} \\
                --policy.dc_pth_path={dc_pth_path} \\
                --env.type=pusht \\
                --eval.batch_size=10 \\
                --eval.n_episodes=10 \\
                --policy.device=cuda \\
                --policy.use_amp=false
            """
        ]
        
        print(f"\n=== 评估命令 ===")
        print(f"工作目录: {current_dir}")
        print(f"模型路径: {temp_model_path}")
        print(f"DC权重: {dc_pth_path}")
        print(f"Python: {python_executable}")
        
        print(f"\n将要执行:")
        cmd_str = eval_command[2].strip()
        for line in cmd_str.split(' && '):
            print(f"  {line.strip()}")
        
        # 询问用户确认
        response = input(f"\n是否执行评估? (y/N): ")
        
        if response.lower() == 'y':
            print(f"\n=== 开始评估 ===")
            try:
                result = subprocess.run(eval_command, capture_output=False, text=True)
                if result.returncode == 0:
                    print(f"\n✅ 评估完成成功")
                else:
                    print(f"\n❌ 评估失败，返回码: {result.returncode}")
                return result.returncode
            except Exception as e:
                print(f"\n❌ 执行评估时出错: {e}")
                return 1
        else:
            print(f"\n取消执行")
            print(f"你可以手动运行以下命令:")
            print(f"cd {current_dir}")
            for line in cmd_str.split(' && '):
                print(f"{line.strip()}")
            return 0

if __name__ == "__main__":
    sys.exit(main())