import torch
import argparse
import os

def print_pth_structure(pth_data, indent=0):
    """递归打印 .pth 文件中的结构"""
    prefix = ' ' * indent
    if isinstance(pth_data, dict):
        for key, value in pth_data.items():
            print(f"{prefix}- Key: '{key}' | Type: {type(value)}")
            print_pth_structure(value, indent + 4)
    elif isinstance(pth_data, list):
        print(f"{prefix}- List of length {len(pth_data)}")
        if len(pth_data) > 0:
            print_pth_structure(pth_data[0], indent + 4)
    elif isinstance(pth_data, torch.Tensor):
        print(f"{prefix}- Tensor: shape {pth_data.shape}, dtype {pth_data.dtype}")
    else:
        print(f"{prefix}- Value: {pth_data} ({type(pth_data)})")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--path', type=str, required=True, help='Path to .pth file')
    parser.add_argument('--device', type=str, default='cpu', help='Device to load the file on')
    args = parser.parse_args()

    if not os.path.exists(args.path):
        print(f"[Error] File not found: {args.path}")
        return

    print(f"[Loading] {args.path}")
    pth_data = torch.load(args.path, map_location=args.device)
    print("\n[PTH Structure]:")
    print_pth_structure(pth_data)

if __name__ == "__main__":
    main()
