# DC_t Token选项使用说明

## 概述

现在`train_dc_with_dataset.py`支持通过`--use_dc_t`参数控制是否使用时间依赖的DC tokens (dc_t tokens)。

## 什么是DC_t Tokens？

DC_t tokens是SoftREPA中的时间依赖判别对比tokens，它们：

1. **时间感知**: 根据扩散时间步调整DC tokens的表示
2. **动态调制**: 在不同时间步提供不同的对比学习信号
3. **参数增加**: 会增加模型的可训练参数数量

## 使用方法

### 不使用DC_t Tokens (默认)

```bash
python train_dc_with_dataset.py \
    --checkpoint /path/to/checkpoint \
    --dataset lerobot/pusht \
    --n_dc_tokens 7 \
    --n_dc_layers 6 \
    --batch_size 2 \
    --steps 10000
```

**特点:**
- ✅ 参数更少，训练更快
- ✅ 内存占用更小
- ✅ 实现更简单
- ❌ 可能缺少时间维度的对比学习信号

### 使用DC_t Tokens

```bash
python train_dc_with_dataset.py \
    --checkpoint /path/to/checkpoint \
    --dataset lerobot/pusht \
    --n_dc_tokens 7 \
    --n_dc_layers 6 \
    --use_dc_t \
    --batch_size 2 \
    --steps 10000
```

**特点:**
- ✅ 时间感知的对比学习
- ✅ 可能获得更好的性能
- ❌ 参数更多，训练更慢
- ❌ 内存占用更大

## 参数对比

| 配置 | DC Tokens | DC_t Tokens | 总DC参数 | 说明 |
|------|-----------|-------------|----------|------|
| 不使用dc_t | ✅ | ❌ | 较少 | 基础对比学习 |
| 使用dc_t | ✅ | ✅ | 较多 | 时间感知对比学习 |

## 训练监控

训练过程中会显示详细的DC tokens更新信息：

```
🔍 DC Tokens更新情况:
  net.dc_tokens: 变化量=4.241259
  net.dc_t_tokens.weight: 变化量=15.728898  # 仅在使用dc_t时显示

📈 DC Tokens梯度范数:
  net.dc_tokens: 梯度范数=0.1092015579
  net.dc_t_tokens.weight: 梯度范数=0.0993033126  # 仅在使用dc_t时显示
```

## 选择建议

### 推荐使用DC_t Tokens的情况：
- 有充足的计算资源
- 追求最佳性能
- 数据集较大，能充分利用时间依赖性

### 推荐不使用DC_t Tokens的情况：
- 计算资源有限
- 快速原型验证
- 数据集较小
- 首次尝试SoftREPA方法

## 示例脚本

项目中提供了两个示例脚本：

1. `example_train_without_dc_t.sh` - 不使用dc_t tokens的训练示例
2. `example_train_with_dc_t.sh` - 使用dc_t tokens的训练示例

## 技术实现

修改涉及以下文件：

1. **train_dc_with_dataset.py**:
   - 添加`--use_dc_t`参数
   - 修改`load_and_create_dc_model`函数签名
   - 更新DC tokens监控逻辑

2. **安全检查**:
   - 所有dc_t_tokens相关的代码都添加了空值检查
   - 确保在不使用dc_t时不会出现错误

## 验证测试

运行测试脚本验证功能：

```bash
python test_use_dc_t_param.py
```

测试包括：
- 参数解析验证
- 函数签名检查
- 帮助信息确认

## 注意事项

1. **向后兼容**: 默认行为保持不变（use_dc_t=False）
2. **内存管理**: 使用dc_t时注意GPU内存使用
3. **检查点兼容**: 两种模式的检查点不完全兼容
4. **性能权衡**: 根据具体任务选择合适的配置

## 故障排除

如果遇到问题：

1. 检查CUDA内存是否足够
2. 确认检查点路径正确
3. 验证参数组合是否合理
4. 查看训练日志中的DC tokens更新信息
