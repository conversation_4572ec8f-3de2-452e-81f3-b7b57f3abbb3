#!/usr/bin/env python3
"""
简单的DiffusionModel_DC测试

验证核心功能：
1. 创建DiffusionModel_DC框架
2. 加载预训练权重
3. 初始化DC tokens
"""

import torch
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
from lerobot.configs.policies import PreTrainedConfig


def test_dc_creation_and_loading():
    """测试DiffusionModel_DC创建和加载"""
    print("🧪 测试DiffusionModel_DC创建和预训练权重加载")
    
    # 检查预训练模型路径
    checkpoint_paths = [
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if Path(path).exists():
            checkpoint_path = path
            break
    
    if checkpoint_path is None:
        print("❌ 未找到预训练模型检查点")
        return False
    
    try:
        print(f"📁 使用检查点: {checkpoint_path}")
        
        # 1. 加载预训练模型的配置
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        if config_path.exists():
            config = PreTrainedConfig.from_pretrained(str(config_path.parent))
            print(f"✅ 配置加载成功: {config.type}")
        else:
            print("❌ 配置文件不存在")
            return False
        
        # 2. 使用from_pretrained创建DiffusionModel_DC
        print("🔄 创建DiffusionModel_DC并加载预训练权重...")
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        print("✅ DiffusionModel_DC创建成功!")
        
        # 3. 初始化DC tokens
        print("🔧 初始化DC tokens...")
        model.initialize_dc_tokens()
        print("✅ DC tokens初始化成功!")
        
        # 4. 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        dc_params = sum(p.numel() for name, p in model.named_parameters() if 'dc' in name.lower())
        base_params = total_params - dc_params
        
        print(f"📊 模型参数统计:")
        print(f"   总参数量: {total_params:,}")
        print(f"   DC参数量: {dc_params:,}")
        print(f"   基础参数量: {base_params:,}")
        print(f"   DC参数占比: {dc_params/total_params*100:.2f}%")
        
        # 5. 测试前向传播
        print("🚀 测试前向传播...")
        model.eval()
        
        batch_size = 2
        horizon = 16
        action_dim = 2
        
        # 创建测试输入
        sample = torch.randn(batch_size, horizon, action_dim)
        timestep = torch.randint(0, 100, (batch_size,))
        global_cond = torch.randn(batch_size, 132)  # 假设条件维度
        
        with torch.no_grad():
            output = model(sample, timestep, global_cond)
        
        print(f"✅ 前向传播成功!")
        print(f"   输入形状: {sample.shape}")
        print(f"   输出形状: {output.shape}")
        print(f"   输出统计: min={output.min():.4f}, max={output.max():.4f}")
        
        # 6. 测试DC功能
        print("🎯 测试DC功能...")
        
        # 冻结基础模型参数
        model.freeze_base_model()
        print("✅ 基础模型参数冻结成功!")
        
        # 获取DC参数
        dc_parameters = model.get_dc_parameters()
        print(f"✅ 获取DC参数成功，共{len(dc_parameters)}个可训练参数")
        
        # 打印参数统计
        model.print_parameter_stats()
        print("✅ 参数统计完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dc_contrastive_functionality():
    """测试DC对比学习功能"""
    print("\n🧪 测试DC对比学习功能")
    
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000"
    
    if not Path(checkpoint_path).exists():
        print("⚠️ 跳过对比学习测试，未找到预训练模型")
        return True
    
    try:
        # 加载配置和模型
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        # 创建模拟批次数据
        batch_size = 4
        batch = {
            'action': torch.randn(batch_size, 16, 2),
            'observation.state': torch.randn(batch_size, 2, 64),  # 假设观察数据
        }
        
        print("🔄 测试对比误差计算...")
        
        # 测试对比误差计算
        error_matrix = model.compute_contrastive_error(batch, use_dc=True)
        
        print(f"✅ 对比误差计算成功!")
        print(f"   误差矩阵形状: {error_matrix.shape}")
        print(f"   对角线误差均值: {torch.diag(error_matrix).mean():.4f}")
        print(f"   非对角线误差均值: {error_matrix[~torch.eye(batch_size, dtype=bool)].mean():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比学习测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🚀 DiffusionModel_DC简单验证测试\n")
    
    tests = [
        ("DC创建和加载", test_dc_creation_and_loading),
        ("DC对比学习", test_dc_contrastive_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print("=" * 60)
        success = test_func()
        results.append((test_name, success))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！DiffusionModel_DC简化后的加载逻辑工作正常")
        print("\n✨ 核心功能验证:")
        print("   ✅ DiffusionModel_DC框架创建")
        print("   ✅ 预训练权重加载")
        print("   ✅ DC tokens初始化")
        print("   ✅ 前向传播")
        print("   ✅ DC功能（冻结、参数获取、统计）")
        print("   ✅ 对比学习误差计算")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
