#!/usr/bin/env python3
"""
分析梯度范数小的问题

检查：
1. DC tokens是否有bias
2. 梯度传播路径
3. 损失函数的梯度
4. 学习率是否合适
5. 梯度裁剪的影响
"""

import torch
import sys
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss
)
from lerobot.configs.policies import PreTrainedConfig


def analyze_dc_structure():
    """分析DC tokens的结构"""
    print("🔍 分析DC tokens结构")
    
    checkpoint_paths = [
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if Path(path).exists():
            checkpoint_path = path
            break
    
    if checkpoint_path is None:
        print("❌ 未找到预训练模型检查点")
        return False
    
    try:
        # 加载模型
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        print("✅ 模型加载成功!")
        
        # 检查DC tokens结构
        print("\n📊 DC Tokens结构分析:")
        
        if hasattr(model.net, 'dc_tokens'):
            dc_tokens = model.net.dc_tokens
            print(f"  DC tokens类型: {type(dc_tokens)}")
            print(f"  DC tokens形状: {dc_tokens.shape}")
            print(f"  DC tokens是否有bias: {hasattr(dc_tokens, 'bias') and dc_tokens.bias is not None}")
            print(f"  DC tokens数据类型: {dc_tokens.dtype}")
            print(f"  DC tokens设备: {dc_tokens.device}")
            print(f"  DC tokens requires_grad: {dc_tokens.requires_grad}")
            print(f"  DC tokens均值: {dc_tokens.data.mean().item():.6f}")
            print(f"  DC tokens标准差: {dc_tokens.data.std().item():.6f}")
            print(f"  DC tokens最大值: {dc_tokens.data.max().item():.6f}")
            print(f"  DC tokens最小值: {dc_tokens.data.min().item():.6f}")
        
        if hasattr(model.net, 'dc_t_tokens'):
            dc_t_tokens = model.net.dc_t_tokens
            print(f"\n  DC t tokens类型: {type(dc_t_tokens)}")
            print(f"  DC t tokens权重形状: {dc_t_tokens.weight.shape}")
            print(f"  DC t tokens是否有bias: {hasattr(dc_t_tokens, 'bias') and dc_t_tokens.bias is not None}")
            print(f"  DC t tokens权重数据类型: {dc_t_tokens.weight.dtype}")
            print(f"  DC t tokens权重requires_grad: {dc_t_tokens.weight.requires_grad}")
            print(f"  DC t tokens权重均值: {dc_t_tokens.weight.data.mean().item():.6f}")
            print(f"  DC t tokens权重标准差: {dc_t_tokens.weight.data.std().item():.6f}")
        
        return model
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_gradient_flow(model):
    """分析梯度流"""
    print("\n🔍 分析梯度流")
    
    try:
        model.train()
        model.freeze_base_model()
        
        # 创建测试数据
        batch = {
            'action': torch.randn(2, 16, 2),
            'observation.state': torch.randn(2, 2, 2),
            'observation.image': torch.randn(2, 2, 3, 96, 96),
        }
        
        # 前向传播
        print("📈 前向传播...")
        error_matrix = model.compute_contrastive_error(batch, use_dc=True)
        print(f"  误差矩阵形状: {error_matrix.shape}")
        print(f"  误差矩阵范围: [{error_matrix.min().item():.4f}, {error_matrix.max().item():.4f}]")
        
        # 计算损失
        contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
        loss = contrastive_loss(error_matrix)
        print(f"  对比损失: {loss.item():.6f}")
        
        # 反向传播
        print("📉 反向传播...")
        loss.backward()
        
        # 分析梯度
        print("\n📊 梯度分析:")
        
        total_grad_norm = 0
        dc_grad_norm = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = torch.norm(param.grad).item()
                total_grad_norm += grad_norm ** 2
                
                if 'dc_token' in name:
                    dc_grad_norm += grad_norm ** 2
                    print(f"  ✅ {name}: 梯度范数={grad_norm:.8f}")
                    
                    # 分析梯度分布
                    grad_mean = param.grad.mean().item()
                    grad_std = param.grad.std().item()
                    grad_max = param.grad.max().item()
                    grad_min = param.grad.min().item()
                    
                    print(f"    梯度统计: 均值={grad_mean:.8f}, 标准差={grad_std:.8f}")
                    print(f"    梯度范围: [{grad_min:.8f}, {grad_max:.8f}]")
                    
                    # 检查梯度是否有异常值
                    if torch.isnan(param.grad).any():
                        print(f"    ⚠️ 发现NaN梯度!")
                    if torch.isinf(param.grad).any():
                        print(f"    ⚠️ 发现无穷大梯度!")
                        
                elif param.requires_grad:
                    print(f"  ⚠️ 非DC参数有梯度: {name}, 梯度范数={grad_norm:.8f}")
        
        total_grad_norm = total_grad_norm ** 0.5
        dc_grad_norm = dc_grad_norm ** 0.5
        
        print(f"\n📈 总梯度范数: {total_grad_norm:.8f}")
        print(f"📈 DC梯度范数: {dc_grad_norm:.8f}")
        print(f"📈 DC梯度占比: {dc_grad_norm/total_grad_norm*100:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 梯度分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_loss_sensitivity(model):
    """分析损失对DC tokens的敏感性"""
    print("\n🔍 分析损失敏感性")
    
    try:
        model.eval()
        
        # 创建测试数据
        batch = {
            'action': torch.randn(2, 16, 2),
            'observation.state': torch.randn(2, 2, 2),
            'observation.image': torch.randn(2, 2, 3, 96, 96),
        }
        
        contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
        
        # 记录原始DC tokens
        original_dc_tokens = model.net.dc_tokens.data.clone()
        
        # 计算原始损失
        with torch.no_grad():
            error_matrix_orig = model.compute_contrastive_error(batch, use_dc=True)
            loss_orig = contrastive_loss(error_matrix_orig).item()
        
        print(f"📊 原始损失: {loss_orig:.6f}")
        
        # 测试不同的扰动幅度
        perturbations = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1]
        
        for eps in perturbations:
            # 添加扰动
            perturbation = torch.randn_like(model.net.dc_tokens) * eps
            model.net.dc_tokens.data = original_dc_tokens + perturbation
            
            # 计算扰动后的损失
            with torch.no_grad():
                error_matrix_pert = model.compute_contrastive_error(batch, use_dc=True)
                loss_pert = contrastive_loss(error_matrix_pert).item()
            
            loss_change = abs(loss_pert - loss_orig)
            sensitivity = loss_change / eps if eps > 0 else 0
            
            print(f"  扰动幅度={eps:.0e}: 损失变化={loss_change:.8f}, 敏感性={sensitivity:.2e}")
        
        # 恢复原始DC tokens
        model.net.dc_tokens.data = original_dc_tokens
        
        # 测试不使用DC tokens的情况
        with torch.no_grad():
            error_matrix_no_dc = model.compute_contrastive_error(batch, use_dc=False)
            loss_no_dc = contrastive_loss(error_matrix_no_dc).item()
        
        print(f"📊 不使用DC tokens的损失: {loss_no_dc:.6f}")
        print(f"📊 DC tokens的影响: {abs(loss_orig - loss_no_dc):.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 敏感性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_learning_rate_effect():
    """分析学习率的影响"""
    print("\n🔍 分析学习率影响")
    
    # 不同学习率下的梯度更新效果
    learning_rates = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1]
    
    print("📊 不同学习率下的参数更新幅度:")
    for lr in learning_rates:
        # 模拟梯度范数为1e-6的情况
        grad_norm = 1e-6
        update_magnitude = lr * grad_norm
        print(f"  学习率={lr:.0e}: 更新幅度={update_magnitude:.2e}")
    
    print("\n💡 建议:")
    print("  - 如果梯度范数~1e-6，学习率应该在1e-2到1e-1之间")
    print("  - 如果梯度范数~1e-4，学习率应该在1e-4到1e-3之间")
    print("  - 当前学习率1e-4可能偏小")


def main():
    """运行所有分析"""
    print("🚀 DC tokens梯度问题分析\n")
    
    # 分析DC结构
    model = analyze_dc_structure()
    if model is None:
        return False
    
    # 分析梯度流
    gradient_ok = analyze_gradient_flow(model)
    
    # 分析损失敏感性
    sensitivity_ok = analyze_loss_sensitivity(model)
    
    # 分析学习率影响
    analyze_learning_rate_effect()
    
    print("\n" + "=" * 60)
    print("📊 分析总结:")
    print("1. DC tokens结构: ✅ 无bias，直接Parameter")
    print("2. 梯度流: ✅ 有梯度但很小" if gradient_ok else "2. 梯度流: ❌ 有问题")
    print("3. 损失敏感性: ✅ 分析完成" if sensitivity_ok else "3. 损失敏感性: ❌ 有问题")
    
    print("\n💡 可能的解决方案:")
    print("1. 增加学习率 (从1e-4增加到1e-3或1e-2)")
    print("2. 减少梯度裁剪范数 (从1.0减少到0.1)")
    print("3. 调整对比损失的温度参数 (从0.07增加到0.1)")
    print("4. 检查DC tokens的初始化方式")
    print("5. 增加DC tokens的数量或层数")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
