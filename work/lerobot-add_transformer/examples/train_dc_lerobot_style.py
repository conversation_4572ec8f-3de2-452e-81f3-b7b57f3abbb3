#!/usr/bin/env python

"""
Example script for training DiffusionModel_DC using lerobot's standard data loading pipeline.
This script demonstrates how to integrate DC token training with le<PERSON>bot's dataset factory.
"""

import argparse
import logging
from pathlib import Path
from dataclasses import dataclass
from typing import Optional

import torch
from omegaconf import OmegaConf

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.utils.utils import init_logging
from lerobot.configs.train import TrainPipelineConfig

# Import our DC training components
from lerobot.common.policies.diffusion.transformer_dc_training import train_dc_policy
from lerobot.common.policies.diffusion.transformer_dc_sampler import ContrastiveLoss


@dataclass
class DCTrainingConfig:
    """Configuration for DC token training"""
    # Dataset config
    dataset_repo_id: str = "lerobot/pusht"
    
    # Model config
    n_dc_tokens: int = 4
    n_dc_layers: int = 6
    use_dc_t: bool = True
    pretrained_model_path: Optional[str] = None
    
    # Training config
    device: str = "cuda"
    batch_size: int = 64
    learning_rate: float = 1e-4
    weight_decay: float = 1e-3
    steps: int = 10000
    log_freq: int = 100
    save_freq: int = 1000
    
    # Contrastive loss config
    temp: float = 0.07
    scale: float = 4.0
    
    # Training settings
    use_amp: bool = True
    grad_clip_norm: float = 1.0
    num_workers: int = 4
    seed: Optional[int] = 42


def create_lerobot_config(dc_config: DCTrainingConfig) -> TrainPipelineConfig:
    """
    Create a lerobot-compatible configuration from our DC config.
    """
    # Create a minimal lerobot config
    config_dict = {
        "dataset_repo_id": dc_config.dataset_repo_id,
        "batch_size": dc_config.batch_size,
        "num_workers": dc_config.num_workers,
        "device": dc_config.device,
        "seed": dc_config.seed,
        
        # Policy config (minimal diffusion config)
        "policy": {
            "_target_": "lerobot.common.policies.diffusion.modeling_diffusion.DiffusionPolicy",
            "horizon": 16,
            "n_obs_steps": 1,
            "n_action_steps": 8,
            "diffusion_step_embed_dim": 256,
            "n_layer": 6,
            "n_head": 8,
            "n_cond_layers": 4,
            "use_transformer": True,
            "causal_attn": True,
            "p_drop_emb": 0.1,
            "p_drop_attn": 0.1,
            "use_amp": dc_config.use_amp,
        },
        
        # Training config
        "steps": dc_config.steps,
        "log_freq": dc_config.log_freq,
        "save_freq": dc_config.save_freq,
        "learning_rate": dc_config.learning_rate,
        "weight_decay": dc_config.weight_decay,
        "grad_clip_norm": dc_config.grad_clip_norm,
        
        # DC-specific config
        "n_dc_tokens": dc_config.n_dc_tokens,
        "n_dc_layers": dc_config.n_dc_layers,
        "use_dc_t": dc_config.use_dc_t,
        "temp": dc_config.temp,
        "scale": dc_config.scale,
    }
    
    # Convert to OmegaConf and then to TrainPipelineConfig-like object
    omega_config = OmegaConf.create(config_dict)
    
    return omega_config


def main():
    parser = argparse.ArgumentParser(description="Train DiffusionModel_DC with lerobot data loading")
    
    # Dataset arguments
    parser.add_argument("--dataset", type=str, default="lerobot/pusht", 
                       help="Dataset repository ID")
    
    # Model arguments
    parser.add_argument("--pretrained_model", type=str, 
                       help="Path to pretrained DiffusionModel checkpoint")
    parser.add_argument("--n_dc_tokens", type=int, default=4,
                       help="Number of DC tokens per layer")
    parser.add_argument("--n_dc_layers", type=int, default=6,
                       help="Number of layers to apply DC tokens")
    parser.add_argument("--use_dc_t", action="store_true", default=True,
                       help="Use time-dependent DC tokens")
    
    # Training arguments
    parser.add_argument("--device", type=str, default="cuda", help="Device to use")
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--steps", type=int, default=10000, help="Training steps")
    parser.add_argument("--log_freq", type=int, default=100, help="Logging frequency")
    
    # Contrastive loss arguments
    parser.add_argument("--temp", type=float, default=0.07, help="Temperature for contrastive loss")
    parser.add_argument("--scale", type=float, default=4.0, help="Scale for contrastive loss")
    
    args = parser.parse_args()
    
    # Initialize logging
    init_logging()
    
    # Create DC training config
    dc_config = DCTrainingConfig(
        dataset_repo_id=args.dataset,
        pretrained_model_path=args.pretrained_model,
        n_dc_tokens=args.n_dc_tokens,
        n_dc_layers=args.n_dc_layers,
        use_dc_t=args.use_dc_t,
        device=args.device,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        steps=args.steps,
        log_freq=args.log_freq,
        temp=args.temp,
        scale=args.scale,
    )
    
    # Create lerobot-compatible config
    cfg = create_lerobot_config(dc_config)
    
    logging.info("Starting DiffusionModel_DC training with lerobot data loading")
    logging.info(f"Dataset: {dc_config.dataset_repo_id}")
    logging.info(f"Pretrained model: {dc_config.pretrained_model_path}")
    logging.info(f"DC tokens: {dc_config.n_dc_tokens} tokens, {dc_config.n_dc_layers} layers")
    logging.info(f"Training steps: {dc_config.steps}")
    
    # Train the model
    try:
        trained_model = train_dc_policy(
            cfg=cfg,
            dataset_name=dc_config.dataset_repo_id,
            pretrained_model_path=dc_config.pretrained_model_path
        )
        
        # Save the trained model
        save_path = Path("./checkpoints/dc_model_final.pth")
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        torch.save({
            'model_state_dict': trained_model.state_dict(),
            'config': dc_config,
        }, save_path)
        
        logging.info(f"Training completed! Model saved to {save_path}")
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        raise


if __name__ == "__main__":
    main()
