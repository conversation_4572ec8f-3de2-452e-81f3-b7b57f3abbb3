#!/usr/bin/env python

"""
Example script for training DiffusionModel_DC using a lerobot safetensors checkpoint.
This script demonstrates how to load a pretrained model from lerobot's checkpoint format.
"""

import argparse
import logging
import os
from pathlib import Path
import json
import torch

# Import lerobot components
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    create_diffusion_model_dc,
    DiffusionModel_DC
)
from lerobot.common.policies.diffusion.transformer_dc_training import (
    TransformerDCTrainingConfig,
    TransformerDCTrainer
)

# Try to import safetensors
try:
    from safetensors.torch import load_file
    HAS_SAFETENSORS = True
except ImportError:
    HAS_SAFETENSORS = False
    print("Warning: safetensors not installed. Install with: pip install safetensors")


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )


def load_lerobot_config(checkpoint_dir: str) -> dict:
    """
    Load lerobot config from checkpoint directory.
    
    Args:
        checkpoint_dir: Path to checkpoint directory (e.g., checkpoints/195000)
    
    Returns:
        Dictionary containing config
    """
    checkpoint_path = Path(checkpoint_dir)
    
    # Look for pretrained_model subdirectory
    if (checkpoint_path / "pretrained_model").exists():
        config_path = checkpoint_path / "pretrained_model" / "config.json"
    else:
        config_path = checkpoint_path / "config.json"
    
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    return config


def create_diffusion_config_from_lerobot(lerobot_config: dict) -> DiffusionConfig:
    """
    Create DiffusionConfig from lerobot config.
    
    Args:
        lerobot_config: Dictionary containing lerobot config
    
    Returns:
        DiffusionConfig for the model
    """
    # Extract action dimension from output features
    action_dim = lerobot_config["output_features"]["action"]["shape"][0]
    
    # Extract state dimension from input features
    state_dim = lerobot_config["input_features"]["observation.state"]["shape"][0]
    
    # Create dummy tensors for feature shapes
    action_feature = torch.zeros(action_dim)
    robot_state_feature = torch.zeros(state_dim)
    
    # Check if we have image features
    has_images = "observation.image" in lerobot_config["input_features"]
    image_features = ["observation.image"] if has_images else None
    
    # Create DiffusionConfig
    config = DiffusionConfig(
        horizon=16,  # Default horizon
        action_feature=action_feature,
        robot_state_feature=robot_state_feature,
        image_features=image_features,
        diffusion_step_embed_dim=256,
        n_layer=6,
        n_head=8,
        n_obs_steps=lerobot_config.get("n_obs_steps", 1),
        n_cond_layers=4,
        use_transformer=True,
        causal_attn=True,
        p_drop_emb=0.1,
        p_drop_attn=0.1,
    )
    
    return config


def main():
    parser = argparse.ArgumentParser(description="Train DiffusionModel_DC from lerobot checkpoint")
    
    # Checkpoint arguments
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="Path to lerobot checkpoint directory (e.g., checkpoints/195000)")
    
    # DC token arguments
    parser.add_argument("--n_dc_tokens", type=int, default=4,
                       help="Number of DC tokens per layer")
    parser.add_argument("--n_dc_layers", type=int, default=6,
                       help="Number of layers to apply DC tokens")
    parser.add_argument("--use_dc_t", action="store_true", default=True,
                       help="Use time-dependent DC tokens")
    
    # Training arguments
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--epochs", type=int, default=20, help="Number of epochs")
    parser.add_argument("--device", type=str, default="cuda", help="Device to use")
    parser.add_argument("--output_dir", type=str, default="./dc_checkpoints", 
                       help="Directory to save checkpoints")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Check if safetensors is installed
    if not HAS_SAFETENSORS:
        logging.error("safetensors is required to load lerobot checkpoints")
        logging.error("Install with: pip install safetensors")
        return
    
    # Load lerobot config
    try:
        lerobot_config = load_lerobot_config(args.checkpoint)
        logging.info(f"Loaded lerobot config: {lerobot_config.get('type', 'unknown')} policy")
    except Exception as e:
        logging.error(f"Failed to load lerobot config: {e}")
        return
    
    # Create DiffusionConfig
    try:
        diffusion_config = create_diffusion_config_from_lerobot(lerobot_config)
        logging.info(f"Created DiffusionConfig with action dim: {diffusion_config.action_feature.shape[0]}")
    except Exception as e:
        logging.error(f"Failed to create DiffusionConfig: {e}")
        return
    
    # Create training config
    training_config = TransformerDCTrainingConfig()
    training_config.n_dc_tokens = args.n_dc_tokens
    training_config.n_dc_layers = args.n_dc_layers
    training_config.use_dc_t = args.use_dc_t
    training_config.batch_size = args.batch_size
    training_config.learning_rate = args.learning_rate
    training_config.epochs = args.epochs
    training_config.device = args.device
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Create trainer
    trainer = TransformerDCTrainer(
        diffusion_config=diffusion_config,
        cond_dim=diffusion_config.robot_state_feature.shape[0],
        training_config=training_config,
        save_dir=args.output_dir
    )
    
    # Load pretrained model
    try:
        # Replace trainer's diffusion model with one loaded from checkpoint
        trainer.diffusion_model = create_diffusion_model_dc(
            config=diffusion_config,
            n_dc_tokens=args.n_dc_tokens,
            n_dc_layers=args.n_dc_layers,
            use_dc_t=args.use_dc_t,
            pretrained_model_path=args.checkpoint
        )
        
        # Move to device
        trainer.diffusion_model.to(args.device)
        
        # Freeze base model and get DC parameters
        trainer.diffusion_model.freeze_base_model()
        
        # Print parameter statistics
        trainer.diffusion_model.print_parameter_stats()
        
        logging.info("Successfully loaded pretrained model from lerobot checkpoint")
    except Exception as e:
        logging.error(f"Failed to load pretrained model: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Now you can train the model with your dataset
    logging.info("Model loaded successfully! Ready for training.")
    logging.info("To train, create a dataset and call trainer.train(train_dataloader, val_dataloader)")


if __name__ == "__main__":
    main()
