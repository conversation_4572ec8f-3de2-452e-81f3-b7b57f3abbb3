"""
Example usage of TransformerForDiffusion_DC with SoftREPA-style contrastive learning.
This script demonstrates how to train and use the DC-enhanced transformer for robot action diffusion.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from pathlib import Path
import argparse
import logging
from typing import Tuple

# Add the lerobot path
import sys
sys.path.append('/home/<USER>/work/lerobot-add_transformer')

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    create_transformer_dc_sampler,
    TransformerDCInference
)
from lerobot.common.policies.diffusion.transformer_dc_training import (
    TransformerDCTrainingConfig,
    create_trainer
)


def create_dummy_dataset(
    num_samples: int = 1000,
    horizon: int = 16,
    action_dim: int = 7,
    obs_dim: int = 128,
    device: str = 'cuda'
) -> <PERSON><PERSON>[<PERSON><PERSON>oa<PERSON>, DataLoader]:
    """
    Create dummy dataset for demonstration.
    
    Args:
        num_samples: Number of samples to generate
        horizon: Action sequence length
        action_dim: Action dimensionality
        obs_dim: Observation dimensionality
        device: Device to create data on
        
    Returns:
        Tuple of (train_dataloader, val_dataloader)
    """
    # Generate synthetic action sequences
    actions = torch.randn(num_samples, horizon, action_dim, device=device)
    
    # Generate synthetic observations (correlated with actions for better learning)
    observations = torch.randn(num_samples, obs_dim, device=device)
    
    # Add some correlation between actions and observations
    for i in range(min(action_dim, obs_dim // 16)):
        observations[:, i*16:(i+1)*16] += actions[:, 0, i].unsqueeze(1) * 0.5
    
    # Create datasets
    train_size = int(0.8 * num_samples)
    train_dataset = TensorDataset(actions[:train_size], observations[:train_size])
    val_dataset = TensorDataset(actions[train_size:], observations[train_size:])
    
    train_dataloader = DataLoader(
        train_dataset, 
        batch_size=32, 
        shuffle=True,
        num_workers=0  # Set to 0 for CUDA tensors
    )
    val_dataloader = DataLoader(
        val_dataset, 
        batch_size=32, 
        shuffle=False,
        num_workers=0
    )
    
    return train_dataloader, val_dataloader


def create_batch_dict(actions: torch.Tensor, observations: torch.Tensor) -> dict:
    """Convert tensors to batch dictionary format expected by trainer"""
    return {
        'action': actions,
        'observation': observations
    }


class DummyDataLoader:
    """Wrapper to convert TensorDataset to expected format"""
    def __init__(self, dataloader: DataLoader):
        self.dataloader = dataloader
    
    def __iter__(self):
        for actions, observations in self.dataloader:
            yield create_batch_dict(actions, observations)
    
    def __len__(self):
        return len(self.dataloader)


def train_transformer_dc(args):
    """Train TransformerForDiffusion_DC with contrastive learning"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Model configuration
    diffusion_config = DiffusionConfig(
        horizon=args.horizon,
        action_feature=torch.zeros(args.action_dim),  # Dummy feature for shape
        diffusion_step_embed_dim=256,
        n_layer=6,
        n_head=8,
        n_obs_steps=1,
        n_cond_layers=4,
        causal_attn=True,
        p_drop_emb=0.1,
        p_drop_attn=0.1
    )
    
    # Training configuration
    training_config = TransformerDCTrainingConfig()
    training_config.batch_size = args.batch_size
    training_config.learning_rate = args.learning_rate
    training_config.epochs = args.epochs
    training_config.n_dc_tokens = args.n_dc_tokens
    training_config.n_dc_layers = args.n_dc_layers
    training_config.use_dc_t = args.use_dc_t
    training_config.device = args.device
    training_config.use_wandb = args.use_wandb
    training_config.pretrained_model_path = args.pretrained_model
    
    logger.info(f"Training configuration:")
    logger.info(f"  Horizon: {args.horizon}")
    logger.info(f"  Action dim: {args.action_dim}")
    logger.info(f"  Obs dim: {args.obs_dim}")
    logger.info(f"  DC tokens: {args.n_dc_tokens}")
    logger.info(f"  DC layers: {args.n_dc_layers}")
    logger.info(f"  Use DC-t: {args.use_dc_t}")
    logger.info(f"  Device: {args.device}")
    
    # Create dataset
    logger.info("Creating dataset...")
    train_dataloader, val_dataloader = create_dummy_dataset(
        num_samples=args.num_samples,
        horizon=args.horizon,
        action_dim=args.action_dim,
        obs_dim=args.obs_dim,
        device=args.device
    )
    
    # Wrap dataloaders
    train_dataloader = DummyDataLoader(train_dataloader)
    val_dataloader = DummyDataLoader(val_dataloader)
    
    # Create trainer
    logger.info("Creating trainer...")
    trainer = create_trainer(
        diffusion_config=diffusion_config,
        cond_dim=args.obs_dim,
        training_config=training_config,
        save_dir=args.save_dir
    )
    
    # Train model
    logger.info("Starting training...")
    trainer.train(train_dataloader, val_dataloader)
    
    logger.info("Training completed!")
    
    return trainer


def test_inference(trainer, args):
    """Test inference with trained model"""
    logger = logging.getLogger(__name__)
    logger.info("Testing inference...")
    
    # Create test conditions
    test_conditions = torch.randn(4, args.obs_dim, device=args.device)
    
    # Test sampling without DC tokens
    logger.info("Sampling without DC tokens...")
    actions_no_dc = trainer.inference.sample(
        conditions=test_conditions,
        action_shape=(args.horizon, args.action_dim),
        use_dc=False
    )
    
    # Test sampling with DC tokens
    logger.info("Sampling with DC tokens...")
    actions_with_dc = trainer.inference.sample(
        conditions=test_conditions,
        action_shape=(args.horizon, args.action_dim),
        use_dc=True
    )
    
    # Compare results
    logger.info(f"Actions without DC - shape: {actions_no_dc.shape}, mean: {actions_no_dc.mean():.4f}, std: {actions_no_dc.std():.4f}")
    logger.info(f"Actions with DC - shape: {actions_with_dc.shape}, mean: {actions_with_dc.mean():.4f}, std: {actions_with_dc.std():.4f}")
    
    # Test guided sampling
    logger.info("Testing guided sampling...")
    actions_guided = trainer.inference.sample_with_guidance(
        conditions=test_conditions,
        action_shape=(args.horizon, args.action_dim),
        guidance_scale=2.0,
        use_dc=True
    )
    
    logger.info(f"Guided actions - shape: {actions_guided.shape}, mean: {actions_guided.mean():.4f}, std: {actions_guided.std():.4f}")
    
    return actions_no_dc, actions_with_dc, actions_guided


def main():
    parser = argparse.ArgumentParser(description="TransformerForDiffusion_DC Example")
    
    # Model parameters
    parser.add_argument('--horizon', type=int, default=16, help='Action sequence length')
    parser.add_argument('--action_dim', type=int, default=7, help='Action dimensionality')
    parser.add_argument('--obs_dim', type=int, default=128, help='Observation dimensionality')
    
    # DC parameters
    parser.add_argument('--n_dc_tokens', type=int, default=4, help='Number of DC tokens per layer')
    parser.add_argument('--n_dc_layers', type=int, default=6, help='Number of layers with DC tokens')
    parser.add_argument('--use_dc_t', action='store_true', help='Use time-dependent DC tokens')
    
    # Training parameters
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--epochs', type=int, default=20, help='Number of epochs')
    parser.add_argument('--num_samples', type=int, default=1000, help='Number of training samples')
    parser.add_argument('--pretrained_model', type=str, help='Path to pretrained DiffusionModel checkpoint')
    
    # System parameters
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--save_dir', type=str, default='./checkpoints', help='Save directory')
    parser.add_argument('--use_wandb', action='store_true', help='Use Weights & Biases logging')
    
    # Mode
    parser.add_argument('--mode', type=str, choices=['train', 'inference', 'both'], 
                       default='both', help='Mode to run')
    parser.add_argument('--checkpoint', type=str, help='Checkpoint path for inference')
    
    args = parser.parse_args()
    
    # Set device
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    if args.mode in ['train', 'both']:
        # Train model
        trainer = train_transformer_dc(args)
        
        if args.mode == 'both':
            # Test inference
            test_inference(trainer, args)
    
    elif args.mode == 'inference':
        if args.checkpoint is None:
            raise ValueError("Checkpoint path required for inference mode")
        
        # Load model for inference
        logger = logging.getLogger(__name__)
        logger.info(f"Loading checkpoint from {args.checkpoint}")
        
        # Create trainer and load checkpoint
        diffusion_config = DiffusionConfig(
            horizon=args.horizon,
            action_feature=torch.zeros(args.action_dim),
            diffusion_step_embed_dim=256,
            n_layer=6,
            n_head=8,
            n_obs_steps=1,
            n_cond_layers=4,
            causal_attn=True,
            p_drop_emb=0.1,
            p_drop_attn=0.1
        )
        
        training_config = TransformerDCTrainingConfig()
        training_config.device = args.device
        
        trainer = create_trainer(
            diffusion_config=diffusion_config,
            cond_dim=args.obs_dim,
            training_config=training_config,
            save_dir=args.save_dir
        )
        
        trainer.load_checkpoint(args.checkpoint)
        
        # Test inference
        test_inference(trainer, args)


if __name__ == "__main__":
    main()
