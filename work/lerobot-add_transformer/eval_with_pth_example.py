#!/usr/bin/env python3
"""
使用 .pth 权重文件进行模型评估的示例

这个脚本展示如何使用修改后的 eval.py 来加载 .pth 格式的预训练权重
"""

import os
import sys
from pathlib import Path

def example_eval_with_pth():
    """
    展示如何使用 .pth 权重文件进行评估
    """
    
    print("🚀 使用 .pth 权重文件进行模型评估示例")
    print("=" * 50)
    
    # 示例1: 直接指定 .pth 文件路径
    print("\n📁 示例1: 直接指定 .pth 文件路径")
    print("-" * 30)
    
    pth_file_path = "path/to/your/model.pth"
    config_dir = "path/to/config/directory"  # 包含 config.json 的目录
    
    command1 = f"""
python lerobot/scripts/eval.py \\
    --policy.path={pth_file_path} \\
    --env.type=pusht \\
    --eval.batch_size=10 \\
    --eval.n_episodes=10 \\
    --use_amp=false \\
    --device=cuda
"""
    
    print(f"命令: {command1}")
    print("说明: 当 policy.path 直接指向 .pth 文件时，脚本会自动在同目录下查找 config.json")
    
    # 示例2: 指定包含 .pth 文件的目录
    print("\n📁 示例2: 指定包含 .pth 文件的目录")
    print("-" * 30)
    
    model_dir = "path/to/model/directory"  # 包含 model.pth 和 config.json 的目录
    
    command2 = f"""
python lerobot/scripts/eval.py \\
    --policy.path={model_dir} \\
    --env.type=pusht \\
    --eval.batch_size=10 \\
    --eval.n_episodes=10 \\
    --use_amp=false \\
    --device=cuda
"""
    
    print(f"命令: {command2}")
    print("说明: 脚本会在指定目录中查找 model.pth, pytorch_model.pth, weights.pth 等文件")
    
    # 示例3: 使用预训练模型目录的 .pth 文件
    print("\n📁 示例3: 使用预训练模型目录的 .pth 文件")
    print("-" * 30)
    
    pretrained_dir = "use_pretrained_model/saved_models"
    
    command3 = f"""
python lerobot/scripts/eval.py \\
    --policy.path={pretrained_dir} \\
    --env.type=pusht \\
    --eval.batch_size=5 \\
    --eval.n_episodes=5 \\
    --use_amp=false \\
    --device=cuda
"""
    
    print(f"命令: {command3}")
    print("说明: 使用 use_pretrained_model 目录中保存的模型进行评估")
    
    # 支持的 .pth 文件命名
    print("\n📋 支持的 .pth 文件命名:")
    print("-" * 30)
    supported_names = [
        "model.pth",
        "pytorch_model.pth", 
        "weights.pth"
    ]
    
    for name in supported_names:
        print(f"  ✅ {name}")
    
    # 目录结构示例
    print("\n📂 推荐的目录结构:")
    print("-" * 30)
    
    structure = """
model_directory/
├── config.json          # 模型配置文件 (必需)
├── model.pth            # PyTorch 权重文件
└── other_files...       # 其他文件
    """
    
    print(structure)
    
    # 注意事项
    print("\n⚠️  注意事项:")
    print("-" * 30)
    
    notes = [
        "确保 config.json 文件存在且包含正确的模型配置",
        ".pth 文件应包含模型的 state_dict",
        "如果 .pth 文件包含嵌套结构 (如 {'model': state_dict})，脚本会自动提取",
        "权重加载使用 strict=False，允许部分权重不匹配",
        "如果 .pth 加载失败，会自动回退到标准的 .safetensors 加载方式"
    ]
    
    for i, note in enumerate(notes, 1):
        print(f"  {i}. {note}")
    
    print("\n🎯 总结:")
    print("-" * 30)
    print("修改后的 eval.py 现在支持:")
    print("  • 自动检测 .pth 权重文件")
    print("  • 多种 .pth 文件命名格式")
    print("  • 灵活的目录结构")
    print("  • 错误处理和回退机制")
    print("  • 与现有 .safetensors 格式完全兼容")


def create_test_structure():
    """
    创建测试用的目录结构
    """
    print("\n🔧 创建测试目录结构...")
    
    test_dir = Path("test_pth_eval")
    test_dir.mkdir(exist_ok=True)
    
    # 创建示例配置文件
    config_content = """{
    "type": "diffusion",
    "horizon": 16,
    "n_obs_steps": 2,
    "use_transformer": true,
    "diffusion_step_embed_dim": 1024,
    "n_layer": 12,
    "n_head": 16
}"""
    
    with open(test_dir / "config.json", "w") as f:
        f.write(config_content)
    
    print(f"✅ 测试目录创建完成: {test_dir}")
    print("   - config.json (示例配置)")
    print("   - 请将您的 .pth 文件放入此目录")
    
    return test_dir


if __name__ == "__main__":
    example_eval_with_pth()
    
    # 询问是否创建测试目录
    response = input("\n是否创建测试目录结构? (y/n): ")
    if response.lower() in ['y', 'yes']:
        create_test_structure() 