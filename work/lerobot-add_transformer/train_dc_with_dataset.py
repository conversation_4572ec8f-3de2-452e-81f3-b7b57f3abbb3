#!/usr/bin/env python

"""
Complete SoftREPA training script using lerobot dataset and safetensors checkpoint.
This script demonstrates the full workflow from checkpoint loading to DC token training.
"""

import argparse
import logging
import sys
import os
import json
from pathlib import Path
from datetime import datetime

import time

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
from safetensors.torch import load_file

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.envs.factory import make_env
from lerobot.common.policies.factory import make_policy

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss,
)
from lerobot.common.utils.utils import get_safe_torch_device

# Import evaluation utilities
from lerobot.scripts.eval import eval_policy
from lerobot.scripts.load_pretrained_dc import load_dc_weights_from_checkpoint


def setup_logging(output_dir: str):
    """Setup logging configuration with output to specified directory"""
    # Create log file in the output directory
    log_file = Path(output_dir) / "training.log"

    # 确保输出目录存在
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # 获取根logger
    root_logger = logging.getLogger()

    # 清除任何现有的日志处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 设置日志级别
    root_logger.setLevel(logging.INFO)

    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)

    # 测试日志文件是否可写
    try:
        logging.info(f"日志文件保存到: {log_file}")
        logging.info(f"日志文件路径存在: {log_file.exists()}")
        logging.info(f"日志文件可写: {log_file.parent.exists() and os.access(log_file.parent, os.W_OK)}")

        # 强制刷新到文件
        file_handler.flush()
        console_handler.flush()

    except Exception as e:
        print(f"日志设置错误: {e}")  # 使用print作为后备


def flush_logs():
    """强制刷新所有日志处理器"""
    for handler in logging.getLogger().handlers:
        if hasattr(handler, 'flush'):
            handler.flush()


def get_dc_tokens_summary(model):
    """获取DC tokens的详细摘要信息，参考SoftREPA的实现"""
    summary = []
    summary.append("🔍 DC Tokens详细信息:")

    for name, param in model.named_parameters():
        # 检查是否是DC相关参数
        is_dc_param = (
            'dc_token' in name or 'dc' in name.lower() or
            (hasattr(model.net, 'dc_tokens') and param is model.net.dc_tokens) or
            (hasattr(model.net, 'dc_t_tokens') and param is model.net.dc_t_tokens.weight)
        )

        if is_dc_param:
            summary.append(f"  {name}:")
            summary.append(f"    形状: {param.shape}")
            summary.append(f"    参数量: {param.numel():,}")
            summary.append(f"    数据类型: {param.dtype}")
            summary.append(f"    设备: {param.device}")
            summary.append(f"    需要梯度: {param.requires_grad}")
            if param.requires_grad:
                summary.append(f"    当前范数: {torch.norm(param.data).item():.6f}")
                summary.append(f"    均值: {param.data.mean().item():.6f}")
                summary.append(f"    标准差: {param.data.std().item():.6f}")

    return "\n".join(summary)


def create_training_directory(base_dir: str = "/home/<USER>/work/lerobot-add_transformer/dc_checkpoints") -> str:
    """创建新的训练目录，基于时间戳"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    training_dir = Path(base_dir) / f"training_{timestamp}"
    training_dir.mkdir(parents=True, exist_ok=True)
    return str(training_dir)


def save_dc_tokens_only(model: DiffusionModel_DC, save_path: str, step: int, loss: float, accuracy: float):
    """只保存DC tokens和相关参数到指定路径"""
    dc_state = {}
    
    # 提取DC相关的参数
    for name, param in model.named_parameters():
        # 检查是否是DC相关参数
        if ('dc_token' in name.lower() or 
            (hasattr(model.net, 'dc_tokens') and param is model.net.dc_tokens) or
            (hasattr(model.net, 'dc_t_tokens') and param is model.net.dc_t_tokens.weight)):
            dc_state[name] = param.data.clone()
    
    # 保存DC tokens的配置信息
    dc_config = {
        'n_dc_tokens': getattr(model.net, 'n_dc_tokens', 4),
        'n_dc_layers': getattr(model.net, 'n_dc_layers', 6),
        'use_dc_t': getattr(model.net, 'use_dc_t', True),
        'hidden_dim': getattr(model.net, 'hidden_dim', 256),
    }
    
    # 保存训练信息
    training_info = {
        'step': step,
        'loss': loss,
        'accuracy': accuracy,
        'timestamp': datetime.now().isoformat(),
    }
    
    checkpoint = {
        'dc_tokens': dc_state,
        'dc_config': dc_config,
        'training_info': training_info,
    }
    
    torch.save(checkpoint, save_path)
    logging.info(f"DC tokens 已保存到: {save_path}")
    logging.info(f"保存的DC参数数量: {len(dc_state)}")
    for name, param in dc_state.items():
        logging.info(f"  {name}: {param.shape}")


def load_dc_tokens(checkpoint_path: str):
    """加载DC tokens检查点"""
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    logging.info(f"从 {checkpoint_path} 加载DC tokens")
    logging.info(f"训练信息: {checkpoint.get('training_info', {})}")
    logging.info(f"DC配置: {checkpoint.get('dc_config', {})}")
    
    dc_tokens = checkpoint.get('dc_tokens', {})
    logging.info(f"加载的DC参数数量: {len(dc_tokens)}")
    for name, param in dc_tokens.items():
        logging.info(f"  {name}: {param.shape}")
    
    return checkpoint


def find_latest_checkpoint(checkpoint_dir: str) -> str:
    """
    在指定目录中找到最新的checkpoint文件
    
    Args:
        checkpoint_dir: 检查点目录路径
        
    Returns:
        最新checkpoint文件的路径，如果没找到返回None
    """
    checkpoint_path = Path(checkpoint_dir)
    if not checkpoint_path.exists():
        logging.warning(f"检查点目录不存在: {checkpoint_dir}")
        return None
    
    # 查找所有.pth文件
    pth_files = list(checkpoint_path.glob("*.pth"))
    if not pth_files:
        logging.warning(f"在目录 {checkpoint_dir} 中未找到.pth文件")
        return None
    
    # 按修改时间排序，获取最新的
    latest_file = max(pth_files, key=lambda x: x.stat().st_mtime)
    
    logging.info(f"找到最新的checkpoint: {latest_file}")
    return str(latest_file)


def load_training_checkpoint(checkpoint_path: str):
    """
    加载完整的训练checkpoint，包括模型状态、优化器状态等
    
    Args:
        checkpoint_path: checkpoint文件路径
        
    Returns:
        checkpoint字典
    """
    if not Path(checkpoint_path).exists():
        raise FileNotFoundError(f"Checkpoint文件不存在: {checkpoint_path}")
    
    logging.info(f"🔄 加载训练checkpoint: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 检查checkpoint内容
    required_keys = ['step', 'model_state_dict']
    missing_keys = [key for key in required_keys if key not in checkpoint]
    if missing_keys:
        raise ValueError(f"Checkpoint缺少必要的键: {missing_keys}")
    
    logging.info(f"✅ Checkpoint加载成功")
    logging.info(f"  训练步数: {checkpoint.get('step', 'Unknown')}")
    logging.info(f"  损失: {checkpoint.get('loss', 'Unknown')}")
    logging.info(f"  准确率: {checkpoint.get('accuracy', 'Unknown')}")
    logging.info(f"  时间戳: {checkpoint.get('timestamp', 'Unknown')}")
    
    if 'eval_success_rate' in checkpoint:
        logging.info(f"  评估成功率: {checkpoint['eval_success_rate']:.2f}%")
    if 'eval_avg_reward' in checkpoint:
        logging.info(f"  评估平均奖励: {checkpoint['eval_avg_reward']:.3f}")
    
    return checkpoint


def resume_training_state(diffusion_model, optimizer, scaler, lr_scheduler, checkpoint):
    """
    恢复训练状态

    Args:
        diffusion_model: DiffusionModel_DC实例
        optimizer: 优化器
        scaler: 梯度缩放器
        lr_scheduler: 学习率调度器
        checkpoint: checkpoint字典

    Returns:
        恢复的步数
    """
    # 加载模型状态
    logging.info("🔄 恢复模型状态...")
    diffusion_model.load_state_dict(checkpoint['model_state_dict'])
    logging.info("✅ 模型状态恢复成功")
    
    # 加载优化器状态（如果存在）
    if 'optimizer_state_dict' in checkpoint:
        logging.info("🔄 恢复优化器状态...")
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        logging.info("✅ 优化器状态恢复成功")
    else:
        logging.warning("⚠️ Checkpoint中没有优化器状态，将使用新的优化器状态")
    
    # 加载梯度缩放器状态（如果存在）
    if 'scaler_state_dict' in checkpoint:
        logging.info("🔄 恢复梯度缩放器状态...")
        scaler.load_state_dict(checkpoint['scaler_state_dict'])
        logging.info("✅ 梯度缩放器状态恢复成功")
    else:
        logging.warning("⚠️ Checkpoint中没有梯度缩放器状态，将使用新的缩放器状态")

    # 加载学习率调度器状态（如果存在）
    if 'lr_scheduler_state_dict' in checkpoint:
        logging.info("🔄 恢复学习率调度器状态...")
        lr_scheduler.load_state_dict(checkpoint['lr_scheduler_state_dict'])
        logging.info("✅ 学习率调度器状态恢复成功")
    else:
        logging.warning("⚠️ Checkpoint中没有学习率调度器状态，将使用新的调度器状态")

    # 返回恢复的步数
    resume_step = checkpoint.get('step', 0)
    logging.info(f"🚀 将从第 {resume_step} 步继续训练")

    return resume_step


def create_dataset_config(dataset_repo_id: str):
    """
    简化的数据集配置创建函数

    Args:
        dataset_repo_id: Dataset repository ID (e.g., "lerobot/pusht")

    Returns:
        简单的配置对象
    """
    # 创建简单的配置对象
    class SimpleConfig:
        def __init__(self, repo_id):
            self.dataset = SimpleDatasetConfig(repo_id)
            self.policy = SimplePolicyConfig()
            self.training = True

    class SimpleDatasetConfig:
        def __init__(self, repo_id):
            self.repo_id = repo_id
            self.root = None
            self.revision = None

    class SimplePolicyConfig:
        def __init__(self):
            self.n_obs_steps = 2
            self.n_action_steps = 16
            self.horizon = 16

    return SimpleConfig(dataset_repo_id)


def create_eval_config(checkpoint_path: str, env_type: str = "pusht"):
    """
    创建评估配置对象
    
    Args:
        checkpoint_path: Path to checkpoint directory
        env_type: Environment type for evaluation
        
    Returns:
        配置对象
    """
    class EvalConfig:
        def __init__(self, checkpoint_path, env_type):
            self.policy = PolicyConfig(checkpoint_path)
            self.env = EnvConfig(env_type)
            self.eval = EvalSubConfig()
            self.seed = 1000  # 按照eval.py的默认配置设置
            self.output_dir = str(Path(checkpoint_path).parent / "eval_results")
    
    class PolicyConfig:
        def __init__(self, checkpoint_path):
            self.path = checkpoint_path  # eval.py中使用path而不是pretrained_path
            self.pretrained_path = checkpoint_path  # 也添加这个以保持兼容性
            self.use_dc = False  # 初始设为False，稍后会修改
            self.device = "cuda"
            self.use_amp = False
            # 添加make_policy需要的type属性
            self.type = "diffusion"  # 根据我们的模型类型设置
    
    class EnvConfig:
        def __init__(self, env_type):
            # 导入需要的类型
            from lerobot.configs.types import FeatureType, PolicyFeature
            
            self.type = env_type
            # 根据make_env的要求添加task属性
            if env_type == "pusht":
                self.task = "PushT-v0"
                # 添加 PushT 环境的 features 配置
                self.features = {
                    "action": PolicyFeature(type=FeatureType.ACTION, shape=(2,)),
                    "agent_pos": PolicyFeature(type=FeatureType.STATE, shape=(2,)),
                    "pixels": PolicyFeature(type=FeatureType.VISUAL, shape=(384, 384, 3))
                }
                # 添加 features_map
                from lerobot.common.constants import ACTION, OBS_ROBOT, OBS_ENV, OBS_IMAGE
                self.features_map = {
                    "action": ACTION,
                    "agent_pos": OBS_ROBOT,
                    "environment_state": OBS_ENV,
                    "pixels": OBS_IMAGE,
                }
                # 添加其他PushT相关属性
                self.fps = 10
                self.episode_length = 300
                self.obs_type = "pixels_agent_pos"
                self.render_mode = "rgb_array"
                self.visualization_width = 384
                self.visualization_height = 384
            else:
                self.task = f"{env_type}-v0"
                # 基础特征配置
                self.features = {
                    "action": PolicyFeature(type=FeatureType.ACTION, shape=(7,)),  # 默认7自由度
                    "observation.state": PolicyFeature(type=FeatureType.STATE, shape=(7,)),
                }
                from lerobot.common.constants import ACTION, OBS_ROBOT
                self.features_map = {
                    "action": ACTION,
                    "observation.state": OBS_ROBOT,
                }
                self.fps = 30
                
            # 添加gym_kwargs属性（作为属性而不是方法）
            if env_type == "pusht":
                self.gym_kwargs = {
                    "obs_type": self.obs_type,
                    "render_mode": self.render_mode,
                    "visualization_width": self.visualization_width,
                    "visualization_height": self.visualization_height,
                    "max_episode_steps": self.episode_length,
                }
            else:
                self.gym_kwargs = {}
    
    class EvalSubConfig:
        def __init__(self):
            # 按照eval.py的默认配置设置
            self.batch_size = 100
            self.n_episodes = 100
            self.use_async_envs = False
    
    return EvalConfig(checkpoint_path, env_type)


# def load_dc_weights_to_policy(policy, pth_path: str):
#     """
#     从.pth文件加载DC权重到policy
    
#     Args:
#         policy: Policy instance
#         pth_path: Path to .pth file containing DC weights
#     """
#     try:
#         logging.info(f"🔄 从 {pth_path} 加载DC权重...")
        
#         # 加载.pth文件
#         checkpoint = torch.load(pth_path, map_location='cpu')
        
#         # 获取模型状态字典
#         if 'model_state_dict' in checkpoint:
#             state_dict = checkpoint['model_state_dict']
#         else:
#             state_dict = checkpoint
        
#         # 提取DC相关的权重
#         dc_weights = {}
#         for key, value in state_dict.items():
#             if 'dc_token' in key or 'dc_t_token' in key:
#                 dc_weights[key] = value
        
#         if not dc_weights:
#             logging.warning("⚠️ 在.pth文件中未找到DC权重")
#             return
        
#         # 加载到policy的diffusion模型中
#         missing_keys, unexpected_keys = policy.diffusion.load_state_dict(dc_weights, strict=False)
        
#         logging.info(f"✅ DC权重加载完成")
#         logging.info(f"  加载的DC权重数量: {len(dc_weights)}")
#         if missing_keys:
#             logging.info(f"  缺失的键: {len(missing_keys)}")
#         if unexpected_keys:
#             logging.info(f"  意外的键: {len(unexpected_keys)}")
            
#     except Exception as e:
#         logging.error(f"❌ 加载DC权重失败: {e}")
#         raise


def evaluate_baseline_model(checkpoint_path: str, env_type: str = "pusht"):
    """
    评估原版预训练模型作为baseline（不使用DC tokens）
    
    Args:
        checkpoint_path: Path to checkpoint directory
        env_type: Environment type
        
    Returns:
        评估结果字典
    """
    try:
        logging.info("🎯 开始评估原版预训练模型 (Baseline)")
        logging.info(f"📁 基础模型checkpoint路径: {checkpoint_path}")
        logging.info(f"🌍 环境类型: {env_type}")
        
        # 参考train.py的方式，直接从checkpoint加载配置
        from lerobot.configs.policies import PreTrainedConfig
        
        # 修复路径：PreTrainedConfig.from_pretrained需要pretrained_model目录的路径
        pretrained_model_path = str(Path(checkpoint_path) / "pretrained_model")
        logging.info(f"🔧 预训练模型路径: {pretrained_model_path}")
        
        # 检查pretrained_model目录是否存在
        if not Path(pretrained_model_path).exists():
            logging.error(f"❌ pretrained_model目录不存在: {pretrained_model_path}")
            raise FileNotFoundError(f"pretrained_model directory not found: {pretrained_model_path}")
        
        # 检查config.json是否存在
        config_file_path = Path(pretrained_model_path) / "config.json"
        if not config_file_path.exists():
            logging.error(f"❌ config.json文件不存在: {config_file_path}")
            raise FileNotFoundError(f"config.json not found: {config_file_path}")
        
        logging.info(f"📁 加载配置从: {pretrained_model_path}")
        
        # 加载预训练配置，确保不使用DC
        policy_cfg = PreTrainedConfig.from_pretrained(pretrained_model_path)
        policy_cfg.use_dc = False  # 明确禁用DC功能
        policy_cfg.pretrained_path = pretrained_model_path
        logging.info(f"✅ 策略配置加载成功，类型: {policy_cfg.type}, use_dc: {policy_cfg.use_dc}")
        
        # 创建评估配置
        eval_cfg = create_eval_config(checkpoint_path, env_type)
        logging.info(f"✅ 评估配置创建成功")
        
        # 按照eval.py设置torch后端
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        from lerobot.common.utils.random_utils import set_seed
        set_seed(eval_cfg.seed)

        # 创建环境
        logging.info(f"🏗️ 创建环境 (batch_size={eval_cfg.eval.batch_size})")
        env = make_env(eval_cfg.env, n_envs=eval_cfg.eval.batch_size, use_async_envs=eval_cfg.eval.use_async_envs)
        logging.info(f"✅ 环境创建成功")
        
        # 使用正确的配置创建原版policy（不使用DC）
        logging.info(f"🤖 创建原版策略...")
        policy = make_policy(
            cfg=policy_cfg,
            env_cfg=eval_cfg.env,  # 使用env_cfg而不是ds_meta，因为我们是在评估环境
        )
        policy.eval()
        logging.info(f"✅ 原版策略创建成功，设为评估模式")
        
        # 运行评估 - 按照eval.py的方式使用autocast
        logging.info(f"🚀 开始运行baseline评估 (n_episodes={eval_cfg.eval.n_episodes})")
        from contextlib import nullcontext
        device = get_safe_torch_device(policy_cfg.device, log=False)
        with torch.no_grad(), torch.autocast(device_type=device.type) if policy_cfg.use_amp else nullcontext():
            info = eval_policy(
                env,
                policy,
                eval_cfg.eval.n_episodes,
                max_episodes_rendered=0,  # 训练时不渲染视频
                videos_dir=None,
                start_seed=eval_cfg.seed,
            )
        
        env.close()
        logging.info(f"✅ Baseline评估运行完成，环境已关闭")
        
        # 按照eval.py的格式显示评估结果
        aggregated = info["aggregated"]
        logging.info("🏁 Baseline评估结果:")
        logging.info("Evaluation Results:")
        logging.info(f"  Average Sum Reward: {aggregated['avg_sum_reward']:.3f}")
        logging.info(f"  Average Max Reward: {aggregated['avg_max_reward']:.3f}")
        logging.info(f"  Success Rate: {aggregated['pc_success']:.2f}%")
        logging.info(f"  Evaluation Time: {aggregated['eval_s']:.1f}s")
        logging.info(f"  Time per Episode: {aggregated['eval_ep_s']:.1f}s")

        # 也输出原始字典以保持兼容性
        logging.info("\nRaw aggregated results:")
        logging.info(aggregated)
        logging.info("=" * 50)

        success_rate = aggregated["pc_success"]
        avg_reward = aggregated["avg_sum_reward"]
        
        return {
            "success_rate": success_rate,
            "avg_reward": avg_reward,
            "full_info": info,
            "model_type": "baseline"
        }
        
    except Exception as e:
        logging.error(f"❌ Baseline评估失败: {e}")
        logging.error(f"❌ 异常类型: {type(e).__name__}")
        import traceback
        error_traceback = traceback.format_exc()
        logging.error(f"❌ 完整错误堆栈:\n{error_traceback}")
        
        # 返回失败结果，但确保程序继续运行
        return {
            "success_rate": 0.0,
            "avg_reward": 0.0,
            "full_info": None,
            "error": str(e),
            "model_type": "baseline"
        }


def evaluate_model(diffusion_model: DiffusionModel_DC, checkpoint_path: str, pth_path: str, env_type: str = "pusht"):
    """
    评估模型性能
    
    Args:
        diffusion_model: 训练中的DiffusionModel_DC
        checkpoint_path: 基础模型checkpoint路径
        pth_path: 当前DC tokens的.pth文件路径
        env_type: 环境类型
        
    Returns:
        评估结果字典
    """
    try:
        logging.info(f"🔍 开始评估模型 (使用DC tokens: {pth_path})")
        logging.info(f"📁 基础模型checkpoint路径: {checkpoint_path}")
        logging.info(f"🌍 环境类型: {env_type}")
        
        # 参考train.py的方式，直接从checkpoint加载配置
        from lerobot.configs.policies import PreTrainedConfig
        
        # 修复路径：PreTrainedConfig.from_pretrained需要pretrained_model目录的路径
        pretrained_model_path = str(Path(checkpoint_path) / "pretrained_model")
        logging.info(f"🔧 预训练模型路径: {pretrained_model_path}")
        
        # 检查pretrained_model目录是否存在
        if not Path(pretrained_model_path).exists():
            logging.error(f"❌ pretrained_model目录不存在: {pretrained_model_path}")
            raise FileNotFoundError(f"pretrained_model directory not found: {pretrained_model_path}")
        
        # 检查config.json是否存在
        config_file_path = Path(pretrained_model_path) / "config.json"
        if not config_file_path.exists():
            logging.error(f"❌ config.json文件不存在: {config_file_path}")
            raise FileNotFoundError(f"config.json not found: {config_file_path}")
        
        logging.info(f"📁 加载配置从: {pretrained_model_path}")
        
        # 加载预训练配置
        policy_cfg = PreTrainedConfig.from_pretrained(pretrained_model_path)
        policy_cfg.use_dc = True  # 启用DC功能
        print(policy_cfg)
        policy_cfg.pretrained_path='/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000/pretrained_model'
        logging.info(f"✅ 策略配置加载成功，类型: {policy_cfg.type}")
        
        # 创建评估配置
        eval_cfg = create_eval_config(checkpoint_path, env_type)
        logging.info(f"✅ 评估配置创建成功")
        
        # 按照eval.py设置torch后端
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        from lerobot.common.utils.random_utils import set_seed
        set_seed(eval_cfg.seed)

        # 创建环境
        logging.info(f"🏗️ 创建环境 (batch_size={eval_cfg.eval.batch_size})")
        env = make_env(eval_cfg.env, n_envs=eval_cfg.eval.batch_size, use_async_envs=eval_cfg.eval.use_async_envs)
        logging.info(f"✅ 环境创建成功")
        
        # 使用正确的配置创建policy
        logging.info(f"🤖 创建策略...")
        policy = make_policy(
            cfg=policy_cfg,
            env_cfg=eval_cfg.env,  # 使用env_cfg而不是ds_meta，因为我们是在评估环境
            
        )
        logging.info(f"✅ 策略创建成功")
        
        # 加载DC权重
        logging.info(f"⚖️ 加载DC权重从: {pth_path}")
        load_dc_weights_from_checkpoint(policy.diffusion, pth_path)
        policy.eval()
        logging.info(f"✅ DC权重加载成功，策略设为评估模式")
        
        # 运行评估 - 按照eval.py的方式使用autocast
        logging.info(f"🚀 开始运行评估 (n_episodes={eval_cfg.eval.n_episodes})")
        from contextlib import nullcontext
        device = get_safe_torch_device(policy_cfg.device, log=False)
        with torch.no_grad(), torch.autocast(device_type=device.type) if policy_cfg.use_amp else nullcontext():
            info = eval_policy(
                env,
                policy,
                eval_cfg.eval.n_episodes,
                max_episodes_rendered=0,  # 训练时不渲染视频
                videos_dir=None,
                start_seed=eval_cfg.seed,
            )
        
        env.close()
        logging.info(f"✅ 评估运行完成，环境已关闭")
        
        # 按照eval.py的格式显示评估结果
        aggregated = info["aggregated"]
        logging.info("Evaluation Results:")
        logging.info(f"  Average Sum Reward: {aggregated['avg_sum_reward']:.3f}")
        logging.info(f"  Average Max Reward: {aggregated['avg_max_reward']:.3f}")
        logging.info(f"  Success Rate: {aggregated['pc_success']:.2f}%")
        logging.info(f"  Evaluation Time: {aggregated['eval_s']:.1f}s")
        logging.info(f"  Time per Episode: {aggregated['eval_ep_s']:.1f}s")

        # 也输出原始字典以保持兼容性
        logging.info("\nRaw aggregated results:")
        logging.info(aggregated)

        success_rate = aggregated["pc_success"]
        avg_reward = aggregated["avg_sum_reward"]
        
        return {
            "success_rate": success_rate,
            "avg_reward": avg_reward,
            "full_info": info
        }
        
    except Exception as e:
        logging.error(f"❌ 评估失败: {e}")
        logging.error(f"❌ 异常类型: {type(e).__name__}")
        import traceback
        error_traceback = traceback.format_exc()
        logging.error(f"❌ 完整错误堆栈:\n{error_traceback}")
        
        # 返回失败结果，但确保程序继续运行
        return {
            "success_rate": 0.0,
            "avg_reward": 0.0,
            "full_info": None,
            "error": str(e)
        }


def load_and_create_dc_model(checkpoint_path: str, n_dc_tokens: int = 4, n_dc_layers: int = 6):
    """
    简化的DiffusionModel_DC创建和加载函数

    Args:
        checkpoint_path: Path to checkpoint directory
        n_dc_tokens: Number of DC tokens per layer
        n_dc_layers: Number of layers to apply DC tokens

    Returns:
        DiffusionModel_DC instance
    """
    checkpoint_dir = Path(checkpoint_path)

    logging.info(f"🔄 Loading DiffusionModel_DC from checkpoint: {checkpoint_path}")

    # 1. 加载预训练模型配置
    config_path = checkpoint_dir / "pretrained_model" / "config.json"
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    # 使用PreTrainedConfig加载配置（与简化后的代码一致）
    from lerobot.configs.policies import PreTrainedConfig
    config = PreTrainedConfig.from_pretrained(str(config_path.parent))

    logging.info(f"✅ 配置加载成功: {config.type}")

    # 2. 使用简化的from_pretrained方法创建DiffusionModel_DC
    try:
        diffusion_model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=True
        )

        logging.info("✅ DiffusionModel_DC创建和加载成功!")

        # 3. 打印模型统计信息
        total_params = sum(p.numel() for p in diffusion_model.parameters())
        dc_params = sum(p.numel() for name, p in diffusion_model.named_parameters() if 'dc' in name.lower())
        base_params = total_params - dc_params

        logging.info(f"📊 模型参数统计:")
        logging.info(f"  总参数量: {total_params:,}")
        logging.info(f"  DC参数量: {dc_params:,} ({dc_params/total_params*100:.2f}%)")
        logging.info(f"  基础参数量: {base_params:,}")

        return diffusion_model

    except Exception as e:
        logging.error(f"❌ DiffusionModel_DC创建失败: {e}")
        raise


def train_dc_tokens(
    diffusion_model: DiffusionModel_DC,
    dataloader: DataLoader,
    device: torch.device,
    learning_rate: float = 1e-4,
    steps: int = 10000,
    log_freq: int = 100,
    save_freq: int = 5000,
    eval_freq: int = 5000,
    output_dir: str = "./dc_checkpoints",
    checkpoint_path: str = None,
    env_type: str = "pusht",
    args=None,
    resume_from: str = None
):
    """
    Train DC tokens using contrastive learning.
    
    Args:
        diffusion_model: DiffusionModel_DC instance
        dataloader: Training data loader
        device: Training device
        learning_rate: Learning rate for DC token training
        steps: Number of training steps
        log_freq: Logging frequency
        save_freq: Checkpoint saving frequency
        eval_freq: Evaluation frequency
        output_dir: Output directory for checkpoints
        checkpoint_path: Base model checkpoint path for evaluation
        env_type: Environment type for evaluation
    """
    # Move model to device
    diffusion_model.to(device)
    
    # Freeze base model and get DC parameters
    diffusion_model.freeze_base_model()
    dc_params = diffusion_model.get_dc_parameters()
    
    if not dc_params:
        raise ValueError("No DC parameters found! Check DC token setup.")
    
    logging.info(f"Training {len(dc_params)} DC parameters")
    diffusion_model.print_parameter_stats()
    
    # Create optimizer for DC parameters only - 参考SoftREPA的AdamW配置
    effective_lr = learning_rate
    logging.info(f"📈 使用学习率: {effective_lr:.0e}")

    # 使用AdamW优化器，参考SoftREPA的配置
    optimizer = torch.optim.AdamW(
        dc_params,
        lr=effective_lr,
        betas=(0.9, 0.999),
        eps=1e-8,
        weight_decay=1e-4  # 参考SoftREPA的weight_decay
    )
    logging.info(f"🔧 使用AdamW优化器，betas=(0.9, 0.999), eps=1e-8, weight_decay=1e-4")

    # 添加学习率调度器，参考SoftREPA的CosineAnnealingWarmRestarts
    lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,      # 参考SoftREPA的T_0=10
        T_mult=1,    # 参考SoftREPA的T_mult=1
        eta_min=1e-5 # 参考SoftREPA的eta_min=1e-5
    )
    logging.info(f"🔧 使用CosineAnnealingWarmRestarts调度器，T_0=10, T_mult=1, eta_min=1e-5")

    # Create contrastive learning components with adjusted parameters
    # 参考SoftREPA的对比损失配置
    adjusted_temp = 0.07  # 使用SoftREPA的默认温度参数
    adjusted_scale = 4.0  # 使用SoftREPA的默认scale参数
    contrastive_loss = ContrastiveLoss(
        temp=adjusted_temp,
        scale=adjusted_scale,
        device=device.type,
        dweight=0  # 参考SoftREPA的默认dweight=0
    )
    logging.info(f"🎯 对比损失参数: temp={adjusted_temp}, scale={adjusted_scale}, dweight=0")

    # 添加直接的DC token正则化损失
    def dc_regularization_loss(model, lambda_reg=0.01):
        """直接的DC token正则化，提供额外的梯度信号"""
        reg_loss = 0.0
        if hasattr(model.net, 'dc_tokens'):
            # L2正则化，鼓励DC tokens学习有意义的表示
            reg_loss += lambda_reg * torch.norm(model.net.dc_tokens, p=2)
        if hasattr(model.net, 'dc_t_tokens'):
            reg_loss += lambda_reg * torch.norm(model.net.dc_t_tokens.weight, p=2)
        return reg_loss

    # Create gradient scaler for mixed precision
    scaler = GradScaler()
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # 最佳模型追踪
    best_success_rate = -1.0
    best_avg_reward = float('-inf')
    best_model_path = None

    # 全局损失跟踪，参考SoftREPA
    global_loss = 0.0
    loss_accumulation_steps = 0
    
    # Resume training if specified
    start_step = 0
    if resume_from:
        logging.info("=" * 60)
        logging.info("🔄 恢复训练模式")
        logging.info("=" * 60)
        
        # Load checkpoint
        resume_checkpoint = load_training_checkpoint(resume_from)
        
        # Resume training state
        start_step = resume_training_state(diffusion_model, optimizer, scaler, lr_scheduler, resume_checkpoint)
        
        # Update best model tracking if available
        if 'eval_success_rate' in resume_checkpoint:
            best_success_rate = resume_checkpoint['eval_success_rate']
            logging.info(f"🏆 恢复最佳成功率: {best_success_rate:.2f}%")
        if 'eval_avg_reward' in resume_checkpoint:
            best_avg_reward = resume_checkpoint['eval_avg_reward']
            logging.info(f"🏆 恢复最佳平均奖励: {best_avg_reward:.3f}")
        
        logging.info("=" * 60)
        logging.info(f"🚀 从第 {start_step} 步继续训练，目标步数: {steps}")
        logging.info("=" * 60)
    else:
        logging.info("Starting DC token training from scratch...")
    
    # Adjust steps if resuming
    remaining_steps = steps - start_step
    if remaining_steps <= 0:
        logging.warning(f"⚠️ 已达到目标步数 {steps}，无需继续训练")
        return
    
    diffusion_model.train()
    
    # Create infinite data iterator
    from itertools import cycle
    data_iter = cycle(dataloader)
    
    # Create progress bar for remaining steps
    progress_bar = tqdm(range(remaining_steps), desc=f"Training DC tokens (from step {start_step})")

    # 初始化监控变量
    initial_dc_tokens = {}
    initial_base_params = {}

    # 记录初始DC tokens状态
    for name, param in diffusion_model.named_parameters():
        # 检查是否是DC相关参数
        is_dc_param = (
            'dc_token' in name or
            (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
            (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
        )

        if is_dc_param and param.requires_grad:
            initial_dc_tokens[name] = param.data.clone()
        elif param.requires_grad:
            logging.warning(f"⚠️ 发现未冻结的非DC参数: {name}")
        elif not is_dc_param:
            # 记录一些基础模型参数用于验证冻结状态
            if len(initial_base_params) < 5:  # 只记录前5个用于检查
                initial_base_params[name] = param.data.clone()

    logging.info(f"📊 初始DC tokens数量: {len(initial_dc_tokens)}")
    logging.info(f"📊 初始基础参数样本数量: {len(initial_base_params)}")

    # 输出DC tokens初始状态
    logging.info(get_dc_tokens_summary(diffusion_model))

    for step_idx in progress_bar:
        # Calculate actual step number (for resume support)
        actual_step = start_step + step_idx
        
        # Get batch
        batch = next(data_iter)

        # Debug: Print batch structure on first iteration
        if step_idx == 0:
            logging.info("Batch structure:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    logging.info(f"  {key}: {value.shape} {value.dtype}")
                else:
                    logging.info(f"  {key}: {type(value)}")

        # Move batch to device
        for key in batch:
            if isinstance(batch[key], torch.Tensor):
                batch[key] = batch[key].to(device, non_blocking=True)
        
        # Forward pass with mixed precision
        with autocast():
            # Compute contrastive error matrix using DiffusionModel_DC directly
            error_matrix = diffusion_model.compute_contrastive_error(batch, use_dc=True)

            # Compute contrastive loss
            contrastive_loss_value = contrastive_loss(error_matrix)

            # 添加DC token正则化损失，提供直接的梯度信号
            reg_loss = dc_regularization_loss(diffusion_model, lambda_reg=0.1)

            # 总损失
            loss = contrastive_loss_value + reg_loss
        
        # Backward pass
        optimizer.zero_grad()
        scaler.scale(loss).backward()


        # 不进行梯度裁剪，保留所有梯度信号
        scaler.unscale_(optimizer)
        # 注释：跳过梯度裁剪，保留完整梯度信号
        
        # Optimizer step
        scaler.step(optimizer)
        scaler.update()

        # 累积全局损失，参考SoftREPA
        global_loss += loss.item()
        loss_accumulation_steps += 1

        # 监控DC tokens和模型参数更新 - 参考SoftREPA的监控频率
        if (actual_step + 1) % 50 == 0 or (actual_step + 1) <= 10:  # 前10步和每50步检查一次
            with torch.no_grad():
                # 检查DC tokens是否更新
                dc_updates = {}
                for name, param in diffusion_model.named_parameters():
                    # 检查是否是DC相关参数
                    is_dc_param = (
                        'dc_token' in name or
                        (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
                        (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
                    )

                    if is_dc_param and param.requires_grad:
                        if name in initial_dc_tokens:
                            diff = torch.norm(param.data - initial_dc_tokens[name]).item()
                            dc_updates[name] = diff

                # 检查基础模型参数是否被意外更新
                base_updates = {}
                for name, param in diffusion_model.named_parameters():
                    if name in initial_base_params:
                        diff = torch.norm(param.data - initial_base_params[name]).item()
                        base_updates[name] = diff

                # 检查梯度
                dc_grad_norms = {}
                base_grad_norms = {}
                for name, param in diffusion_model.named_parameters():
                    if param.grad is not None:
                        grad_norm = torch.norm(param.grad).item()

                        # 检查是否是DC相关参数
                        is_dc_param = (
                            'dc_token' in name or
                            (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
                            (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
                        )

                        if is_dc_param:
                            dc_grad_norms[name] = grad_norm
                        else:
                            base_grad_norms[name] = grad_norm

        # Compute metrics
        with torch.no_grad():
            batch_size = error_matrix.shape[0]
            diagonal_errors = torch.diag(error_matrix)
            off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]

            # Compute accuracy: how many diagonal elements are smaller than their row mean
            row_means = error_matrix.mean(dim=1)  # Mean error for each row
            correct_predictions = (diagonal_errors < row_means).float()
            accuracy = correct_predictions.mean()
        
        # Update progress bar
        progress_bar.set_postfix({
            'total_loss': f"{loss.item():.4f}",
            'cont_loss': f"{contrastive_loss_value.item():.4f}",
            'reg_loss': f"{reg_loss.item():.4f}",
            'acc': f"{accuracy.item():.3f}",
            'diag': f"{diagonal_errors.mean().item():.4f}",
            'off_diag': f"{off_diagonal_errors.mean().item():.4f}",
            'best_success': f"{best_success_rate:.2f}%"
        })

        # 详细日志输出
        if (actual_step + 1) % log_freq == 0 or step_idx < 5:
            current_lr = optimizer.param_groups[0]['lr']
            logging.info(
                f"Step {actual_step+1}/{steps}: "
                f"TotalLoss={loss.item():.4f}, "
                f"ContrastiveLoss={contrastive_loss_value.item():.4f}, "
                f"RegLoss={reg_loss.item():.4f}, "
                f"Acc={accuracy.item():.3f}, "
                f"DiagErr={diagonal_errors.mean().item():.4f}, "
                f"OffDiagErr={off_diagonal_errors.mean().item():.4f}, "
                f"LR={current_lr:.2e}"  # 添加学习率监控
            )
            flush_logs()  # 强制刷新日志

            # 输出训练进度信息，参考SoftREPA的格式
            logging.info(f"📊 训练进度: Step {actual_step+1}/{steps}, Loss: {loss.item():.6f}")

            # 输出DC tokens更新情况
            if 'dc_updates' in locals():
                logging.info("🔍 DC Tokens更新情况:")
                for name, diff in dc_updates.items():
                    logging.info(f"  {name}: 变化量={diff:.6f}")

                if dc_grad_norms:
                    logging.info("📈 DC Tokens梯度范数:")
                    for name, grad_norm in dc_grad_norms.items():
                        logging.info(f"  {name}: 梯度范数={grad_norm:.10f}")  # 增加精度
                else:
                    logging.warning("⚠️ 没有检测到DC tokens的梯度!")

                # 检查基础模型是否被意外更新
                if base_updates:
                    max_base_update = max(base_updates.values())
                    if max_base_update > 1e-8:
                        logging.warning(f"⚠️ 基础模型参数可能未被正确冻结! 最大变化量: {max_base_update:.8f}")
                    else:
                        logging.info(f"✅ 基础模型参数正确冻结 (最大变化量: {max_base_update:.8f})")

                if base_grad_norms:
                    logging.warning("⚠️ 检测到基础模型参数的梯度:")
                    for name, grad_norm in list(base_grad_norms.items())[:3]:  # 只显示前3个
                        logging.warning(f"  {name}: 梯度范数={grad_norm:.6f}")
                else:
                    logging.info("✅ 基础模型参数没有梯度 (正确冻结)")

                # 检查损失变化，参考SoftREPA的实现
                if hasattr(progress_bar, 'last_loss'):
                    loss_change = loss.item() - progress_bar.last_loss
                    if loss_change < 0:
                        logging.info(f"✅ 损失下降: {loss_change:.6f}")
                    else:
                        logging.warning(f"⚠️ 损失上升: +{loss_change:.6f}")
                progress_bar.last_loss = loss.item()
        
        # Save checkpoint and evaluate
        if (actual_step + 1) % save_freq == 0:
            # 计算平均损失，参考SoftREPA
            avg_loss = global_loss / loss_accumulation_steps if loss_accumulation_steps > 0 else loss.item()
            logging.info(f"Step {actual_step+1}: 平均损失: {avg_loss:.4f}")

            # 保存当前模型
            checkpoint_path_step = Path(output_dir) / f"dc_checkpoint_step_{actual_step+1}.pth"
            torch.save({
                'step': actual_step + 1,
                'model_state_dict': diffusion_model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scaler_state_dict': scaler.state_dict(),
                'lr_scheduler_state_dict': lr_scheduler.state_dict(),  # 添加学习率调度器状态
                'loss': loss.item(),
                'avg_loss': avg_loss,  # 添加平均损失
                'accuracy': accuracy.item(),
                'timestamp': datetime.now().isoformat(),
            }, checkpoint_path_step)
            logging.info(f"完整模型检查点已保存: {checkpoint_path_step}")

            # 更新学习率调度器，参考SoftREPA在每个validation后调用
            lr_scheduler.step()
            current_lr = optimizer.param_groups[0]['lr']
            logging.info(f"📈 学习率调度器更新，当前学习率: {current_lr:.2e}")

            # 重置全局损失计数器，参考SoftREPA
            global_loss = 0.0
            loss_accumulation_steps = 0
        
        # Evaluate model
        if (actual_step + 1) % eval_freq == 0 and checkpoint_path is not None:
            logging.info(f"🎯 开始第 {actual_step+1} 步评估...")
            logging.info(f"📊 评估条件检查: actual_step+1={actual_step+1}, eval_freq={eval_freq}, checkpoint_path={checkpoint_path}")
            
            try:
                # 保存当前模型用于评估
                eval_checkpoint_path = Path(output_dir) / f"dc_checkpoint_step_{actual_step+1}.pth"
                logging.info(f"💾 保存评估用模型到: {eval_checkpoint_path}")
                
                torch.save({
                    'step': actual_step + 1,
                    'model_state_dict': diffusion_model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scaler_state_dict': scaler.state_dict(),
                    'lr_scheduler_state_dict': lr_scheduler.state_dict(),  # 添加学习率调度器状态
                    'loss': loss.item(),
                    'accuracy': accuracy.item(),
                    'timestamp': datetime.now().isoformat(),
                }, eval_checkpoint_path)
                logging.info(f"✅ 评估用模型保存成功")
                
                # 评估当前模型
                logging.info(f"🏃 调用评估函数...")
                eval_results = evaluate_model(
                    diffusion_model, 
                    checkpoint_path,
                    str(eval_checkpoint_path),
                    env_type
                )
                logging.info(f"✅ 评估函数调用完成")
                
                # 检查评估结果
                if "error" in eval_results:
                    logging.error(f"❌ 评估过程中发生错误: {eval_results['error']}")
                    current_success_rate = 0.0
                    current_avg_reward = 0.0
                else:
                    current_success_rate = eval_results['success_rate']
                    current_avg_reward = eval_results['avg_reward']
                
                logging.info(f"📊 第 {actual_step+1} 步评估结果:")
                logging.info(f"  成功率: {current_success_rate:.2f}%")
                logging.info(f"  平均奖励: {current_avg_reward:.3f}")
                flush_logs()  # 强制刷新日志
                
                # 检查是否是最佳模型
                is_best = False
                if current_success_rate > best_success_rate:
                    is_best = True
                    best_success_rate = current_success_rate
                    best_avg_reward = current_avg_reward
                elif current_success_rate == best_success_rate and current_avg_reward > best_avg_reward:
                    is_best = True
                    best_avg_reward = current_avg_reward
                
                if is_best:
                    # 更新最佳模型
                    best_model_path = Path(output_dir) / "best_dc_model.pth"
                    torch.save({
                        'step': actual_step + 1,
                        'model_state_dict': diffusion_model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'scaler_state_dict': scaler.state_dict(),
                        'lr_scheduler_state_dict': lr_scheduler.state_dict(),  # 添加学习率调度器状态
                        'loss': loss.item(),
                        'accuracy': accuracy.item(),
                        'eval_success_rate': current_success_rate,
                        'eval_avg_reward': current_avg_reward,
                        'timestamp': datetime.now().isoformat(),
                    }, best_model_path)
                    
                    logging.info(f"🏆 新的最佳模型! 成功率: {best_success_rate:.2f}%, 平均奖励: {best_avg_reward:.3f}")
                    logging.info(f"🏆 最佳模型已保存: {best_model_path}")
                    
                    # 更新进度条显示
                    progress_bar.set_postfix({
                        'total_loss': f"{loss.item():.4f}",
                        'cont_loss': f"{contrastive_loss_value.item():.4f}",
                        'reg_loss': f"{reg_loss.item():.4f}",
                        'acc': f"{accuracy.item():.3f}",
                        'diag': f"{diagonal_errors.mean().item():.4f}",
                        'off_diag': f"{off_diagonal_errors.mean().item():.4f}",
                        'best_success': f"{best_success_rate:.2f}%"
                    })
                else:
                    logging.info(f"📊 当前最佳: 成功率 {best_success_rate:.2f}%, 平均奖励 {best_avg_reward:.3f}")
                    
            except Exception as eval_error:
                logging.error(f"❌ 评估过程中发生异常: {eval_error}")
                import traceback
                eval_traceback = traceback.format_exc()
                logging.error(f"❌ 评估异常堆栈:\n{eval_traceback}")
                # 继续训练，不因评估失败而中断
    
    # Save final model (complete model)
    final_path = Path(output_dir) / "dc_model_final.pth"
    torch.save({
        'step': steps,
        'model_state_dict': diffusion_model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scaler_state_dict': scaler.state_dict(),
        'lr_scheduler_state_dict': lr_scheduler.state_dict(),  # 添加学习率调度器状态
        'loss': loss.item(),
        'accuracy': accuracy.item(),
        'timestamp': datetime.now().isoformat(),
    }, final_path)
    logging.info(f"最终完整模型已保存: {final_path}")
    
    # 输出最佳模型信息
    if best_model_path and best_model_path.exists():
        logging.info("🏆 训练总结:")
        logging.info(f"  最佳模型路径: {best_model_path}")
        logging.info(f"  最佳成功率: {best_success_rate:.2f}%")
        logging.info(f"  最佳平均奖励: {best_avg_reward:.3f}")
    else:
        logging.info("⚠️ 未找到最佳模型")


def main():
    parser = argparse.ArgumentParser(description="Train DiffusionModel_DC with lerobot dataset")
    
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="Path to lerobot checkpoint directory")
    parser.add_argument("--dataset", type=str, default="lerobot/pusht",
                       help="Dataset repository ID")
    parser.add_argument("--n_dc_tokens", type=int, default=4,
                       help="Number of DC tokens per layer")
    parser.add_argument("--n_dc_layers", type=int, default=6,
                       help="Number of layers to apply DC tokens")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use for training")
    parser.add_argument("--batch_size", type=int, default=64,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-3,
                       help="Learning rate for DC token training")
    parser.add_argument("--steps", type=int, default=10000,
                       help="Number of training steps")
    parser.add_argument("--log_freq", type=int, default=100,
                       help="Logging frequency")
    parser.add_argument("--save_freq", type=int, default=5000,
                       help="Checkpoint saving frequency")
    parser.add_argument("--eval_freq", type=int, default=5000,
                       help="Evaluation frequency")
    parser.add_argument("--env_type", type=str, default="pusht",
                       help="Environment type for evaluation")
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/work/lerobot-add_transformer/dc_checkpoints",
                       help="Base output directory for checkpoints")
    parser.add_argument("--resume_from", type=str, default=None,
                       help="Path to checkpoint file to resume training from")
    parser.add_argument("--auto_resume", action="store_true",
                       help="Automatically find and resume from the latest checkpoint in output_dir")
    
    args = parser.parse_args()
    
    # Create training directory with timestamp FIRST
    training_dir = create_training_directory(args.output_dir)
    args.output_dir = training_dir  # Update output_dir to the new training directory
    
    # Setup logging IMMEDIATELY after creating directory
    setup_logging(args.output_dir)
    # 不要调用 init_logging()，避免冲突

    # Now we can safely use logging
    logging.info("=" * 60)
    logging.info("SoftREPA DC Token Training with Lerobot Dataset")
    logging.info("=" * 60)
    flush_logs()  # 强制刷新日志
    
    # 验证导入
    try:
        from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
        from lerobot.configs.policies import PreTrainedConfig
        logging.info("✅ 所有必要模块导入成功")
    except ImportError as e:
        logging.error(f"❌ 导入模块失败: {e}")
        return
    
    # Validate inputs
    if not Path(args.checkpoint).exists():
        logging.error(f"Checkpoint directory not found: {args.checkpoint}")
        return
    
    # Get device
    device = get_safe_torch_device(args.device, log=True)
    
    logging.info(f"Configuration:")
    logging.info(f"  Checkpoint: {args.checkpoint}")
    logging.info(f"  Dataset: {args.dataset}")
    logging.info(f"  DC tokens: {args.n_dc_tokens} tokens, {args.n_dc_layers} layers")
    logging.info(f"  Device: {device}")
    logging.info(f"  Batch size: {args.batch_size}")
    logging.info(f"  Learning rate: {args.learning_rate}")
    logging.info(f"  Training steps: {args.steps}")
    logging.info(f"  Evaluation frequency: {args.eval_freq}")
    logging.info(f"  Environment type: {args.env_type}")
    logging.info(f"  Training directory: {args.output_dir}")
    logging.info("  注意: 每5000步进行评估并更新最佳模型")
    
    # Handle resume functionality
    resume_checkpoint_path = None
    if args.resume_from:
        if Path(args.resume_from).exists():
            resume_checkpoint_path = args.resume_from
            logging.info(f"🔄 指定恢复checkpoint: {resume_checkpoint_path}")
        else:
            logging.error(f"❌ 指定的resume checkpoint不存在: {args.resume_from}")
            return
    elif args.auto_resume:
        # Find latest checkpoint in output directory
        resume_checkpoint_path = find_latest_checkpoint(args.output_dir)
        if resume_checkpoint_path:
            logging.info(f"🔄 自动找到最新checkpoint: {resume_checkpoint_path}")
        else:
            logging.info("ℹ️ 未找到可恢复的checkpoint，将从头开始训练")
    
    if resume_checkpoint_path:
        logging.info("=" * 60)
        logging.info("🔄 Resume模式激活")
        logging.info(f"📁 Resume checkpoint: {resume_checkpoint_path}")
        logging.info("=" * 60)
    
    try:
        # Create dataset config
        dataset_config = create_dataset_config(args.dataset)
        
        # Load dataset
        logging.info("Loading dataset...")

        # Set up delta_timestamps for sequence loading
        delta_timestamps = {
            # Load 2 observation steps: previous and current
            "observation.state": [-0.1, 0.0],
            "observation.image": [-0.1, 0.0],
            # Load 16 action steps: current and 15 future actions
            "action": [i * 0.1 for i in range(16)],  # [0.0, 0.1, 0.2, ..., 1.5]
        }

        # Create LeRobotDataset directly
        from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
        dataset = LeRobotDataset(
            repo_id=dataset_config.dataset.repo_id,
            delta_timestamps=delta_timestamps,
            image_transforms=None,  # No image transforms for now
        )
        logging.info(f"Dataset loaded: {len(dataset)} samples")
        
        # Create dataloader
        dataloader = DataLoader(
            dataset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=device.type != "cpu",
            drop_last=True
        )
        
        # 🎯 首先评估原版预训练模型作为baseline
        logging.info("=" * 60)
        logging.info("🎯 步骤1: 评估原版预训练模型 (Baseline)")
        # logging.info("=" * 60)
        
        # baseline_results = evaluate_baseline_model(args.checkpoint, args.env_type)
        
        # # 保存baseline结果
        # baseline_results_file = Path(args.output_dir) / "baseline_results.json"
        # with open(baseline_results_file, 'w') as f:
        #     import json
        #     # 处理不能序列化的对象
        #     serializable_results = {
        #         "success_rate": baseline_results["success_rate"],
        #         "avg_reward": baseline_results["avg_reward"],
        #         "model_type": baseline_results["model_type"],
        #         "timestamp": datetime.now().isoformat()
        #     }
        #     if "error" in baseline_results:
        #         serializable_results["error"] = baseline_results["error"]
        #     json.dump(serializable_results, f, indent=2)
        
        # logging.info(f"📊 Baseline结果已保存到: {baseline_results_file}")
        # logging.info("=" * 60)
        logging.info("🚀 步骤2: 开始DC Token训练")
        logging.info("=" * 60)
        flush_logs()
        
        # Load model from checkpoint
        logging.info("Loading DiffusionModel_DC from checkpoint...")
        diffusion_model = load_and_create_dc_model(
            args.checkpoint,
            args.n_dc_tokens,
            args.n_dc_layers
        )
        
        # Train DC tokens
        train_dc_tokens(
            diffusion_model=diffusion_model,
            dataloader=dataloader,
            device=device,
            learning_rate=args.learning_rate,
            steps=args.steps,
            log_freq=args.log_freq,
            save_freq=args.save_freq,
            eval_freq=args.eval_freq,
            output_dir=args.output_dir,
            checkpoint_path=args.checkpoint,
            env_type=args.env_type,
            args=args,  # 传递完整的args参数
            resume_from=resume_checkpoint_path  # 传递resume参数
        )
        
        logging.info("=" * 60)
        logging.info("Training completed successfully!")
        logging.info("=" * 60)
        logging.info(f"训练结果保存在: {args.output_dir}")

        # 显示pth文件
        logging.info("保存的模型文件:")
        for file_path in Path(args.output_dir).glob("*.pth"):
            file_size = file_path.stat().st_size / (1024 * 1024)  # MB
            logging.info(f"  - {file_path.name} ({file_size:.1f} MB)")

        logging.info(f"日志文件: {Path(args.output_dir) / 'training.log'}")
        logging.info("注意: 最佳模型保存为 best_dc_model.pth")
        logging.info("=" * 60)
        flush_logs()  # 最终刷新日志
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 