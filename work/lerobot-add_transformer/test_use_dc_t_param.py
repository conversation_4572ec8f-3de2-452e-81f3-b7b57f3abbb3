#!/usr/bin/env python

"""
测试脚本：验证train_dc_with_dataset.py的--use_dc_t参数功能
"""

import subprocess
import sys
import os

def test_use_dc_t_parameter():
    """测试--use_dc_t参数是否正确工作"""
    
    print("🧪 测试train_dc_with_dataset.py的--use_dc_t参数")
    
    # 测试1: 检查帮助信息中是否包含--use_dc_t参数
    print("\n📋 测试1: 检查帮助信息")
    try:
        result = subprocess.run([
            sys.executable, "train_dc_with_dataset.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if "--use_dc_t" in result.stdout:
            print("✅ --use_dc_t参数已正确添加到帮助信息中")
            
            # 查找相关行
            lines = result.stdout.split('\n')
            for line in lines:
                if "--use_dc_t" in line or "time-dependent DC tokens" in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ --use_dc_t参数未在帮助信息中找到")
            print("帮助信息:")
            print(result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 获取帮助信息超时")
        return False
    except Exception as e:
        print(f"❌ 获取帮助信息失败: {e}")
        return False
    
    # 测试2: 测试参数解析（不实际运行训练）
    print("\n📋 测试2: 测试参数解析")
    
    # 创建一个简单的测试脚本来验证参数解析
    test_script = '''
import sys
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import argparse

# 复制train_dc_with_dataset.py中的参数定义
parser = argparse.ArgumentParser(description="Train DiffusionModel_DC with lerobot dataset")

parser.add_argument("--checkpoint", type=str, required=True,
                   help="Path to lerobot checkpoint directory")
parser.add_argument("--dataset", type=str, default="lerobot/pusht",
                   help="Dataset repository ID")
parser.add_argument("--n_dc_tokens", type=int, default=4,
                   help="Number of DC tokens per layer")
parser.add_argument("--n_dc_layers", type=int, default=6,
                   help="Number of layers to apply DC tokens")
parser.add_argument("--use_dc_t", action="store_true", default=False,
                   help="Whether to use time-dependent DC tokens")
parser.add_argument("--device", type=str, default="cuda",
                   help="Device to use for training")

# 测试不同的参数组合
test_cases = [
    ["--checkpoint", "/fake/path"],  # 默认use_dc_t=False
    ["--checkpoint", "/fake/path", "--use_dc_t"],  # 显式设置use_dc_t=True
]

for i, test_case in enumerate(test_cases):
    try:
        args = parser.parse_args(test_case)
        print(f"测试用例 {i+1}: {test_case}")
        print(f"  use_dc_t = {args.use_dc_t}")
        print(f"  n_dc_tokens = {args.n_dc_tokens}")
        print(f"  n_dc_layers = {args.n_dc_layers}")
        print("  ✅ 参数解析成功")
    except Exception as e:
        print(f"测试用例 {i+1}: {test_case}")
        print(f"  ❌ 参数解析失败: {e}")

print("\\n✅ 参数解析测试完成")
'''
    
    try:
        result = subprocess.run([
            sys.executable, "-c", test_script
        ], capture_output=True, text=True, timeout=30)
        
        print("参数解析测试结果:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
        if result.returncode == 0:
            print("✅ 参数解析测试通过")
        else:
            print("❌ 参数解析测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 参数解析测试超时")
        return False
    except Exception as e:
        print(f"❌ 参数解析测试失败: {e}")
        return False
    
    # 测试3: 验证load_and_create_dc_model函数签名
    print("\n📋 测试3: 验证函数签名")
    
    function_test_script = '''
import sys
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

try:
    from train_dc_with_dataset import load_and_create_dc_model
    import inspect
    
    # 获取函数签名
    sig = inspect.signature(load_and_create_dc_model)
    print("load_and_create_dc_model函数签名:")
    print(f"  {sig}")
    
    # 检查参数
    params = sig.parameters
    if 'use_dc_t' in params:
        param = params['use_dc_t']
        print(f"  use_dc_t参数: {param}")
        print(f"    默认值: {param.default}")
        print(f"    类型注解: {param.annotation}")
        print("  ✅ use_dc_t参数已正确添加")
    else:
        print("  ❌ use_dc_t参数未找到")
        
except Exception as e:
    print(f"❌ 函数签名检查失败: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        result = subprocess.run([
            sys.executable, "-c", function_test_script
        ], capture_output=True, text=True, timeout=30)
        
        print("函数签名测试结果:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
        if result.returncode == 0 and "✅ use_dc_t参数已正确添加" in result.stdout:
            print("✅ 函数签名测试通过")
        else:
            print("❌ 函数签名测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 函数签名测试超时")
        return False
    except Exception as e:
        print(f"❌ 函数签名测试失败: {e}")
        return False
    
    print("\n🎉 所有测试通过! --use_dc_t参数功能正常。")
    print("\n📝 使用说明:")
    print("  不使用dc_t tokens: python train_dc_with_dataset.py --checkpoint /path/to/checkpoint")
    print("  使用dc_t tokens:   python train_dc_with_dataset.py --checkpoint /path/to/checkpoint --use_dc_t")
    
    return True


if __name__ == "__main__":
    success = test_use_dc_t_parameter()
    if not success:
        sys.exit(1)
