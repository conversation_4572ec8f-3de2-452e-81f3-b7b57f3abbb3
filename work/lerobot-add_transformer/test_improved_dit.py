#!/usr/bin/env python3

"""
测试改进后的 DiTForDiffusion 实现
基于 dit-policy 架构的编码器-解码器结构
"""

import sys
import torch
import torch.nn as nn
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.modeling_diffusion import DiTForDiffusion


def test_improved_dit():
    """测试改进后的 DiT 实现"""
    print("🧪 测试改进后的 DiTForDiffusion")
    print("=" * 60)
    
    # 创建配置
    config = DiffusionConfig()
    config.horizon = 16
    config.n_obs_steps = 2
    config.diffusion_step_embed_dim = 256
    config.n_layer = 4
    config.n_head = 8
    config.p_drop_attn = 0.1
    config.p_drop_emb = 0.1

    # 使用新的配置方式 - 通过 output_shapes 设置动作维度
    # 这样避免了直接设置不存在的属性
    action_dim = 7
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🔧 使用设备: {device}")
    
    # 计算条件维度
    robot_state_dim = 14  # 机器人状态维度
    image_feature_dim = 64  # 图像特征维度 (spatial_softmax_num_keypoints * 2)
    num_cameras = 2  # 摄像头数量

    # 单步条件维度
    single_cond_dim = robot_state_dim + image_feature_dim * num_cameras
    # 多步条件维度 (展平)
    total_cond_dim = single_cond_dim * config.n_obs_steps

    print(f"📊 配置信息:")
    print(f"   • 动作维度: {action_dim}")
    print(f"   • 时间步长: {config.horizon}")
    print(f"   • 观测步数: {config.n_obs_steps}")
    print(f"   • 隐藏维度: {config.diffusion_step_embed_dim}")
    print(f"   • 层数: {config.n_layer}")
    print(f"   • 注意力头数: {config.n_head}")
    print(f"   • 单步条件维度: {single_cond_dim}")
    print(f"   • 总条件维度: {total_cond_dim}")
    
    try:
        # 创建改进的 DiT 模型
        print("\n🏗️  创建改进的 DiT 模型...")
        dit_model = DiTForDiffusion(config, cond_dim=total_cond_dim).to(device)
        
        # 计算参数数量
        total_params = sum(p.numel() for p in dit_model.parameters())
        trainable_params = sum(p.numel() for p in dit_model.parameters() if p.requires_grad)
        
        print(f"✅ 模型创建成功!")
        print(f"   • 总参数数量: {total_params:,}")
        print(f"   • 可训练参数: {trainable_params:,}")
        
        # 测试前向传播
        print("\n🔄 测试前向传播...")
        batch_size = 4
        
        # 创建测试数据
        sample = torch.randn(batch_size, config.horizon, action_dim, device=device)
        timestep = torch.randint(0, 100, (batch_size,), device=device)
        global_cond = torch.randn(batch_size, total_cond_dim, device=device)
        
        print(f"   • 输入形状:")
        print(f"     - sample: {sample.shape}")
        print(f"     - timestep: {timestep.shape}")
        print(f"     - global_cond: {global_cond.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = dit_model(sample, timestep, global_cond)
        
        print(f"   • 输出形状: {output.shape}")
        print(f"   • 输出统计:")
        print(f"     - 均值: {output.mean().item():.6f}")
        print(f"     - 标准差: {output.std().item():.6f}")
        print(f"     - 最小值: {output.min().item():.6f}")
        print(f"     - 最大值: {output.max().item():.6f}")
        
        # 验证输出形状
        expected_shape = (batch_size, config.horizon, action_dim)
        if output.shape == expected_shape:
            print("✅ 输出形状正确!")
        else:
            print(f"❌ 输出形状错误! 期望: {expected_shape}, 实际: {output.shape}")
            return False
        
        # 测试梯度计算
        print("\n🎯 测试梯度计算...")
        dit_model.train()

        # 重新进行前向传播以确保梯度计算
        sample.requires_grad_(True)
        output = dit_model(sample, timestep, global_cond)

        # 创建损失
        target = torch.randn_like(output)
        loss = nn.MSELoss()(output, target)

        print(f"   • 损失值: {loss.item():.6f}")

        # 反向传播
        loss.backward()
        
        # 检查梯度
        grad_norm = 0
        param_count = 0
        for name, param in dit_model.named_parameters():
            if param.grad is not None:
                grad_norm += param.grad.data.norm(2).item() ** 2
                param_count += 1
        
        grad_norm = grad_norm ** 0.5
        print(f"   • 梯度范数: {grad_norm:.6f}")
        print(f"   • 有梯度的参数数量: {param_count}")
        
        if grad_norm > 0:
            print("✅ 梯度计算正常!")
        else:
            print("❌ 梯度计算异常!")
            return False
        
        # 测试编码器-解码器分离
        print("\n🔧 测试编码器-解码器分离...")
        dit_model.eval()
        
        with torch.no_grad():
            # 测试编码器
            obs_enc = global_cond.view(batch_size, config.n_obs_steps, single_cond_dim)
            enc_cache = dit_model.forward_enc(obs_enc)
            
            print(f"   • 编码器输出层数: {len(enc_cache)}")
            print(f"   • 每层输出形状: {enc_cache[0].shape}")
            
            # 测试解码器
            dec_output = dit_model.forward_dec(sample, timestep, enc_cache)
            
            print(f"   • 解码器输出形状: {dec_output.shape}")
            
            # 验证分离调用与完整调用的一致性
            full_output = dit_model(sample, timestep, global_cond)
            
            if torch.allclose(dec_output, full_output, atol=1e-6):
                print("✅ 编码器-解码器分离调用与完整调用一致!")
            else:
                print("❌ 编码器-解码器分离调用与完整调用不一致!")
                return False
        
        print("\n🎉 所有测试通过!")
        print("\n💡 改进后的 DiT 特性:")
        print("   ✅ 基于 dit-policy 的编码器-解码器架构")
        print("   ✅ 支持多步观测条件处理")
        print("   ✅ AdaLN 调制机制")
        print("   ✅ 位置编码和时间嵌入")
        print("   ✅ 分层条件传递")
        print("   ✅ 零初始化最终层")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_improved_dit()
    
    if success:
        print("\n🚀 改进后的 DiT 模型已准备就绪!")
        print("   • 可用于训练和推理")
        print("   • 支持复杂的观测条件处理")
        print("   • 具有更好的架构设计")
        return 0
    else:
        print("\n⚠️  测试失败，请检查实现")
        return 1


if __name__ == "__main__":
    sys.exit(main())
