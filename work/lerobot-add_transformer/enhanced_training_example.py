#!/usr/bin/env python

"""
Enhanced Training Example with tqdm Progress Bars
=================================================

This example demonstrates how to use the enhanced training script with detailed progress bars
and monitoring capabilities.

Features:
- Real-time training progress with ETA estimation
- Data loading speed monitoring
- Evaluation progress tracking
- Checkpoint saving timing
- Enhanced metrics display

Usage:
    python enhanced_training_example.py
"""

import subprocess
import sys
from pathlib import Path

def run_enhanced_training():
    """Run training with enhanced progress bars and monitoring."""
    
    # Example training command with enhanced progress tracking
    cmd = [
        sys.executable, "lerobot/scripts/train.py",
        "policy=diffusion_mmdit",  # Use our new MMDiT policy
        "env=pusht",
        
        # Training configuration
        "training.offline_steps=10000",
        "training.batch_size=256",
        "training.log_freq=100",
        "training.save_freq=2000",
        "training.eval_freq=1000",
        
        # Enhanced progress tracking
        "training.use_enhanced_progress=true",
        
        # Policy configuration for better progress visualization
        "policy.use_mmdit=true",
        "policy.use_mmdit_dc=true",
        "policy.n_dc_tokens=4",
        "policy.n_dc_layers=6",
        
        # Output configuration
        "output_dir=outputs/enhanced_training_demo",
        "wandb.enable=false",  # Disable wandb for this demo
        
        # Device configuration
        "device=cuda",
        "policy.use_amp=true",
    ]
    
    print("🚀 Starting Enhanced Training with Progress Bars")
    print("=" * 60)
    print("Features enabled:")
    print("  ✅ Real-time progress tracking with ETA")
    print("  ✅ Data loading speed monitoring")
    print("  ✅ Evaluation progress visualization")
    print("  ✅ Checkpoint saving timing")
    print("  ✅ Enhanced metrics display")
    print("  ✅ MMDiT architecture with SoftREPA")
    print("=" * 60)
    
    try:
        # Run the training command
        result = subprocess.run(cmd, check=True, cwd=Path(__file__).parent)
        print("\n🎉 Training completed successfully!")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error code {e.returncode}")
        print("Check the logs above for details.")
        return False
    
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        return False
    
    return True

def demonstrate_progress_features():
    """Demonstrate the progress tracking features."""
    
    print("\n📊 Progress Tracking Features:")
    print("-" * 40)
    
    print("1. Training Progress Bar:")
    print("   [████████████████████████████████] 5000/10000 [02:30<02:30, 33.33 steps/s]")
    print("   loss: 0.1234 | lr: 1.0e-04 | grad_norm: 0.567 | ETA: 2m30s")
    
    print("\n2. Data Loading Monitoring:")
    print("   Loading data: 100%|██████████| 256/256 [00:01<00:00, 180.5 batch/s]")
    print("   batch_size: 256 | load_time: 0.005s | samples/s: 51200.0")
    
    print("\n3. Evaluation Progress:")
    print("   🔍 Evaluating (step 5000)")
    print("   Stepping through eval batches: 100%|██████████| 10/10 [00:30<00:00, 3.33 batch/s]")
    print("   success: 85.0% | reward: 0.892 | eval_time: 30.2s")
    
    print("\n4. Checkpoint Saving:")
    print("   💾 Saving checkpoint (step 5000)")
    print("   save_time: 2.1s")
    
    print("\n5. Dynamic Status Updates:")
    print("   🚀 Training (High Success)  - when success rate >= 80%")
    print("   📈 Training (Good Progress) - when success rate >= 50%")
    print("   🔄 Training                 - normal training")

def show_configuration_tips():
    """Show tips for configuring enhanced progress tracking."""
    
    print("\n⚙️ Configuration Tips:")
    print("-" * 30)
    
    print("1. Adjust log frequency for optimal progress updates:")
    print("   training.log_freq=100  # Update every 100 steps")
    
    print("\n2. Configure progress smoothing window:")
    print("   # In the training script, TrainingProgressTracker uses:")
    print("   # smoothing_window=50  # Average over last 50 steps")
    
    print("\n3. Enable/disable specific progress features:")
    print("   # Data loading progress (automatic)")
    print("   # Evaluation progress (automatic)")
    print("   # Checkpoint timing (automatic)")
    
    print("\n4. For better performance on slow terminals:")
    print("   # Set dynamic_ncols=False in tqdm configuration")
    print("   # Increase log_freq to reduce update frequency")

if __name__ == "__main__":
    print("Enhanced Training with tqdm Progress Bars")
    print("========================================")
    
    # Show features demonstration
    demonstrate_progress_features()
    
    # Show configuration tips
    show_configuration_tips()
    
    print("\n" + "=" * 60)
    response = input("Do you want to run the enhanced training demo? (y/N): ")
    
    if response.lower() in ['y', 'yes']:
        success = run_enhanced_training()
        if success:
            print("\n✨ Demo completed! Check the outputs/enhanced_training_demo directory for results.")
        else:
            print("\n💡 Tip: Make sure you have the required dependencies and datasets available.")
    else:
        print("\n👋 Demo skipped. You can run the training manually using the command shown above.")
