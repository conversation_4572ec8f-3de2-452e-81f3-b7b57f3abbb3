#!/usr/bin/env python3
"""
简化的模型权重提取工具

快速提取LeRobot检查点中的TransformerForDiffusion权重
"""

import torch
import json
from pathlib import Path
import safetensors.torch

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionPolicy, TransformerForDiffusion
from lerobot.configs.policies import PreTrainedConfig


def load_model_from_checkpoint(checkpoint_path):
    """从检查点加载模型"""
    print(f"正在加载模型: {checkpoint_path}")
    
    # 加载配置
    config = PreTrainedConfig.from_pretrained(checkpoint_path)
    print(f"模型类型: {config.type}")
    
    # 加载策略
    policy = DiffusionPolicy.from_pretrained(
        pretrained_name_or_path=str(checkpoint_path),
        config=config
    )
    
    print("模型加载成功!")
    return policy, config


def analyze_model_structure(policy):
    """分析模型结构"""
    print("\n=== 模型架构分析 ===")
    
    # 总参数量
    total_params = sum(p.numel() for p in policy.parameters())
    trainable_params = sum(p.numel() for p in policy.parameters() if p.requires_grad)
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    
    # 查找TransformerForDiffusion
    transformer_found = False
    for name, module in policy.named_modules():
        if isinstance(module, TransformerForDiffusion):
            print(f"找到TransformerForDiffusion: {name}")
            transformer_params = sum(p.numel() for p in module.parameters())
            print(f"Transformer参数量: {transformer_params:,}")
            transformer_found = True
            break
    
    if not transformer_found:
        print("未找到TransformerForDiffusion模块")
    
    return transformer_found


def extract_transformer_weights(policy):
    """提取TransformerForDiffusion权重"""
    print("\n=== 提取Transformer权重 ===")
    
    # 查找TransformerForDiffusion模块
    transformer_module = None
    for name, module in policy.named_modules():
        if isinstance(module, TransformerForDiffusion):
            transformer_module = module
            print(f"从模块 '{name}' 提取权重")
            break
    
    if transformer_module is None:
        print("错误: 未找到TransformerForDiffusion模块")
        return None
    
    # 提取权重
    weights = {}
    for name, param in transformer_module.named_parameters():
        weights[name] = param.detach().cpu().clone()
        print(f"  {name}: {list(param.shape)}")
    
    print(f"成功提取 {len(weights)} 个权重参数")
    return weights


def save_weights(weights, output_dir, name="transformer_weights"):
    """保存权重到文件"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 保存为safetensors格式
    safetensors_path = output_path / f"{name}.safetensors"
    safetensors.torch.save_file(weights, str(safetensors_path))
    print(f"权重已保存: {safetensors_path}")
    
    # 保存为PyTorch格式
    torch_path = output_path / f"{name}.pth"
    torch.save(weights, torch_path)
    print(f"权重已保存: {torch_path}")
    
    # 保存权重信息
    weight_info = {}
    for name, weight in weights.items():
        weight_info[name] = {
            "shape": list(weight.shape),
            "dtype": str(weight.dtype),
            "parameters": weight.numel()
        }
    
    info_path = output_path / f"{name}_info.json"
    with open(info_path, 'w', encoding='utf-8') as f:
        json.dump(weight_info, f, indent=2, ensure_ascii=False)
    print(f"权重信息已保存: {info_path}")
    
    return {
        "safetensors": safetensors_path,
        "pytorch": torch_path,
        "info": info_path
    }


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 3:
        print("使用方法: python simple_weight_extractor.py <checkpoint_path> <output_dir>")
        print("示例: python simple_weight_extractor.py ./checkpoints/190000/pretrained_model ./extracted_weights")
        sys.exit(1)
    
    checkpoint_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    try:
        # 加载模型
        policy, config = load_model_from_checkpoint(checkpoint_path)
        
        # 分析模型结构
        transformer_found = analyze_model_structure(policy)
        
        if transformer_found:
            # 提取TransformerForDiffusion权重
            transformer_weights = extract_transformer_weights(policy)
            
            if transformer_weights:
                # 保存权重
                print(f"\n=== 保存权重到 {output_dir} ===")
                save_weights(transformer_weights, output_dir, "transformer_weights")
        
        # 也可以提取完整模型权重
        print(f"\n=== 保存完整模型权重 ===")
        full_weights = {}
        for name, param in policy.named_parameters():
            full_weights[name] = param.detach().cpu().clone()
        
        save_weights(full_weights, output_dir, "full_model_weights")
        
        print("\n✅ 权重提取完成!")
        
    except Exception as e:
        print(f"❌ 提取失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 