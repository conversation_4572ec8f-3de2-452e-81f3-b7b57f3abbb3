#!/usr/bin/env python3
"""
创建TransformerForDiffusion实例并从权重文件加载

这个脚本展示如何从提取的权重文件重新创建TransformerForDiffusion模型
"""

import torch
import torch.nn as nn
from pathlib import Path
from typing import Dict, Any, Optional

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import TransformerForDiffusion
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.configs.policies import PreTrainedConfig


class TransformerLoader:
    """TransformerForDiffusion加载器"""
    
    def __init__(self):
        self.model = None
        self.config = None
    
    def create_transformer_from_config(self, config_path: str, cond_dim: int = 66) -> TransformerForDiffusion:
        """从配置文件创建TransformerForDiffusion实例"""
        print(f"从配置文件创建TransformerForDiffusion: {config_path}")
        
        # 加载配置
        config = PreTrainedConfig.from_pretrained(config_path)
        print(f"配置类型: {config.type}")
        
        if config.type != "diffusion":
            raise ValueError(f"配置类型必须是'diffusion'，当前是'{config.type}'")
        
        # 创建TransformerForDiffusion实例
        transformer = TransformerForDiffusion(config, cond_dim=cond_dim)
        
        self.model = transformer
        self.config = config
        
        print(f"TransformerForDiffusion创建成功!")
        print(f"参数量: {sum(p.numel() for p in transformer.parameters()):,}")
        
        return transformer
    
    def create_transformer_from_params(self, **kwargs) -> TransformerForDiffusion:
        """从参数直接创建TransformerForDiffusion实例"""
        print("从参数创建TransformerForDiffusion...")
        
        # 创建一个基本的DiffusionConfig
        default_config = {
            'use_transformer': True,
            'diffusion_step_embed_dim': 1024,
            'n_layer': 12,
            'n_head': 16,
            'n_cond_layers': 12,
            'horizon': 16,
            'n_obs_steps': 2,
            'causal_attn': True,
            'p_drop_emb': 0.0,
            'p_drop_attn': 0.3,
            'action_feature': type('obj', (object,), {'shape': [2]})(),  # 创建一个简单的形状对象
        }
        
        # 更新配置参数
        default_config.update(kwargs)
        
        # 创建配置对象
        config = type('DiffusionConfig', (object,), default_config)()
        
        # 获取条件维度
        cond_dim = kwargs.get('cond_dim', 66)
        
        # 创建TransformerForDiffusion实例
        transformer = TransformerForDiffusion(config, cond_dim=cond_dim)
        
        self.model = transformer
        self.config = config
        
        print(f"TransformerForDiffusion创建成功!")
        print(f"参数量: {sum(p.numel() for p in transformer.parameters()):,}")
        
        return transformer
    
    def load_weights_from_pth(self, weights_path: str, strict: bool = True) -> None:
        """从.pth文件加载权重"""
        if self.model is None:
            raise ValueError("请先创建模型实例")
        
        print(f"从 {weights_path} 加载权重...")
        
        # 加载权重文件
        weights = torch.load(weights_path, map_location='cpu')
        
        print(f"权重文件包含 {len(weights)} 个参数")
        
        # 加载权重到模型
        missing_keys, unexpected_keys = self.model.load_state_dict(weights, strict=strict)
        
        if missing_keys:
            print(f"缺失的键: {missing_keys}")
        if unexpected_keys:
            print(f"意外的键: {unexpected_keys}")
        
        if not missing_keys and not unexpected_keys:
            print("✅ 权重加载成功，所有参数匹配!")
        elif not strict:
            print("⚠️ 权重加载完成，但有部分参数不匹配 (strict=False)")
        else:
            print("❌ 权重加载失败，存在不匹配的参数")
    
    def verify_model(self, batch_size: int = 2) -> bool:
        """验证模型是否可以正常前向传播"""
        if self.model is None:
            raise ValueError("请先创建并加载模型")
        
        print(f"验证模型 (batch_size={batch_size})...")
        
        try:
            self.model.eval()
            
            # 创建测试输入
            horizon = getattr(self.config, 'horizon', 16)
            action_dim = getattr(self.config, 'action_feature', type('obj', (object,), {'shape': [2]})()).shape[0]
            n_obs_steps = getattr(self.config, 'n_obs_steps', 2)
            
            # 假设条件维度为66 (64维图像特征 + 2维状态)
            cond_dim = 66
            
            sample = torch.randn(batch_size, horizon, action_dim)
            timestep = torch.randint(0, 100, (batch_size,))
            global_cond = torch.randn(batch_size, n_obs_steps * cond_dim)
            
            print(f"输入形状:")
            print(f"  sample: {sample.shape}")
            print(f"  timestep: {timestep.shape}")
            print(f"  global_cond: {global_cond.shape}")
            
            # 前向传播
            with torch.no_grad():
                output = self.model(sample, timestep, global_cond)
            
            print(f"输出形状: {output.shape}")
            print(f"输出统计: min={output.min().item():.4f}, max={output.max().item():.4f}, mean={output.mean().item():.4f}")
            
            print("✅ 模型验证成功!")
            return True
            
        except Exception as e:
            print(f"❌ 模型验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_model(self, save_path: str) -> None:
        """保存完整的模型（包括架构和权重）"""
        if self.model is None:
            raise ValueError("请先创建模型")
        
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存完整模型
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': vars(self.config) if hasattr(self.config, '__dict__') else self.config,
            'model_class': 'TransformerForDiffusion'
        }, save_path)
        
        print(f"模型已保存到: {save_path}")


def example_usage():
    """使用示例"""
    print("=== TransformerForDiffusion 加载示例 ===\n")
    
    # 创建加载器
    loader = TransformerLoader()
    
    # 方法1: 从配置文件创建
    try:
        checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
        transformer = loader.create_transformer_from_config(checkpoint_path, cond_dim=66)
        
        # 加载权重
        weights_path = "./extracted_weights/transformer_weights.pth"
        if Path(weights_path).exists():
            loader.load_weights_from_pth(weights_path, strict=True)
            
            # 验证模型
            loader.verify_model(batch_size=2)
            
        else:
            print(f"权重文件不存在: {weights_path}")
    
    except Exception as e:
        print(f"从配置文件创建失败: {e}")
        
        # 方法2: 从参数直接创建（备用方案）
        print("\n尝试从参数直接创建...")
        transformer = loader.create_transformer_from_params(
            diffusion_step_embed_dim=1024,
            n_layer=12,
            n_head=16,
            n_cond_layers=12,
            horizon=16,
            n_obs_steps=2,
            causal_attn=True,
            p_drop_emb=0.0,
            p_drop_attn=0.3,
            cond_dim=66
        )
        
        # 加载权重
        weights_path = "./extracted_weights/transformer_weights.pth"
        if Path(weights_path).exists():
            loader.load_weights_from_pth(weights_path, strict=False)  # 使用非严格模式
            
            # 验证模型
            loader.verify_model(batch_size=2)


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("1. 从配置文件创建: python load_transformer_from_weights.py config <config_path> <weights_path>")
        print("2. 从参数创建: python load_transformer_from_weights.py params <weights_path>")
        print("3. 运行示例: python load_transformer_from_weights.py example")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "example":
        example_usage()
    
    elif mode == "config":
        if len(sys.argv) != 4:
            print("从配置文件创建需要: python load_transformer_from_weights.py config <config_path> <weights_path>")
            sys.exit(1)
        
        config_path = sys.argv[2]
        weights_path = sys.argv[3]
        
        loader = TransformerLoader()
        transformer = loader.create_transformer_from_config(config_path, cond_dim=66)
        loader.load_weights_from_pth(weights_path, strict=True)
        loader.verify_model()
        
    elif mode == "params":
        if len(sys.argv) != 3:
            print("从参数创建需要: python load_transformer_from_weights.py params <weights_path>")
            sys.exit(1)
        
        weights_path = sys.argv[2]
        
        loader = TransformerLoader()
        transformer = loader.create_transformer_from_params(
            diffusion_step_embed_dim=1024,
            n_layer=12,
            n_head=16,
            n_cond_layers=12,
            horizon=16,
            n_obs_steps=2,
            causal_attn=True,
            p_drop_emb=0.0,
            p_drop_attn=0.3,
            cond_dim=66
        )
        loader.load_weights_from_pth(weights_path, strict=False)
        loader.verify_model()
    
    else:
        print(f"未知模式: {mode}")
        sys.exit(1)


if __name__ == "__main__":
    main() 