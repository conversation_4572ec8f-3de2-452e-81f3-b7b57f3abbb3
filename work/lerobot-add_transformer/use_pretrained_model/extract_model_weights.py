#!/usr/bin/env python3
"""
模型权重提取工具

这个脚本用于从LeRobot检查点中提取模型架构和权重参数，
特别是针对TransformerForDiffusion的net权重参数。

使用方法:
python extract_model_weights.py --checkpoint_path /path/to/checkpoint --output_dir /path/to/output

支持的功能:
1. 提取完整的DiffusionPolicy模型
2. 仅提取TransformerForDiffusion (net) 部分
3. 分析模型架构和参数量
4. 保存权重到不同格式 (safetensors, pickle, state_dict)
"""

import argparse
import json
import pickle
from pathlib import Path
from typing import Dict, Any, Optional
import logging

import torch
import torch.nn as nn
import safetensors.torch
import pandas as pd

# 导入LeRobot相关模块
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionPolicy, TransformerForDiffusion
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.configs.policies import PreTrainedConfig


# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ModelWeightExtractor:
    """模型权重提取器"""
    
    def __init__(self, checkpoint_path: str):
        self.checkpoint_path = Path(checkpoint_path)
        self.policy = None
        self.config = None
        
    def load_policy(self) -> DiffusionPolicy:
        """加载策略模型"""
        logger.info(f"从 {self.checkpoint_path} 加载策略模型...")
        
        try:
            # 加载策略配置
            config = PreTrainedConfig.from_pretrained(self.checkpoint_path)
            logger.info(f"配置类型: {config.type}")
            
            # 加载策略模型
            self.policy = DiffusionPolicy.from_pretrained(
                pretrained_name_or_path=str(self.checkpoint_path),
                config=config
            )
            self.config = config
            
            logger.info("策略模型加载成功!")
            return self.policy
            
        except Exception as e:
            logger.error(f"加载策略失败: {e}")
            raise
    
    def analyze_model_architecture(self) -> Dict[str, Any]:
        """分析模型架构"""
        if self.policy is None:
            self.load_policy()
            
        architecture_info = {
            "policy_type": self.config.type,
            "model_components": {},
            "parameter_count": {},
            "total_parameters": 0,
            "trainable_parameters": 0
        }
        
        # 分析主要组件
        for name, module in self.policy.named_modules():
            if len(list(module.children())) == 0:  # 叶子模块
                param_count = sum(p.numel() for p in module.parameters())
                if param_count > 0:
                    architecture_info["model_components"][name] = {
                        "type": type(module).__name__,
                        "parameters": param_count
                    }
        
        # 计算参数量
        total_params = sum(p.numel() for p in self.policy.parameters())
        trainable_params = sum(p.numel() for p in self.policy.parameters() if p.requires_grad)
        
        architecture_info["total_parameters"] = total_params
        architecture_info["trainable_parameters"] = trainable_params
        architecture_info["parameter_count"] = {
            "total": f"{total_params:,}",
            "trainable": f"{trainable_params:,}",
            "non_trainable": f"{total_params - trainable_params:,}"
        }
        
        logger.info(f"总参数量: {total_params:,}")
        logger.info(f"可训练参数量: {trainable_params:,}")
        
        return architecture_info
    
    def extract_transformer_weights(self) -> Optional[Dict[str, torch.Tensor]]:
        """提取TransformerForDiffusion (net) 权重"""
        if self.policy is None:
            self.load_policy()
        
        # 查找TransformerForDiffusion模块
        transformer_net = None
        for name, module in self.policy.named_modules():
            if isinstance(module, TransformerForDiffusion):
                transformer_net = module
                logger.info(f"找到TransformerForDiffusion模块: {name}")
                break
        
        if transformer_net is None:
            logger.warning("未找到TransformerForDiffusion模块")
            return None
        
        # 提取权重
        weights = {}
        for name, param in transformer_net.named_parameters():
            weights[name] = param.detach().cpu().clone()
            logger.debug(f"提取权重: {name}, 形状: {param.shape}")
        
        logger.info(f"成功提取 {len(weights)} 个TransformerForDiffusion权重参数")
        return weights
    
    def extract_full_model_weights(self) -> Dict[str, torch.Tensor]:
        """提取完整模型权重"""
        if self.policy is None:
            self.load_policy()
        
        weights = {}
        for name, param in self.policy.named_parameters():
            weights[name] = param.detach().cpu().clone()
            logger.debug(f"提取权重: {name}, 形状: {param.shape}")
        
        logger.info(f"成功提取 {len(weights)} 个完整模型权重参数")
        return weights
    
    def get_model_state_dict(self) -> Dict[str, torch.Tensor]:
        """获取模型state_dict"""
        if self.policy is None:
            self.load_policy()
        
        return self.policy.state_dict()
    
    def create_weight_summary(self, weights: Dict[str, torch.Tensor]) -> pd.DataFrame:
        """创建权重摘要表"""
        summary_data = []
        
        for name, weight in weights.items():
            summary_data.append({
                "参数名": name,
                "形状": str(list(weight.shape)),
                "参数量": weight.numel(),
                "数据类型": str(weight.dtype),
                "最小值": float(weight.min().item()),
                "最大值": float(weight.max().item()),
                "均值": float(weight.mean().item()),
                "标准差": float(weight.std().item())
            })
        
        return pd.DataFrame(summary_data)
    
    def save_weights(self, weights: Dict[str, torch.Tensor], output_dir: str, prefix: str = ""):
        """保存权重到多种格式"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存为safetensors格式
        safetensors_path = output_path / f"{prefix}weights.safetensors"
        safetensors.torch.save_file(weights, str(safetensors_path))
        logger.info(f"权重已保存为safetensors格式: {safetensors_path}")
        
        # 保存为pickle格式
        pickle_path = output_path / f"{prefix}weights.pkl"
        with open(pickle_path, 'wb') as f:
            pickle.dump(weights, f)
        logger.info(f"权重已保存为pickle格式: {pickle_path}")
        
        # 保存为PyTorch state_dict格式
        torch_path = output_path / f"{prefix}weights.pth"
        torch.save(weights, torch_path)
        logger.info(f"权重已保存为PyTorch格式: {torch_path}")
        
        # 保存权重摘要
        summary = self.create_weight_summary(weights)
        summary_path = output_path / f"{prefix}weight_summary.csv"
        summary.to_csv(summary_path, index=False, encoding='utf-8')
        logger.info(f"权重摘要已保存: {summary_path}")
        
        return {
            "safetensors": safetensors_path,
            "pickle": pickle_path,
            "pytorch": torch_path,
            "summary": summary_path
        }
    
    def save_architecture_info(self, output_dir: str) -> str:
        """保存架构信息"""
        architecture_info = self.analyze_model_architecture()
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        info_path = output_path / "architecture_info.json"
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(architecture_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"架构信息已保存: {info_path}")
        return str(info_path)


def main():
    parser = argparse.ArgumentParser(description="从LeRobot检查点提取模型权重")
    parser.add_argument("--checkpoint_path", type=str, required=True,
                       help="检查点路径 (通常是pretrained_model目录)")
    parser.add_argument("--output_dir", type=str, required=True,
                       help="输出目录")
    parser.add_argument("--extract_mode", type=str, choices=["full", "transformer", "both"], 
                       default="both", help="提取模式: full(完整模型), transformer(仅Transformer), both(两者)")
    parser.add_argument("--save_config", action="store_true", default=True,
                       help="是否保存配置文件")
    
    args = parser.parse_args()
    
    try:
        # 创建提取器
        extractor = ModelWeightExtractor(args.checkpoint_path)
        
        # 加载模型
        extractor.load_policy()
        
        # 保存架构信息
        extractor.save_architecture_info(args.output_dir)
        
        # 根据模式提取权重
        if args.extract_mode in ["full", "both"]:
            logger.info("提取完整模型权重...")
            full_weights = extractor.extract_full_model_weights()
            extractor.save_weights(full_weights, args.output_dir, "full_model_")
        
        if args.extract_mode in ["transformer", "both"]:
            logger.info("提取TransformerForDiffusion权重...")
            transformer_weights = extractor.extract_transformer_weights()
            if transformer_weights:
                extractor.save_weights(transformer_weights, args.output_dir, "transformer_")
            else:
                logger.warning("未找到TransformerForDiffusion权重")
        
        # 保存配置文件
        if args.save_config and extractor.config:
            config_path = Path(args.output_dir) / "config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(extractor.config.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件已保存: {config_path}")
        
        logger.info("权重提取完成!")
        
    except Exception as e:
        logger.error(f"提取失败: {e}")
        raise


if __name__ == "__main__":
    main() 