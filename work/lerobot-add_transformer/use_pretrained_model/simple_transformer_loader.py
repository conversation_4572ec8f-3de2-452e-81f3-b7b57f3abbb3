#!/usr/bin/env python3
"""
简化的DiffusionModel_DC加载器

快速创建和加载DiffusionModel_DC模型（支持SoftREPA风格的DC tokens）
"""

import torch
from pathlib import Path

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    create_diffusion_model_dc
)
from lerobot.configs.policies import PreTrainedConfig


def load_diffusion_dc_from_checkpoint(checkpoint_path: str, n_dc_tokens: int = 4,
                                     n_dc_layers: int = 6, use_dc_t: bool = True):
    """
    从检查点加载DiffusionModel_DC

    Args:
        checkpoint_path: 检查点配置路径
        n_dc_tokens: DC tokens数量
        n_dc_layers: DC层数
        use_dc_t: 是否使用时间相关的DC tokens

    Returns:
        DiffusionModel_DC实例
    """
    print(f"正在加载DiffusionModel_DC...")
    print(f"配置路径: {checkpoint_path}")
    print(f"DC配置: {n_dc_tokens} tokens, {n_dc_layers} layers, use_dc_t={use_dc_t}")

    # 1. 加载配置
    config_path = Path(checkpoint_path)
    if (config_path / "pretrained_model" / "config.json").exists():
        config = PreTrainedConfig.from_pretrained(str(config_path / "pretrained_model"))
    else:
        config = PreTrainedConfig.from_pretrained(checkpoint_path)

    print(f"配置类型: {config.type}")

    # 2. 使用工厂函数创建DiffusionModel_DC
    try:
        # 尝试从预训练模型加载
        diffusion_model = create_diffusion_model_dc(
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=use_dc_t,
            pretrained_model_path=checkpoint_path
        )
        print("✅ 从预训练模型加载DiffusionModel_DC成功!")

    except Exception as e:
        print(f"⚠️ 从预训练模型加载失败: {e}")
        print("创建新的DiffusionModel_DC...")

        # 创建新的DiffusionModel_DC
        diffusion_model = DiffusionModel_DC(
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=use_dc_t
        )
        print("✅ 新DiffusionModel_DC创建成功!")

    # 3. 打印模型信息
    total_params = sum(p.numel() for p in diffusion_model.parameters())
    dc_params = sum(p.numel() for name, p in diffusion_model.named_parameters()
                   if 'dc' in name.lower())

    print(f"模型参数统计:")
    print(f"  总参数量: {total_params:,}")
    print(f"  DC参数量: {dc_params:,}")
    print(f"  基础参数量: {total_params - dc_params:,}")

    return diffusion_model


def test_transformer(transformer, batch_size=2):
    """测试TransformerForDiffusion前向传播"""
    print(f"\n测试模型前向传播 (batch_size={batch_size})...")
    
    transformer.eval()
    
    # 输入维度
    horizon = 16  # 时间步长
    action_dim = 2  # 动作维度
    n_obs_steps = 2  # 观察步数
    cond_dim = 66  # 条件维度
    
    # 创建随机输入
    sample = torch.randn(batch_size, horizon, action_dim)
    timestep = torch.randint(0, 100, (batch_size,))
    global_cond = torch.randn(batch_size, n_obs_steps * cond_dim)
    
    print(f"输入形状:")
    print(f"  sample: {sample.shape}")
    print(f"  timestep: {timestep.shape}")  
    print(f"  global_cond: {global_cond.shape}")
    
    # 前向传播
    with torch.no_grad():
        output = transformer(sample, timestep, global_cond)
    
    print(f"输出形状: {output.shape}")
    print(f"输出统计: min={output.min():.4f}, max={output.max():.4f}, mean={output.mean():.4f}")
    print("✅ 模型测试成功!")
    
    return output


def save_standalone_model(transformer, save_path: str):
    """保存独立的模型文件"""
    save_path = Path(save_path)
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存完整模型
    torch.save({
        'model_state_dict': transformer.state_dict(),
        'model_class': 'TransformerForDiffusion',
        'architecture': {
            'embedding_dim': transformer.config.diffusion_step_embed_dim,
            'num_encoder_layers': transformer.config.n_cond_layers,
            'num_decoder_layers': transformer.config.n_layer,
            'num_heads': transformer.config.n_head,
            'horizon': transformer.config.horizon,
            'n_obs_steps': transformer.config.n_obs_steps,
        }
    }, save_path)
    
    print(f"独立模型已保存到: {save_path}")


def main():
    """主函数"""
    import sys
    
    # 默认路径
    default_checkpoint = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
    default_weights = "./extracted_weights/transformer_weights.pth"
    
    if len(sys.argv) == 1:
        # 使用默认路径
        checkpoint_path = default_checkpoint
        weights_path = default_weights
    elif len(sys.argv) == 2:
        # 只指定检查点路径
        checkpoint_path = sys.argv[1]
        weights_path = default_weights
    elif len(sys.argv) == 3:
        # 指定检查点和权重路径
        checkpoint_path = sys.argv[1]
        weights_path = sys.argv[2]
    else:
        print("使用方法:")
        print("  python simple_transformer_loader.py")
        print("  python simple_transformer_loader.py <checkpoint_path>")
        print("  python simple_transformer_loader.py <checkpoint_path> <weights_path>")
        sys.exit(1)
    
    try:
        # 加载模型
        transformer = load_transformer_from_checkpoint(checkpoint_path, weights_path)
        
        if transformer is not None:
            # 测试模型
            test_transformer(transformer)
            
            # 保存独立模型（可选）
            save_standalone_model(transformer, "./loaded_transformer_model.pth")
            
            print(f"\n🎉 TransformerForDiffusion加载完成!")
            print(f"你现在可以使用这个模型进行推理或进一步训练。")
            
            return transformer
        else:
            print("❌ 模型加载失败")
            return None
            
    except Exception as e:
        print(f"❌ 加载过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    transformer = main() 