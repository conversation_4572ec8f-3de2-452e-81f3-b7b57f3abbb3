#!/usr/bin/env python3
"""
模型结构可视化工具
==================
生成模型的层次结构图，用于更直观地了解模型架构
"""

import sys
import json
from pathlib import Path
from collections import defaultdict, OrderedDict
import torch
from safetensors import safe_open

def create_tree_structure(keys):
    """根据权重键名创建树形结构"""
    tree = defaultdict(lambda: defaultdict(list))
    
    for key in keys:
        parts = key.split('.')
        if len(parts) >= 2:
            main_module = parts[0]
            sub_module = parts[1] if len(parts) > 1 else 'root'
            tree[main_module][sub_module].append(key)
    
    return tree

def print_tree_structure(tree, weights_info):
    """打印树形结构"""
    print("🌳 模型层次结构:")
    print("=" * 100)
    
    for main_module, sub_modules in sorted(tree.items()):
        # 计算主模块参数
        main_params = sum(weights_info[key]['params'] for sub_layers in sub_modules.values() 
                         for key in sub_layers if key in weights_info)
        main_size = sum(weights_info[key]['size'] for sub_layers in sub_modules.values() 
                       for key in sub_layers if key in weights_info)
        
        print(f"📦 {main_module}")
        print(f"   └── 参数数量: {main_params:,} ({main_params/1e6:.1f}M)")
        print(f"   └── 内存占用: {format_bytes(main_size)}")
        print()
        
        for sub_module, layers in sorted(sub_modules.items()):
            if len(layers) > 0:
                sub_params = sum(weights_info[key]['params'] for key in layers if key in weights_info)
                sub_size = sum(weights_info[key]['size'] for key in layers if key in weights_info)
                
                print(f"   ├── 📁 {sub_module}")
                print(f"   │   ├── 参数数量: {sub_params:,}")
                print(f"   │   ├── 内存占用: {format_bytes(sub_size)}")
                print(f"   │   └── 层数: {len(layers)}")
                
                # 显示具体的层（只显示前几个）
                shown_layers = layers[:3]
                for i, layer in enumerate(shown_layers):
                    if layer in weights_info:
                        info = weights_info[layer]
                        prefix = "   │   │   ├──" if i < len(shown_layers) - 1 else "   │   │   └──"
                        layer_name = layer.split('.')[-1] if '.' in layer else layer
                        print(f"{prefix} {layer_name}: {tuple(info['shape'])} ({info['params']:,} params)")
                
                if len(layers) > 3:
                    print(f"   │   │   └── ... 还有 {len(layers) - 3} 个层")
                print()

def format_bytes(bytes_val):
    """将字节数转换为可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_val < 1024.0:
            return f"{bytes_val:.2f} {unit}"
        bytes_val /= 1024.0
    return f"{bytes_val:.2f} TB"

def analyze_transformer_structure(keys, weights_info):
    """分析Transformer结构"""
    print("🔄 Transformer 架构分析:")
    print("=" * 80)
    
    # 分析编码器
    encoder_layers = [k for k in keys if 'encoder.layers' in k]
    if encoder_layers:
        encoder_nums = set()
        for key in encoder_layers:
            parts = key.split('.')
            for i, part in enumerate(parts):
                if part == 'layers' and i+1 < len(parts) and parts[i+1].isdigit():
                    encoder_nums.add(int(parts[i+1]))
        
        print(f"📥 编码器 (Encoder):")
        print(f"   ├── 层数: {len(encoder_nums)}")
        if encoder_nums:
            # 分析单层结构
            sample_layer = min(encoder_nums)
            layer_keys = [k for k in encoder_layers if f'layers.{sample_layer}.' in k]
            layer_params = sum(weights_info[k]['params'] for k in layer_keys if k in weights_info)
            print(f"   ├── 每层参数: {layer_params:,}")
            print(f"   └── 总参数: {layer_params * len(encoder_nums):,}")
            
            # 显示层内结构
            components = defaultdict(int)
            for key in layer_keys:
                parts = key.split('.')
                for i, part in enumerate(parts):
                    if part == str(sample_layer) and i+1 < len(parts):
                        component = parts[i+1]
                        components[component] += weights_info[key]['params'] if key in weights_info else 0
            
            print(f"   单层组件:")
            for comp, params in sorted(components.items()):
                print(f"      ├── {comp}: {params:,} 参数")
        print()
    
    # 分析解码器
    decoder_layers = [k for k in keys if 'decoder.layers' in k]
    if decoder_layers:
        decoder_nums = set()
        for key in decoder_layers:
            parts = key.split('.')
            for i, part in enumerate(parts):
                if part == 'layers' and i+1 < len(parts) and parts[i+1].isdigit():
                    decoder_nums.add(int(parts[i+1]))
        
        print(f"📤 解码器 (Decoder):")
        print(f"   ├── 层数: {len(decoder_nums)}")
        if decoder_nums:
            # 分析单层结构
            sample_layer = min(decoder_nums)
            layer_keys = [k for k in decoder_layers if f'layers.{sample_layer}.' in k]
            layer_params = sum(weights_info[k]['params'] for k in layer_keys if k in weights_info)
            print(f"   ├── 每层参数: {layer_params:,}")
            print(f"   └── 总参数: {layer_params * len(decoder_nums):,}")
            
            # 显示层内结构
            components = defaultdict(int)
            for key in layer_keys:
                parts = key.split('.')
                for i, part in enumerate(parts):
                    if part == str(sample_layer) and i+1 < len(parts):
                        component = parts[i+1]
                        components[component] += weights_info[key]['params'] if key in weights_info else 0
            
            print(f"   单层组件:")
            for comp, params in sorted(components.items()):
                print(f"      ├── {comp}: {params:,} 参数")
        print()

def analyze_vision_encoder(keys, weights_info):
    """分析视觉编码器结构"""
    vision_keys = [k for k in keys if 'rgb_encoder' in k]
    if not vision_keys:
        return
    
    print("👁️  视觉编码器分析:")
    print("=" * 80)
    
    # 按backbone层分组
    backbone_layers = defaultdict(list)
    for key in vision_keys:
        if 'backbone' in key:
            parts = key.split('.')
            for i, part in enumerate(parts):
                if part == 'backbone' and i+1 < len(parts):
                    layer_num = parts[i+1]
                    backbone_layers[layer_num].append(key)
    
    total_vision_params = sum(weights_info[k]['params'] for k in vision_keys if k in weights_info)
    print(f"📊 总参数数量: {total_vision_params:,}")
    print(f"🏗️  骨干网络结构:")
    
    for layer_num in sorted(backbone_layers.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
        layer_keys = backbone_layers[layer_num]
        layer_params = sum(weights_info[k]['params'] for k in layer_keys if k in weights_info)
        
        # 尝试识别层类型
        layer_type = "Unknown"
        if any('conv' in k for k in layer_keys):
            layer_type = "卷积层"
        elif any('bn' in k for k in layer_keys):
            layer_type = "批标准化层"
        elif any('downsample' in k for k in layer_keys):
            layer_type = "下采样层"
        
        print(f"   ├── 层 {layer_num} ({layer_type}): {layer_params:,} 参数")
    
    # 分析其他组件
    other_components = defaultdict(int)
    for key in vision_keys:
        if 'backbone' not in key:
            parts = key.split('.')
            component = parts[-2] if len(parts) > 1 else 'other'
            other_components[component] += weights_info[key]['params'] if key in weights_info else 0
    
    if other_components:
        print(f"🔧 其他组件:")
        for comp, params in sorted(other_components.items()):
            print(f"   ├── {comp}: {params:,} 参数")
    print()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        model_path = "outputs/train/diffusion_pusht_transformer_edit/checkpoints/last/pretrained_model/model.safetensors"
        config_path = "outputs/train/diffusion_pusht_transformer_edit/checkpoints/last/pretrained_model/config.json"
    else:
        model_path = sys.argv[1]
        config_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(model_path).exists():
        print(f"❌ 错误: 模型文件不存在: {model_path}")
        return
    
    print("🔍 详细模型结构可视化")
    print("=" * 100)
    print(f"📁 模型文件: {model_path}")
    print()
    
    # 读取配置信息
    if config_path and Path(config_path).exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
        print("📋 模型配置:")
        key_configs = ['type', 'use_transformer', 'n_layer', 'n_head', 'diffusion_step_embed_dim']
        for key in key_configs:
            if key in config:
                print(f"   ├── {key}: {config[key]}")
        print()
    
    # 加载权重信息
    weights_info = {}
    with safe_open(model_path, framework="pt", device="cpu") as f:
        keys = sorted(f.keys())
        for key in keys:
            tensor = f.get_tensor(key)
            weights_info[key] = {
                'shape': tuple(tensor.shape),
                'params': tensor.numel(),
                'size': tensor.nelement() * tensor.element_size()
            }
    
    # 创建并显示树形结构
    tree = create_tree_structure(keys)
    print_tree_structure(tree, weights_info)
    
    # 分析Transformer结构
    analyze_transformer_structure(keys, weights_info)
    
    # 分析视觉编码器
    analyze_vision_encoder(keys, weights_info)
    
    # 总结
    total_params = sum(info['params'] for info in weights_info.values())
    total_size = sum(info['size'] for info in weights_info.values())
    
    print("📊 模型总结:")
    print("=" * 50)
    print(f"🔢 总参数数量: {total_params:,} ({total_params/1e6:.1f}M)")
    print(f"💾 模型大小: {format_bytes(total_size)}")
    print(f"🏗️  权重张量数: {len(keys)}")
    print(f"📦 主要模块数: {len(tree)}")

if __name__ == "__main__":
    main() 