#!/usr/bin/env python

"""
测试脚本：验证GradScaler修复
"""

import torch
import torch.nn as nn
from torch.cuda.amp import autocast, GradScaler

def test_gradscaler_patterns():
    """测试不同的GradScaler使用模式"""
    
    print("🧪 测试GradScaler使用模式...")
    
    # 创建简单模型
    model = nn.Linear(10, 1).cuda()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    scaler = GradScaler()
    
    # 创建模拟数据
    x = torch.randn(32, 10).cuda()
    y = torch.randn(32, 1).cuda()
    
    print("\n1️⃣ 测试模式1：不使用梯度裁剪")
    try:
        optimizer.zero_grad()
        
        with autocast():
            output = model(x)
            loss = nn.MSELoss()(output, y)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        
        print("✅ 模式1成功：不使用梯度裁剪")
    except Exception as e:
        print(f"❌ 模式1失败: {e}")
    
    print("\n2️⃣ 测试模式2：使用梯度裁剪")
    try:
        optimizer.zero_grad()
        
        with autocast():
            output = model(x)
            loss = nn.MSELoss()(output, y)
        
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        scaler.step(optimizer)
        scaler.update()
        
        print("✅ 模式2成功：使用梯度裁剪")
    except Exception as e:
        print(f"❌ 模式2失败: {e}")
    
    print("\n3️⃣ 测试模式3：错误模式（只unscale不clip）")
    try:
        optimizer.zero_grad()
        
        with autocast():
            output = model(x)
            loss = nn.MSELoss()(output, y)
        
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)  # 只unscale但不做任何操作
        scaler.step(optimizer)
        scaler.update()
        
        print("❌ 模式3不应该成功")
    except Exception as e:
        print(f"✅ 模式3正确失败: {e}")
    
    print("\n📋 总结:")
    print("✅ 推荐模式1：不需要梯度裁剪时，直接scale().backward() -> step() -> update()")
    print("✅ 推荐模式2：需要梯度裁剪时，scale().backward() -> unscale_() -> clip_grad_norm_() -> step() -> update()")
    print("❌ 避免模式3：不要只调用unscale_()而不进行梯度裁剪")


def test_train_dc_pattern():
    """测试train_dc_with_dataset.py中的修复模式"""
    
    print("\n🔧 测试train_dc_with_dataset.py的修复模式...")
    
    # 模拟train_dc_with_dataset.py中的代码模式
    model = nn.Linear(10, 1).cuda()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    scaler = GradScaler()
    
    x = torch.randn(32, 10).cuda()
    y = torch.randn(32, 1).cuda()
    
    # 测试修复后的模式
    use_grad_clip = True  # 对应修复后的代码
    
    try:
        optimizer.zero_grad()
        
        with autocast():
            output = model(x)
            loss = nn.MSELoss()(output, y)
        
        scaler.scale(loss).backward()
        
        # 修复后的梯度裁剪逻辑
        if use_grad_clip:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        print("✅ 修复后的模式成功 (use_grad_clip=True)")
    except Exception as e:
        print(f"❌ 修复后的模式失败: {e}")
    
    # 测试不使用梯度裁剪的情况
    use_grad_clip = False
    
    try:
        optimizer.zero_grad()
        
        with autocast():
            output = model(x)
            loss = nn.MSELoss()(output, y)
        
        scaler.scale(loss).backward()
        
        # 修复后的梯度裁剪逻辑
        if use_grad_clip:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        print("✅ 修复后的模式成功 (use_grad_clip=False)")
    except Exception as e:
        print(f"❌ 修复后的模式失败: {e}")


if __name__ == "__main__":
    if torch.cuda.is_available():
        print("🚀 开始测试GradScaler修复...")
        test_gradscaler_patterns()
        test_train_dc_pattern()
        
        print("\n🎉 测试完成!")
        print("\n💡 修复说明:")
        print("1. 原问题：调用了scaler.unscale_()但没有进行梯度裁剪")
        print("2. 修复方案：添加条件判断，只在需要梯度裁剪时调用unscale_()")
        print("3. 现在可以通过use_grad_clip变量控制是否使用梯度裁剪")
        print("4. 建议：对于DC tokens训练，可以尝试两种模式看哪种效果更好")
    else:
        print("❌ 需要CUDA支持才能测试GradScaler")
