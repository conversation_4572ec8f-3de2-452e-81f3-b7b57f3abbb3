#!/usr/bin/env python3
"""
修正版本的 DiffusionModel_DC .pth 权重评估脚本

解决了以下问题：
1. 正确处理包含 model_state_dict 的权重文件
2. 处理缺失的归一化统计信息
3. 提供更好的错误诊断和处理
"""

import torch
from pathlib import Path
import json
import sys

def inspect_pth_file(pth_path: str):
    """
    检查 .pth 文件的结构
    """
    print(f"🔍 检查 .pth 文件: {pth_path}")
    
    checkpoint = torch.load(pth_path, map_location='cpu')
    
    print("=" * 50)
    print(f"文件类型: {type(checkpoint)}")
    
    if isinstance(checkpoint, dict):
        print(f"顶级键: {list(checkpoint.keys())}")
        
        for key, value in checkpoint.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: Tensor {value.shape}")
            elif isinstance(value, dict):
                print(f"  {key}: Dict with {len(value)} items")
                if key == 'model_state_dict':
                    print(f"    模型权重数量: {len(value)}")
                    weight_keys = list(value.keys())
                    print(f"    权重键示例: {weight_keys[:3]}...")
            else:
                print(f"  {key}: {type(value)} = {value}")
    else:
        print(f"直接包含 {len(checkpoint)} 个参数")
    
    print("=" * 50)


def main():
    """
    主函数
    """
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--inspect":
        if len(sys.argv) > 2:
            inspect_pth_file(sys.argv[2])
        else:
            print("请提供 .pth 文件路径")
        return
    
    print("🔧 DiffusionModel_DC .pth 权重问题诊断工具")
    print("=" * 50)
    
    # 检查常见的权重文件
    common_paths = [
        "dc_eval_setup/model.pth",
        "dc_checkpoints/dc_model_final.pth",
        "dc_checkpoints/dc_checkpoint_step_100000.pth"
    ]
    
    found_files = []
    for path in common_paths:
        if Path(path).exists():
            found_files.append(path)
    
    if found_files:
        print("📁 找到的 .pth 文件:")
        for i, path in enumerate(found_files, 1):
            print(f"  {i}. {path}")
        
        print(f"\n🔍 检查第一个文件: {found_files[0]}")
        inspect_pth_file(found_files[0])
    else:
        print("❌ 未找到常见的 .pth 文件")
    
    print("\n🛠️  问题诊断:")
    print("-" * 40)
    print("根据您的错误信息，问题是：")
    print("1. 权重文件只包含 6 个顶级键，实际模型权重在 'model_state_dict' 中")
    print("2. 缺失归一化统计信息（min, max, mean, std）")
    print("3. 需要修正权重加载逻辑")
    
    print("\n💡 解决方案:")
    print("1. 修正 eval.py 中的权重提取逻辑")
    print("2. 从训练过程中获取归一化统计信息")
    print("3. 或创建包含完整信息的检查点")


if __name__ == "__main__":
    main()
