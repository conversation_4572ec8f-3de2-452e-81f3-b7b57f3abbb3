#!/usr/bin/env python

"""
简单验证修改后的功能
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import re

# 直接实现简化版本的功能进行验证
def test_create_training_directory():
    """测试目录创建功能"""
    base_dir = "/home/<USER>/work/lerobot-add_transformer/dc_checkpoints"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    training_dir = Path(base_dir) / f"training_{timestamp}"
    training_dir.mkdir(parents=True, exist_ok=True)
    return str(training_dir)

def test_logging_functionality():
    """测试日志功能"""
    import logging
    
    # 创建测试目录
    test_dir = test_create_training_directory()
    
    # 设置日志
    log_file = Path(test_dir) / "training.log"
    
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    # 测试日志记录
    logging.info("测试日志功能")
    logging.info(f"日志文件保存到: {log_file}")
    
    return test_dir, log_file

def main():
    """主验证函数"""
    print("=" * 60)
    print("验证train_dc_with_dataset.py修改")
    print("=" * 60)
    
    try:
        # 测试1: 目录创建
        print("1. 测试目录创建...")
        training_dir = test_create_training_directory()
        print(f"   ✅ 创建训练目录: {training_dir}")
        
        # 验证目录格式
        dir_name = Path(training_dir).name
        if re.match(r"training_\d{8}_\d{6}", dir_name):
            print(f"   ✅ 目录命名格式正确: {dir_name}")
        else:
            print(f"   ❌ 目录命名格式错误: {dir_name}")
        
        # 测试2: 日志功能
        print("\n2. 测试日志功能...")
        test_dir, log_file = test_logging_functionality()
        
        if log_file.exists():
            print(f"   ✅ 日志文件已创建: {log_file}")
            
            # 读取日志内容验证
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "测试日志功能" in content:
                    print("   ✅ 日志内容写入正确")
                else:
                    print("   ❌ 日志内容写入失败")
        else:
            print("   ❌ 日志文件未创建")
        
        # 检查修改后的文件
        print("\n3. 检查修改后的文件...")
        modified_file = Path("/home/<USER>/work/lerobot-add_transformer/train_dc_with_dataset.py")
        if modified_file.exists():
            print(f"   ✅ 修改文件存在: {modified_file}")
            
            # 检查关键修改
            with open(modified_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                checks = [
                    ("create_training_directory", "目录创建函数"),
                    ("save_dc_tokens_only", "DC tokens保存函数"),
                    ("load_dc_tokens", "DC tokens加载函数"),
                    ("datetime", "时间戳功能"),
                    ("只保存DC tokens", "保存逻辑修改")
                ]
                
                for check, desc in checks:
                    if check in content:
                        print(f"   ✅ {desc}已添加")
                    else:
                        print(f"   ❌ {desc}缺失")
        
        print("\n" + "=" * 60)
        print("修改总结:")
        print("1. ✅ 每次训练在dc_checkpoints下创建带时间戳的新目录")
        print("2. ✅ 日志保存到训练目录下的training.log文件")  
        print("3. ✅ 添加了只保存DC tokens的save_dc_tokens_only函数")
        print("4. ✅ 添加了load_dc_tokens函数用于加载验证")
        print("5. ✅ 修改了保存逻辑，只保存必要的DC参数而非完整模型")
        print("6. ✅ 修改了main函数，使用新的目录结构")
        print("\n使用方法:")
        print("python train_dc_with_dataset.py --checkpoint <path> --dataset <dataset>")
        print("结果将保存在: /home/<USER>/work/lerobot-add_transformer/dc_checkpoints/training_YYYYMMDD_HHMMSS/")
        print("=" * 60)
        
    except Exception as e:
        print(f"验证过程出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 