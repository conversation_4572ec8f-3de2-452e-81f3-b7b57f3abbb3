#!/bin/bash

# LeRobot格式DC tokens训练脚本
# 使用safetensors保存，每5000步保存一次检查点

cd /home/<USER>/work/lerobot-add_transformer

echo "=== LeRobot格式DC Tokens训练 ==="
echo "保存格式: safetensors"
echo "保存频率: 每5000步"
echo "输出目录: outputs/train/"

python train_dc_with_lerobot_format.py \
    --checkpoint /home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000 \
    --dataset lerobot/pusht \
    --n_dc_tokens 4 \
    --n_dc_layers 6 \
    --device cuda \
    --batch_size 64 \
    --learning_rate 1e-4 \
    --steps 10000 \
    --log_freq 100 \
    --save_freq 5000 \
    --output_dir /home/<USER>/work/lerobot-add_transformer/outputs \
    --experiment_name diffusion_pusht_dc_safetensors

echo "=== 训练完成 ==="
echo "检查保存的文件结构:"
echo "outputs/train/diffusion_pusht_dc_safetensors/checkpoints/"
echo "  ├── 005000/"
echo "  │   ├── pretrained_model/"
echo "  │   │   ├── model.safetensors"
echo "  │   │   ├── config.json"
echo "  │   │   └── train_config.json"
echo "  │   └── training_state/"
echo "  │       ├── optimizer_state.safetensors"
echo "  │       ├── optimizer_param_groups.json"
echo "  │       ├── scheduler_state.json"
echo "  │       ├── training_step.json"
echo "  │       └── rng_state.safetensors"
echo "  ├── 010000/"
echo "  │   └── ..."
echo "  └── last -> 010000"
