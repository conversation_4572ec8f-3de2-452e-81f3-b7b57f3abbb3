#!/usr/bin/env python3
"""
模型权重架构分析工具
====================
用于分析LeRobot训练产生的safetensors模型文件的权重结构
"""

import sys
import json
from pathlib import Path
from collections import defaultdict, OrderedDict
import torch
from safetensors import safe_open

def format_bytes(bytes_val):
    """将字节数转换为可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_val < 1024.0:
            return f"{bytes_val:.2f} {unit}"
        bytes_val /= 1024.0
    return f"{bytes_val:.2f} TB"

def count_parameters(tensor):
    """计算张量中的参数数量"""
    return tensor.numel()

def analyze_model_weights(model_path, config_path=None):
    """分析模型权重结构"""
    
    print("=" * 80)
    print("🔍 模型权重架构分析")
    print("=" * 80)
    print(f"📁 模型文件: {model_path}")
    
    # 读取配置文件
    if config_path and Path(config_path).exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
        print(f"📄 配置文件: {config_path}")
        print(f"🏗️  模型类型: {config.get('type', 'Unknown')}")
        if config.get('use_transformer'):
            print(f"🔄 Transformer层数: {config.get('n_layer', 'Unknown')}")
            print(f"🎯 注意力头数: {config.get('n_head', 'Unknown')}")
            print(f"📐 扩散步骤嵌入维度: {config.get('diffusion_step_embed_dim', 'Unknown')}")
        print()
    
    # 分析权重文件
    total_params = 0
    total_size = 0
    layer_groups = defaultdict(list)
    
    print("🔬 权重张量详细信息:")
    print("-" * 80)
    print(f"{'名称':<60} {'形状':<20} {'参数数量':<15} {'内存占用'}")
    print("-" * 80)
    
    with safe_open(model_path, framework="pt", device="cpu") as f:
        # 获取所有键并排序
        keys = sorted(f.keys())
        
        for key in keys:
            tensor = f.get_tensor(key)
            params = count_parameters(tensor)
            size = tensor.nelement() * tensor.element_size()
            
            total_params += params
            total_size += size
            
            # 按模块分组
            if '.' in key:
                module_name = key.split('.')[0]
                layer_groups[module_name].append((key, tensor.shape, params, size))
            
            print(f"{key:<60} {str(tuple(tensor.shape)):<20} {params:<15,} {format_bytes(size)}")
    
    print("-" * 80)
    print(f"{'总计':<60} {'':<20} {total_params:<15,} {format_bytes(total_size)}")
    print()
    
    # 按模块分组统计
    print("📊 按模块分组统计:")
    print("-" * 60)
    print(f"{'模块名称':<30} {'参数数量':<15} {'内存占用':<15}")
    print("-" * 60)
    
    for module_name, tensors in sorted(layer_groups.items()):
        module_params = sum(params for _, _, params, _ in tensors)
        module_size = sum(size for _, _, _, size in tensors)
        percentage = (module_params / total_params) * 100
        print(f"{module_name:<30} {module_params:<15,} {format_bytes(module_size):<15} ({percentage:.1f}%)")
    
    print("-" * 60)
    print(f"{'总计':<30} {total_params:<15,} {format_bytes(total_size)}")
    print()
    
    # 模型架构概要
    print("🏗️  模型架构概要:")
    print("-" * 40)
    print(f"📊 总参数数量: {total_params:,} ({total_params/1e6:.1f}M)")
    print(f"💾 模型大小: {format_bytes(total_size)}")
    print(f"🔧 模块数量: {len(layer_groups)}")
    
    # 特殊层分析
    transformer_layers = [k for k in keys if 'transformer' in k.lower() or 'attention' in k.lower()]
    if transformer_layers:
        print(f"🔄 Transformer相关层: {len(transformer_layers)}")
    
    vision_layers = [k for k in keys if any(vision_keyword in k.lower() for vision_keyword in ['vision', 'conv', 'resnet', 'backbone'])]
    if vision_layers:
        print(f"👁️  视觉相关层: {len(vision_layers)}")
    
    diffusion_layers = [k for k in keys if any(diff_keyword in k.lower() for diff_keyword in ['diffusion', 'noise', 'timestep'])]
    if diffusion_layers:
        print(f"🌊 扩散相关层: {len(diffusion_layers)}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        # 默认路径
        model_path = "outputs/train/diffusion_pusht_transformer_edit/checkpoints/last/pretrained_model/model.safetensors"
        config_path = "outputs/train/diffusion_pusht_transformer_edit/checkpoints/last/pretrained_model/config.json"
    else:
        model_path = sys.argv[1]
        config_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(model_path).exists():
        print(f"❌ 错误: 模型文件不存在: {model_path}")
        print("💡 用法: python analyze_model_architecture.py [model_path] [config_path]")
        return
    
    try:
        analyze_model_weights(model_path, config_path)
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 