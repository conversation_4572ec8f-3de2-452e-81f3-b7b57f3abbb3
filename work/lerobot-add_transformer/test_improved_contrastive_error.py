#!/usr/bin/env python3
"""
测试改进后的compute_contrastive_error方法

验证基于lerobot_dc_loss的改进是否有效
"""

import torch
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss
)
from lerobot.configs.policies import PreTrainedConfig


def test_contrastive_error_methods():
    """测试不同的对比误差计算方法"""
    print("🧪 测试改进后的compute_contrastive_error方法")
    
    # 检查预训练模型路径
    checkpoint_paths = [
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if Path(path).exists():
            checkpoint_path = path
            break
    
    if checkpoint_path is None:
        print("❌ 未找到预训练模型检查点")
        return False
    
    try:
        print(f"📁 使用检查点: {checkpoint_path}")
        
        # 加载配置和模型
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        model.eval()
        print("✅ 模型加载成功!")
        
        # 测试不同批次大小
        batch_sizes = [2, 4, 8, 16]
        
        for batch_size in batch_sizes:
            print(f"\n📊 测试批次大小: {batch_size}")
            
            # 创建测试数据（匹配预训练模型的维度）
            batch = {
                'action': torch.randn(batch_size, 16, 2),
                'observation.state': torch.randn(batch_size, 2, 2),  # 2维状态匹配pusht
                'observation.image': torch.randn(batch_size, 2, 3, 96, 96),  # 添加图像观察
            }
            
            # 测试计算时间和结果
            start_time = time.time()
            
            with torch.no_grad():
                error_matrix = model.compute_contrastive_error(batch, use_dc=True)
            
            end_time = time.time()
            
            print(f"   ⏱️ 计算时间: {end_time - start_time:.4f}秒")
            print(f"   📐 误差矩阵形状: {error_matrix.shape}")
            print(f"   📈 对角线误差均值: {torch.diag(error_matrix).mean():.4f}")
            print(f"   📉 非对角线误差均值: {error_matrix[~torch.eye(batch_size, dtype=bool)].mean():.4f}")
            
            # 测试对比损失
            contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
            loss, diagnostics = contrastive_loss.forward_with_diagnostics(error_matrix)
            
            print(f"   🔥 对比损失: {loss.item():.4f}")
            print(f"   🎯 对比准确率: {diagnostics['contrastive_accuracy']:.4f}")
            print(f"   📊 误差分离度: {diagnostics['error_separation']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_contrastive_loss_improvements():
    """测试改进后的ContrastiveLoss功能"""
    print("\n🧪 测试改进后的ContrastiveLoss功能")
    
    try:
        # 创建测试误差矩阵
        batch_size = 4
        
        # 理想情况：对角线误差小，非对角线误差大
        error_matrix = torch.randn(batch_size, batch_size) + 2.0  # 基础误差
        diagonal_indices = torch.arange(batch_size)
        error_matrix[diagonal_indices, diagonal_indices] -= 1.5  # 对角线误差更小
        
        print(f"📐 测试误差矩阵:\n{error_matrix}")
        
        # 测试对比损失
        contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
        
        # 基本损失计算
        loss = contrastive_loss(error_matrix)
        print(f"🔥 对比损失: {loss.item():.4f}")
        
        # 准确率计算
        accuracy = contrastive_loss.compute_accuracy(error_matrix)
        print(f"🎯 对比准确率: {accuracy.item():.4f}")
        
        # 诊断信息
        diagnostics = contrastive_loss.get_diagnostics(error_matrix)
        print("📊 诊断信息:")
        for key, value in diagnostics.items():
            print(f"   {key}: {value:.4f}")
        
        # 测试带诊断的前向传播
        loss_with_diag, diag_dict = contrastive_loss.forward_with_diagnostics(error_matrix)
        print(f"🔥 带诊断的损失: {loss_with_diag.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ ContrastiveLoss测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_efficiency():
    """测试内存效率"""
    print("\n🧪 测试内存效率")
    
    try:
        # 创建大批次测试数据
        large_batch_size = 32
        batch = {
            'action': torch.randn(large_batch_size, 16, 2),
            'observation.state': torch.randn(large_batch_size, 2, 64),
        }
        
        print(f"📊 大批次测试 (batch_size={large_batch_size})")
        
        # 检查内存使用
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated()
            print(f"   💾 初始GPU内存: {initial_memory / 1024**2:.2f} MB")
        
        # 模拟计算（不需要真实模型）
        print("   ⚡ 模拟大批次对比误差计算...")
        
        # 创建模拟误差矩阵
        error_matrix = torch.randn(large_batch_size, large_batch_size)
        
        # 测试对比损失计算
        contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
        loss = contrastive_loss(error_matrix)
        
        print(f"   🔥 大批次对比损失: {loss.item():.4f}")
        
        if torch.cuda.is_available():
            final_memory = torch.cuda.memory_allocated()
            memory_used = (final_memory - initial_memory) / 1024**2
            print(f"   💾 内存使用: {memory_used:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存效率测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 改进后的compute_contrastive_error测试\n")
    
    tests = [
        ("对比误差计算方法", test_contrastive_error_methods),
        ("ContrastiveLoss改进", test_contrastive_loss_improvements),
        ("内存效率", test_memory_efficiency),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print("=" * 60)
        success = test_func()
        results.append((test_name, success))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！改进后的compute_contrastive_error工作正常")
        print("\n✨ 改进亮点:")
        print("   ✅ 基于lerobot_dc_loss的高效实现")
        print("   ✅ 小批次使用循环方法（内存友好）")
        print("   ✅ 大批次使用向量化方法（计算高效）")
        print("   ✅ 增强的ContrastiveLoss诊断功能")
        print("   ✅ 数值稳定性改进")
        print("   ✅ 准确率和诊断指标")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
