#!/usr/bin/env python

"""
Enhanced evaluation script that:
1. <PERSON>ads base model from safetensors (from standard checkpoint)
2. Enables DC functionality by modifying config
3. Loads DC-specific weights from .pth file
4. Runs evaluation with the combined model
"""

import argparse
import json
import logging
import os
import torch
from pathlib import Path
from typing import Dict, Any

from lerobot.scripts.eval import main as original_eval_main
from lerobot.common.policies.factory import make_policy
from lerobot.common.utils.utils import init_hydra_config
from lerobot.configs.eval import EvalPipelineConfig

def load_pth_structure(pth_path: str, device: str = "cpu") -> Dict[str, Any]:
    """Load and analyze .pth file structure"""
    print(f"[加载PTH文件] {pth_path}")
    pth_data = torch.load(pth_path, map_location=device)
    
    if isinstance(pth_data, dict) and 'model_state_dict' in pth_data:
        print("[PTH结构] 检测到训练检查点格式，提取model_state_dict")
        model_weights = pth_data['model_state_dict']
    else:
        print("[PTH结构] 直接的模型权重格式")
        model_weights = pth_data
    
    # 分析DC相关权重
    dc_keys = [k for k in model_weights.keys() if 'dc' in k.lower()]
    print(f"[DC权重] 找到 {len(dc_keys)} 个DC相关的键:")
    for key in dc_keys[:10]:  # 只显示前10个
        print(f"  - {key}: {model_weights[key].shape}")
    if len(dc_keys) > 10:
        print(f"  ... 还有 {len(dc_keys) - 10} 个DC权重")
    
    return model_weights

def modify_config_for_dc(config_path: str, backup: bool = True) -> str:
    """修改配置文件以启用DC功能"""
    if backup:
        backup_path = config_path + ".backup"
        if not os.path.exists(backup_path):
            os.system(f"cp {config_path} {backup_path}")
            print(f"[备份配置] 已备份到 {backup_path}")
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # 启用DC功能
    original_use_dc = config.get('use_dc', False)
    config['use_dc'] = True
    config['use_mmdit_dc'] = False  # 我们使用TransformerForDiffusion_DC而不是MMDiT_DC
    
    # 确保DC参数存在
    config.setdefault('n_dc_tokens', 7)
    config.setdefault('n_dc_layers', 6) 
    config.setdefault('use_dc_t', True)
    
    print(f"[配置修改] use_dc: {original_use_dc} -> {config['use_dc']}")
    print(f"[配置修改] DC参数: n_dc_tokens={config['n_dc_tokens']}, n_dc_layers={config['n_dc_layers']}, use_dc_t={config['use_dc_t']}")
    
    # 保存修改后的配置
    temp_config_path = config_path + ".dc_enabled"
    with open(temp_config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    print(f"[配置保存] DC启用的配置保存到 {temp_config_path}")
    return temp_config_path

def load_dc_weights_to_model(policy, pth_weights: Dict[str, torch.Tensor], device: str):
    """将DC权重加载到已初始化的模型中"""
    print(f"[加载DC权重] 开始将DC权重加载到模型...")
    
    # 检查模型是否有DC功能
    if not hasattr(policy.model.net, 'dc_tokens'):
        print("[错误] 模型没有DC tokens，请确认use_dc=True")
        return False
    
    # 过滤出DC相关的权重
    dc_weights = {}
    for key, weight in pth_weights.items():
        if any(dc_key in key.lower() for dc_key in ['dc_tokens', 'dc_t_tokens']):
            dc_weights[key] = weight
    
    print(f"[DC权重过滤] 找到 {len(dc_weights)} 个DC相关权重:")
    for key, weight in dc_weights.items():
        print(f"  - {key}: {weight.shape}")
    
    # 加载DC权重
    loaded_count = 0
    for key, weight in dc_weights.items():
        # 去掉可能的前缀
        clean_key = key
        for prefix in ['model.', 'net.', 'policy.', 'diffusion_model.']:
            if clean_key.startswith(prefix):
                clean_key = clean_key[len(prefix):]
        
        # 尝试加载到模型中
        try:
            if hasattr(policy.model.net, clean_key):
                target_param = getattr(policy.model.net, clean_key)
                if isinstance(target_param, torch.nn.Parameter):
                    if target_param.shape == weight.shape:
                        target_param.data.copy_(weight.to(device))
                        loaded_count += 1
                        print(f"  ✓ 成功加载: {clean_key}")
                    else:
                        print(f"  ✗ 形状不匹配: {clean_key} 期望{target_param.shape}, 得到{weight.shape}")
                else:
                    print(f"  ✗ 不是参数: {clean_key}")
            else:
                print(f"  ✗ 模型中不存在: {clean_key}")
        except Exception as e:
            print(f"  ✗ 加载失败 {clean_key}: {e}")
    
    print(f"[DC权重加载] 成功加载 {loaded_count}/{len(dc_weights)} 个DC权重")
    return loaded_count > 0

def main():
    parser = argparse.ArgumentParser(description="Enhanced evaluation with DC weights from PTH")
    parser.add_argument("--policy.path", type=str, required=True, 
                       help="Path to safetensors model directory")
    parser.add_argument("--dc_pth_path", type=str, required=True,
                       help="Path to DC weights .pth file")
    parser.add_argument("--env.type", type=str, default="pusht",
                       help="Environment type")
    parser.add_argument("--eval.batch_size", type=int, default=10,
                       help="Evaluation batch size")
    parser.add_argument("--eval.n_episodes", type=int, default=10,
                       help="Number of evaluation episodes")
    parser.add_argument("--policy.device", type=str, default="cuda",
                       help="Device for policy")
    parser.add_argument("--policy.use_amp", type=bool, default=False,
                       help="Use automatic mixed precision")
    
    args = parser.parse_args()
    
    # 1. 确认路径存在
    model_dir = Path(args.policy.path)
    if not model_dir.exists():
        raise FileNotFoundError(f"模型目录不存在: {model_dir}")
    
    config_path = model_dir / "config.json"
    safetensors_path = model_dir / "model.safetensors"
    
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    if not safetensors_path.exists():
        raise FileNotFoundError(f"模型权重文件不存在: {safetensors_path}")
    
    dc_pth_path = Path(args.dc_pth_path)
    if not dc_pth_path.exists():
        raise FileNotFoundError(f"DC权重文件不存在: {dc_pth_path}")
    
    print(f"[验证路径] 所有必需文件都存在")
    print(f"  - 配置文件: {config_path}")
    print(f"  - 基础权重: {safetensors_path}")
    print(f"  - DC权重: {dc_pth_path}")
    
    # 2. 修改配置以启用DC
    dc_config_path = modify_config_for_dc(str(config_path))
    
    # 3. 加载DC权重
    pth_weights = load_pth_structure(str(dc_pth_path), device=args.policy.device)
    
    # 4. 创建临时的评估配置
    temp_model_dir = model_dir.parent / f"{model_dir.name}_dc_temp"
    temp_model_dir.mkdir(exist_ok=True)
    
    # 复制文件到临时目录
    os.system(f"cp {safetensors_path} {temp_model_dir}/model.safetensors")
    os.system(f"cp {dc_config_path} {temp_model_dir}/config.json")
    
    # 5. 使用LeRobot的配置系统
    print(f"[初始化] 使用临时目录: {temp_model_dir}")
    
    # 构建eval命令参数
    eval_args = [
        f"--policy.pretrained_path={temp_model_dir}",
        f"--env.type={args.env.type}",
        f"--eval.batch_size={args.eval.batch_size}",
        f"--eval.n_episodes={args.eval.n_episodes}",
        f"--policy.device={args.policy.device}",
        f"--policy.use_amp={args.policy.use_amp}",
    ]
    
    print(f"[评估参数] {' '.join(eval_args)}")
    
    # 6. 手动初始化配置和policy
    try:
        # 使用hydra初始化配置
        cfg = init_hydra_config(EvalPipelineConfig, eval_args, job_name="eval_dc")
        
        # 创建policy
        print(f"[创建Policy] 使用配置创建policy...")
        policy = make_policy(cfg=cfg.policy, env_cfg=cfg.env)
        
        # 检查模型结构
        print(f"[模型检查] Policy类型: {type(policy)}")
        print(f"[模型检查] 模型类型: {type(policy.model)}")
        print(f"[模型检查] 网络类型: {type(policy.model.net)}")
        
        # 验证DC功能是否启用
        if hasattr(policy.model.net, 'dc_tokens'):
            print(f"[DC验证] ✓ 模型具有DC tokens: {policy.model.net.dc_tokens.shape}")
            if hasattr(policy.model.net, 'dc_t_tokens'):
                print(f"[DC验证] ✓ 模型具有DC-T tokens: {policy.model.net.dc_t_tokens.shape}")
            else:
                print(f"[DC验证] - 模型没有DC-T tokens")
        else:
            print(f"[DC验证] ✗ 模型没有DC tokens，DC功能未启用")
            return
        
        # 7. 加载DC权重到模型
        success = load_dc_weights_to_model(policy, pth_weights, args.policy.device)
        if not success:
            print(f"[警告] DC权重加载失败，继续使用随机初始化的DC权重")
        
        # 8. 运行评估（这里可以调用原始的eval逻辑或者自定义评估）
        print(f"[评估开始] 开始运行增强的DC模型评估...")
        
        # 你可以在这里添加实际的评估逻辑
        # 或者调用原始的evaluation pipeline
        
        print(f"[评估完成] DC增强模型评估完成")
        
    except Exception as e:
        print(f"[错误] 评估过程中出现错误: {e}")
        raise
    finally:
        # 清理临时文件
        if temp_model_dir.exists():
            os.system(f"rm -rf {temp_model_dir}")
            print(f"[清理] 已删除临时目录: {temp_model_dir}")

if __name__ == "__main__":
    main() 