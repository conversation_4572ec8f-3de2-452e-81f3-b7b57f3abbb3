#!/usr/bin/env python

"""
测试脚本：验证dc_t_tokens的None安全检查修复
"""

import sys
import logging

# 添加项目路径
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

def test_dc_t_none_safety():
    """测试dc_t_tokens为None时的安全性"""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
    
    try:
        # 导入必要的模块
        from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
        from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
        
        logging.info("✅ 模块导入成功")
        
        # 创建基本配置
        config = DiffusionConfig()
        config.horizon = 16
        config.n_obs_steps = 2
        config.diffusion_step_embed_dim = 256
        config.n_layer = 4
        config.n_head = 4
        config.n_cond_layers = 2
        config.use_transformer = True
        
        # 设置特征配置
        from types import SimpleNamespace
        config.input_shapes = {"observation.state": [32]}
        config.output_shapes = {"action": [2]}
        
        logging.info("✅ 配置创建成功")
        
        # 测试1: 创建不使用dc_t的模型
        logging.info("\n🧪 测试1: 创建不使用dc_t的DiffusionModel_DC")
        try:
            model_without_dc_t = DiffusionModel_DC(
                config=config,
                n_dc_tokens=4,
                n_dc_layers=3,
                use_dc_t=False,  # 关键：不使用dc_t
                cond_dim=32
            )
            logging.info("✅ 模型创建成功")
            
            # 测试freeze_base_model方法
            logging.info("🧪 测试freeze_base_model方法...")
            model_without_dc_t.freeze_base_model()
            logging.info("✅ freeze_base_model方法执行成功")
            
            # 测试get_dc_parameters方法
            logging.info("🧪 测试get_dc_parameters方法...")
            dc_params = model_without_dc_t.get_dc_parameters()
            logging.info(f"✅ get_dc_parameters方法执行成功，返回{len(dc_params)}个参数")
            
            # 测试initialize_dc_tokens方法
            logging.info("🧪 测试initialize_dc_tokens方法...")
            model_without_dc_t.initialize_dc_tokens()
            logging.info("✅ initialize_dc_tokens方法执行成功")
            
            # 测试check_dc_tokens_status方法
            logging.info("🧪 测试check_dc_tokens_status方法...")
            status = model_without_dc_t.check_dc_tokens_status()
            logging.info("✅ check_dc_tokens_status方法执行成功")
            logging.info(f"  dc_tokens_exist: {status['dc_tokens_exist']}")
            logging.info(f"  dc_t_tokens_exist: {status['dc_t_tokens_exist']}")
            
            # 测试get_dc_tokens_summary方法
            logging.info("🧪 测试get_dc_tokens_summary方法...")
            summary = model_without_dc_t.get_dc_tokens_summary()
            logging.info("✅ get_dc_tokens_summary方法执行成功")
            logging.info("摘要内容:")
            for line in summary.split('\n'):
                logging.info(f"  {line}")
            
        except Exception as e:
            logging.error(f"❌ 不使用dc_t的模型测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试2: 创建使用dc_t的模型进行对比
        logging.info("\n🧪 测试2: 创建使用dc_t的DiffusionModel_DC进行对比")
        try:
            model_with_dc_t = DiffusionModel_DC(
                config=config,
                n_dc_tokens=4,
                n_dc_layers=3,
                use_dc_t=True,  # 使用dc_t
                cond_dim=32
            )
            logging.info("✅ 使用dc_t的模型创建成功")
            
            # 测试相同的方法
            model_with_dc_t.freeze_base_model()
            dc_params_with_dc_t = model_with_dc_t.get_dc_parameters()
            model_with_dc_t.initialize_dc_tokens()
            status_with_dc_t = model_with_dc_t.check_dc_tokens_status()
            summary_with_dc_t = model_with_dc_t.get_dc_tokens_summary()
            
            logging.info("✅ 使用dc_t的模型所有方法执行成功")
            logging.info(f"  dc_params数量: {len(dc_params_with_dc_t)}")
            logging.info(f"  dc_t_tokens_exist: {status_with_dc_t['dc_t_tokens_exist']}")
            
        except Exception as e:
            logging.error(f"❌ 使用dc_t的模型测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试3: 参数数量对比
        logging.info("\n🧪 测试3: 参数数量对比")
        dc_params_without = model_without_dc_t.get_dc_parameters()
        dc_params_with = model_with_dc_t.get_dc_parameters()
        
        logging.info(f"不使用dc_t的DC参数数量: {len(dc_params_without)}")
        logging.info(f"使用dc_t的DC参数数量: {len(dc_params_with)}")
        logging.info(f"参数数量差异: {len(dc_params_with) - len(dc_params_without)}")
        
        if len(dc_params_with) > len(dc_params_without):
            logging.info("✅ 使用dc_t的模型确实有更多参数")
        else:
            logging.warning("⚠️ 参数数量差异不符合预期")
        
        logging.info("\n🎉 所有测试通过! dc_t_tokens的None安全检查修复成功。")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_dc_t_none_safety()
    if success:
        print("\n✅ 修复验证成功! 现在可以安全地使用--use_dc_t参数了。")
    else:
        print("\n❌ 修复验证失败! 请检查错误信息。")
        sys.exit(1)
