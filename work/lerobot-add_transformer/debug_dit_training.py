#!/usr/bin/env python3

"""
DiT 训练问题诊断脚本
帮助识别 loss 不下降的原因
"""

import torch
import torch.nn as nn
import numpy as np
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.modeling_diffusion import DiTForDiffusion


def diagnose_dit_training():
    """诊断 DiT 训练问题"""
    print("🔍 DiT 训练问题诊断")
    print("=" * 60)
    
    # 创建配置
    config = DiffusionConfig()
    config.use_dit = True
    config.diffusion_step_embed_dim = 256
    config.n_layer = 4
    config.n_head = 8
    config.horizon = 16
    config.n_obs_steps = 2
    config.p_drop_attn = 0.1
    config.p_drop_emb = 0.1
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 计算条件维度
    single_cond_dim = 142  # 机器人状态 + 图像特征
    total_cond_dim = single_cond_dim * config.n_obs_steps
    
    # 创建模型
    model = DiTForDiffusion(config, cond_dim=total_cond_dim).to(device)
    model.train()
    
    print(f"📊 模型信息:")
    print(f"   • 参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   • 可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 1. 检查前向传播
    print(f"\n🔄 检查前向传播...")
    batch_size = 8
    
    sample = torch.randn(batch_size, config.horizon, 7, device=device)
    timestep = torch.randint(0, 100, (batch_size,), device=device)
    global_cond = torch.randn(batch_size, total_cond_dim, device=device)
    
    try:
        output = model(sample, timestep, global_cond)
        print(f"   ✅ 前向传播成功")
        print(f"   • 输出形状: {output.shape}")
        print(f"   • 输出统计: 均值={output.mean().item():.6f}, 标准差={output.std().item():.6f}")
        
        # 检查输出是否有异常值
        if torch.isnan(output).any():
            print(f"   ❌ 输出包含 NaN!")
            return False
        if torch.isinf(output).any():
            print(f"   ❌ 输出包含 Inf!")
            return False
            
    except Exception as e:
        print(f"   ❌ 前向传播失败: {e}")
        return False
    
    # 2. 检查梯度
    print(f"\n🎯 检查梯度...")
    
    # 创建损失
    target = torch.randn_like(output)
    loss = nn.MSELoss()(output, target)
    print(f"   • 初始损失: {loss.item():.6f}")
    
    # 反向传播
    loss.backward()
    
    # 检查梯度统计
    grad_norms = []
    zero_grad_count = 0
    nan_grad_count = 0
    
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.data.norm(2).item()
            grad_norms.append(grad_norm)
            
            if grad_norm == 0:
                zero_grad_count += 1
            if torch.isnan(param.grad).any():
                nan_grad_count += 1
                print(f"   ❌ {name} 的梯度包含 NaN!")
        else:
            print(f"   ⚠️  {name} 没有梯度")
    
    if grad_norms:
        total_grad_norm = np.sqrt(sum(g**2 for g in grad_norms))
        print(f"   • 总梯度范数: {total_grad_norm:.6f}")
        print(f"   • 平均梯度范数: {np.mean(grad_norms):.6f}")
        print(f"   • 最大梯度范数: {np.max(grad_norms):.6f}")
        print(f"   • 最小梯度范数: {np.min(grad_norms):.6f}")
        print(f"   • 零梯度参数数量: {zero_grad_count}")
        
        if total_grad_norm < 1e-8:
            print(f"   ❌ 梯度过小，可能存在梯度消失问题!")
        elif total_grad_norm > 100:
            print(f"   ❌ 梯度过大，可能存在梯度爆炸问题!")
        else:
            print(f"   ✅ 梯度范数正常")
    
    if nan_grad_count > 0:
        print(f"   ❌ {nan_grad_count} 个参数的梯度包含 NaN!")
        return False
    
    # 3. 检查学习能力
    print(f"\n📚 检查学习能力...")
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # 简单的过拟合测试
    model.train()
    initial_loss = None
    
    for step in range(50):
        optimizer.zero_grad()
        
        # 使用相同的数据
        output = model(sample, timestep, global_cond)
        loss = nn.MSELoss()(output, target)
        
        if step == 0:
            initial_loss = loss.item()
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        if step % 10 == 0:
            print(f"   Step {step:2d}: Loss = {loss.item():.6f}")
    
    final_loss = loss.item()
    loss_reduction = (initial_loss - final_loss) / initial_loss
    
    print(f"   • 初始损失: {initial_loss:.6f}")
    print(f"   • 最终损失: {final_loss:.6f}")
    print(f"   • 损失下降: {loss_reduction:.2%}")
    
    if loss_reduction < 0.1:
        print(f"   ❌ 损失下降不足，模型可能无法学习!")
        print(f"   💡 建议:")
        print(f"      - 检查学习率 (当前可能太小)")
        print(f"      - 检查数据预处理")
        print(f"      - 检查模型初始化")
        return False
    else:
        print(f"   ✅ 模型具有学习能力")
    
    # 4. 检查模型组件
    print(f"\n🔧 检查模型组件...")
    
    # 检查编码器
    try:
        obs_enc = global_cond.view(batch_size, config.n_obs_steps, single_cond_dim)
        enc_cache = model.forward_enc(obs_enc)
        print(f"   ✅ 编码器工作正常，输出 {len(enc_cache)} 层")
    except Exception as e:
        print(f"   ❌ 编码器错误: {e}")
        return False
    
    # 检查解码器
    try:
        dec_output = model.forward_dec(sample, timestep, enc_cache)
        print(f"   ✅ 解码器工作正常，输出形状: {dec_output.shape}")
    except Exception as e:
        print(f"   ❌ 解码器错误: {e}")
        return False
    
    print(f"\n🎉 诊断完成!")
    
    # 5. 给出建议
    print(f"\n💡 训练建议:")
    print(f"   1. 使用较小的学习率: 1e-5 到 5e-4")
    print(f"   2. 添加梯度裁剪: max_norm=1.0")
    print(f"   3. 使用 warmup 学习率调度")
    print(f"   4. 检查数据预处理和归一化")
    print(f"   5. 监控梯度范数和权重更新")
    print(f"   6. 确保残差连接正常工作")
    
    return True


def main():
    """主函数"""
    success = diagnose_dit_training()
    
    if success:
        print(f"\n✅ 诊断完成，模型看起来正常")
        print(f"   如果训练时 loss 仍不下降，请检查:")
        print(f"   • 数据集质量和预处理")
        print(f"   • 训练超参数设置")
        print(f"   • 损失函数计算")
    else:
        print(f"\n❌ 发现问题，请根据上述建议修复")
    
    return 0 if success else 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
