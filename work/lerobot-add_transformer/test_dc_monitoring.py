#!/usr/bin/env python3
"""
测试DC tokens监控功能

验证DC tokens是否正确更新，主体模型是否被冻结
"""

import torch
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss
)
from lerobot.configs.policies import PreTrainedConfig


def test_dc_monitoring():
    """测试DC tokens监控功能"""
    print("🧪 测试DC tokens监控功能")
    
    # 检查预训练模型路径
    checkpoint_paths = [
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if Path(path).exists():
            checkpoint_path = path
            break
    
    if checkpoint_path is None:
        print("❌ 未找到预训练模型检查点")
        return False
    
    try:
        print(f"📁 使用检查点: {checkpoint_path}")
        
        # 加载配置和模型
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        print("✅ 模型加载成功!")
        
        # 检查DC tokens状态
        print("\n" + "="*50)
        print(model.get_dc_tokens_summary())
        print("="*50)
        
        # 冻结基础模型
        model.freeze_base_model()
        dc_params = model.get_dc_parameters()
        
        print(f"\n📊 DC参数数量: {len(dc_params)}")
        
        # 记录初始状态
        initial_dc_tokens = {}
        initial_base_params = {}
        
        for name, param in model.named_parameters():
            if 'dc_token' in name and param.requires_grad:
                initial_dc_tokens[name] = param.data.clone()
                print(f"✅ DC参数: {name}, 形状: {param.shape}, 可训练: {param.requires_grad}")
            elif param.requires_grad:
                print(f"⚠️ 未冻结的非DC参数: {name}")
            elif 'dc_token' not in name and len(initial_base_params) < 3:
                initial_base_params[name] = param.data.clone()
                print(f"🔒 冻结的基础参数: {name}, 形状: {param.shape}")
        
        # 创建优化器
        optimizer = torch.optim.AdamW(dc_params, lr=1e-4)
        contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
        
        # 模拟训练几步
        model.train()
        
        for step in range(3):
            print(f"\n🔄 训练步骤 {step+1}")
            
            # 创建测试数据
            batch = {
                'action': torch.randn(2, 16, 2),
                'observation.state': torch.randn(2, 2, 2),
                'observation.image': torch.randn(2, 2, 3, 96, 96),
            }
            
            # 前向传播 (不使用no_grad，需要计算梯度)
            error_matrix = model.compute_contrastive_error(batch, use_dc=True)
            
            loss = contrastive_loss(error_matrix)
            print(f"   损失: {loss.item():.6f}")
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 检查更新
            print("   🔍 检查参数更新:")
            
            # 检查DC tokens更新
            for name, param in model.named_parameters():
                if 'dc_token' in name and param.requires_grad:
                    if name in initial_dc_tokens:
                        diff = torch.norm(param.data - initial_dc_tokens[name]).item()
                        grad_norm = torch.norm(param.grad).item() if param.grad is not None else 0
                        print(f"     DC {name}: 变化={diff:.8f}, 梯度范数={grad_norm:.8f}")
            
            # 检查基础模型参数
            for name, param in model.named_parameters():
                if name in initial_base_params:
                    diff = torch.norm(param.data - initial_base_params[name]).item()
                    grad_norm = torch.norm(param.grad).item() if param.grad is not None else 0
                    if diff > 1e-10:
                        print(f"     ⚠️ 基础 {name}: 变化={diff:.8f}, 梯度范数={grad_norm:.8f}")
                    else:
                        print(f"     ✅ 基础 {name}: 正确冻结")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_loss_computation():
    """测试损失计算的合理性"""
    print("\n🧪 测试损失计算")
    
    try:
        # 创建简单的测试数据
        batch_size = 2
        
        # 理想情况：对角线误差小，非对角线误差大
        error_matrix = torch.randn(batch_size, batch_size) + 2.0
        diagonal_indices = torch.arange(batch_size)
        error_matrix[diagonal_indices, diagonal_indices] -= 1.5  # 对角线误差更小
        
        print(f"📐 测试误差矩阵:\n{error_matrix}")
        
        contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
        
        # 计算多次损失，看是否有变化
        losses = []
        for i in range(5):
            loss = contrastive_loss(error_matrix)
            losses.append(loss.item())
            print(f"   第{i+1}次损失: {loss.item():.6f}")
        
        # 检查损失是否一致（应该一致，因为输入相同）
        loss_std = torch.tensor(losses).std().item()
        if loss_std < 1e-6:
            print("✅ 损失计算一致")
        else:
            print(f"⚠️ 损失计算不一致，标准差: {loss_std}")
        
        # 测试梯度
        error_matrix.requires_grad_(True)
        loss = contrastive_loss(error_matrix)
        loss.backward()
        
        if error_matrix.grad is not None:
            grad_norm = torch.norm(error_matrix.grad).item()
            print(f"✅ 梯度计算正常，梯度范数: {grad_norm:.6f}")
        else:
            print("❌ 没有计算出梯度")
        
        return True
        
    except Exception as e:
        print(f"❌ 损失测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🚀 DC tokens监控功能测试\n")
    
    tests = [
        ("DC监控功能", test_dc_monitoring),
        ("损失计算", test_loss_computation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print("=" * 60)
        success = test_func()
        results.append((test_name, success))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 监控功能正常！可以用于调试训练过程")
        print("\n💡 使用建议:")
        print("   ✅ 检查DC tokens是否在更新")
        print("   ✅ 验证基础模型是否被正确冻结")
        print("   ✅ 监控梯度范数和参数变化")
        print("   ✅ 跟踪损失变化趋势")
    else:
        print("⚠️ 部分功能有问题，请检查")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
