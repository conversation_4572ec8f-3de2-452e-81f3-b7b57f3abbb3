#!/usr/bin/env python

"""
Complete SoftREPA training script using lerobot dataset and safetensors checkpoint.
This script demonstrates the full workflow from checkpoint loading to DC token training.

FIXED: GradScaler usage issue - 修复了"No inf checks were recorded for this optimizer"错误
"""

import argparse
import logging
import sys
import os
import json
from pathlib import Path
from datetime import datetime

import time

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
from safetensors.torch import load_file

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.envs.factory import make_env
from lerobot.common.policies.factory import make_policy

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss,
)
from lerobot.common.utils.utils import init_logging, get_safe_torch_device

# Import evaluation utilities
from lerobot.scripts.eval import eval_policy


def setup_logging(output_dir: str):
    """Setup logging configuration with output to specified directory"""
    # Create log file in the output directory
    log_file = Path(output_dir) / "training.log"
    
    # Configure logging to write to both console and file
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    )
    
    logging.info(f"日志文件保存到: {log_file}")


def create_training_directory(base_dir: str = "/home/<USER>/work/lerobot-add_transformer/dc_checkpoints") -> str:
    """创建新的训练目录，基于时间戳"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    training_dir = Path(base_dir) / f"training_{timestamp}"
    training_dir.mkdir(parents=True, exist_ok=True)
    return str(training_dir)


def create_dataset_config(dataset_repo_id: str):
    """
    简化的数据集配置创建函数

    Args:
        dataset_repo_id: Dataset repository ID (e.g., "lerobot/pusht")

    Returns:
        简单的配置对象
    """
    # 创建简单的配置对象
    class SimpleConfig:
        def __init__(self, repo_id):
            self.dataset = SimpleDatasetConfig(repo_id)
            self.policy = SimplePolicyConfig()
            self.training = True

    class SimpleDatasetConfig:
        def __init__(self, repo_id):
            self.repo_id = repo_id
            self.root = None
            self.revision = None

    class SimplePolicyConfig:
        def __init__(self):
            self.n_obs_steps = 2
            self.n_action_steps = 16
            self.horizon = 16

    return SimpleConfig(dataset_repo_id)


def evaluate_model(diffusion_model: DiffusionModel_DC, checkpoint_path: str, env_type: str = "pusht"):
    """
    评估模型性能 - 简化版本，仿照train.py
    
    Args:
        diffusion_model: 训练中的DiffusionModel_DC
        checkpoint_path: 基础模型checkpoint路径（用于日志记录）
        env_type: 环境类型
        
    Returns:
        评估结果字典
    """
    try:
        logging.info(f"🔍 开始评估模型 (step {diffusion_model.current_step if hasattr(diffusion_model, 'current_step') else 'unknown'})")
        
        # 使用lerobot官方的PushtEnv配置
        from lerobot.common.envs.configs import PushtEnv
        env_cfg = PushtEnv()  # 使用官方配置
        
        # 创建环境 - 就像train.py中一样
        from lerobot.common.envs.factory import make_env
        env = make_env(env_cfg, n_envs=10)  # 10个环境并行评估
        
        # 直接使用训练中的diffusion_model，它已经有正确的归一化统计信息
        # 不需要重新创建policy！
        policy = diffusion_model
        policy.eval()
        
        # 运行评估 - 就像train.py中一样
        with torch.no_grad():
            info = eval_policy(
                env,
                policy,
                10,  # n_episodes
                max_episodes_rendered=0,  # 训练时不渲染视频
                videos_dir=None,
                start_seed=42,
            )
        
        env.close()
        
        success_rate = info["aggregated"]["pc_success"]
        avg_reward = info["aggregated"]["avg_sum_reward"]
        
        logging.info(f"✅ 评估完成 - 成功率: {success_rate:.1f}%, 平均奖励: {avg_reward:.3f}")
        
        return {
            "success_rate": success_rate,
            "avg_reward": avg_reward,
            "full_info": info
        }
        
    except Exception as e:
        logging.error(f"❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success_rate": 0.0,
            "avg_reward": 0.0,
            "full_info": None
        }


def load_and_create_dc_model(checkpoint_path: str, n_dc_tokens: int = 4, n_dc_layers: int = 6):
    """
    简化的DiffusionModel_DC创建和加载函数

    Args:
        checkpoint_path: Path to checkpoint directory
        n_dc_tokens: Number of DC tokens per layer
        n_dc_layers: Number of layers to apply DC tokens

    Returns:
        DiffusionModel_DC instance
    """
    checkpoint_dir = Path(checkpoint_path)

    logging.info(f"🔄 Loading DiffusionModel_DC from checkpoint: {checkpoint_path}")

    # 1. 加载预训练模型配置
    config_path = checkpoint_dir / "pretrained_model" / "config.json"
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    # 使用PreTrainedConfig加载配置（与简化后的代码一致）
    from lerobot.configs.policies import PreTrainedConfig
    config = PreTrainedConfig.from_pretrained(str(config_path.parent))

    logging.info(f"✅ 配置加载成功: {config.type}")

    # 2. 使用简化的from_pretrained方法创建DiffusionModel_DC
    try:
        diffusion_model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=True
        )

        logging.info("✅ DiffusionModel_DC创建和加载成功!")

        # 3. 打印模型统计信息
        total_params = sum(p.numel() for p in diffusion_model.parameters())
        dc_params = sum(p.numel() for name, p in diffusion_model.named_parameters() if 'dc' in name.lower())
        base_params = total_params - dc_params

        logging.info(f"📊 模型参数统计:")
        logging.info(f"  总参数量: {total_params:,}")
        logging.info(f"  DC参数量: {dc_params:,} ({dc_params/total_params*100:.2f}%)")
        logging.info(f"  基础参数量: {base_params:,}")

        return diffusion_model

    except Exception as e:
        logging.error(f"❌ DiffusionModel_DC创建失败: {e}")
        raise


def train_dc_tokens(
    diffusion_model: DiffusionModel_DC,
    dataloader: DataLoader,
    device: torch.device,
    learning_rate: float = 1e-4,
    steps: int = 10000,
    log_freq: int = 100,
    save_freq: int = 5000,
    eval_freq: int = 5000,
    output_dir: str = "./dc_checkpoints",
    checkpoint_path: str = None,
    env_type: str = "pusht",
    use_grad_clip: bool = True,  # 新增：是否使用梯度裁剪
    args=None
):
    """
    Train DC tokens using contrastive learning.
    
    Args:
        diffusion_model: DiffusionModel_DC instance
        dataloader: Training data loader
        device: Training device
        learning_rate: Learning rate for DC token training
        steps: Number of training steps
        log_freq: Logging frequency
        save_freq: Checkpoint saving frequency
        eval_freq: Evaluation frequency
        output_dir: Output directory for checkpoints
        checkpoint_path: Base model checkpoint path for evaluation
        env_type: Environment type for evaluation
        use_grad_clip: Whether to use gradient clipping (fixes GradScaler issue)
    """
    # Move model to device
    diffusion_model.to(device)
    
    # Freeze base model and get DC parameters
    diffusion_model.freeze_base_model()
    dc_params = diffusion_model.get_dc_parameters()
    
    if not dc_params:
        raise ValueError("No DC parameters found! Check DC token setup.")
    
    logging.info(f"Training {len(dc_params)} DC parameters")
    diffusion_model.print_parameter_stats()
    
    # Create optimizer for DC parameters only
    effective_lr = learning_rate * 10  # 增加学习率
    logging.info(f"📈 调整学习率: {learning_rate:.0e} -> {effective_lr:.0e}")

    optimizer = torch.optim.SGD(dc_params, lr=effective_lr, momentum=0.9, weight_decay=1e-5)
    logging.info(f"🔧 使用SGD优化器，momentum=0.9, weight_decay=1e-5")

    # Create contrastive learning components
    adjusted_temp = 0.5
    adjusted_scale = 1.0
    contrastive_loss = ContrastiveLoss(
        temp=adjusted_temp,
        scale=adjusted_scale,
        device=device.type,
        dweight=1.0
    )
    logging.info(f"🎯 对比损失参数: temp={adjusted_temp}, scale={adjusted_scale}, dweight=1.0")

    # 梯度裁剪配置
    logging.info(f"🔧 梯度裁剪设置: {'启用' if use_grad_clip else '禁用'}")
    if use_grad_clip:
        logging.info("  - 梯度裁剪范数: 1.0")
        logging.info("  - 这有助于训练稳定性，避免梯度爆炸")
    else:
        logging.info("  - 保留完整梯度信号，可能有更好的收敛性")

    # 添加直接的DC token正则化损失
    def dc_regularization_loss(model, lambda_reg=0.01):
        """直接的DC token正则化，提供额外的梯度信号"""
        reg_loss = 0.0
        if hasattr(model.net, 'dc_tokens'):
            reg_loss += lambda_reg * torch.norm(model.net.dc_tokens, p=2)
        if hasattr(model.net, 'dc_t_tokens'):
            reg_loss += lambda_reg * torch.norm(model.net.dc_t_tokens.weight, p=2)
        return reg_loss

    # Create gradient scaler for mixed precision
    scaler = GradScaler()
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # 最佳模型追踪
    best_success_rate = -1.0
    best_avg_reward = float('-inf')
    best_model_path = None
    
    # Training loop
    logging.info("Starting DC token training...")
    diffusion_model.train()
    
    # Create infinite data iterator
    from itertools import cycle
    data_iter = cycle(dataloader)
    
    progress_bar = tqdm(range(steps), desc="Training DC tokens")

    # 初始化监控变量
    initial_dc_tokens = {}
    initial_base_params = {}

    # 记录初始DC tokens状态
    for name, param in diffusion_model.named_parameters():
        # 检查是否是DC相关参数
        is_dc_param = (
            'dc_token' in name or
            (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
            (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
        )

        if is_dc_param and param.requires_grad:
            initial_dc_tokens[name] = param.data.clone()
        elif param.requires_grad:
            logging.warning(f"⚠️ 发现未冻结的非DC参数: {name}")
        elif not is_dc_param:
            # 记录一些基础模型参数用于验证冻结状态
            if len(initial_base_params) < 5:  # 只记录前5个用于检查
                initial_base_params[name] = param.data.clone()

    logging.info(f"📊 初始DC tokens数量: {len(initial_dc_tokens)}")
    logging.info(f"📊 初始基础参数样本数量: {len(initial_base_params)}")

    # 输出DC tokens初始状态
    logging.info(diffusion_model.get_dc_tokens_summary())

    for step in progress_bar:
        # Get batch
        batch = next(data_iter)

        # Debug: Print batch structure on first iteration
        if step == 0:
            logging.info("Batch structure:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    logging.info(f"  {key}: {value.shape} {value.dtype}")
                else:
                    logging.info(f"  {key}: {type(value)}")

        # Move batch to device
        for key in batch:
            if isinstance(batch[key], torch.Tensor):
                batch[key] = batch[key].to(device, non_blocking=True)
        
        # Forward pass with mixed precision
        with autocast():
            # Compute contrastive error matrix using DiffusionModel_DC directly
            error_matrix = diffusion_model.compute_contrastive_error(batch, use_dc=True)

            # Compute contrastive loss
            contrastive_loss_value = contrastive_loss(error_matrix)

            # 添加DC token正则化损失，提供直接的梯度信号
            reg_loss = dc_regularization_loss(diffusion_model, lambda_reg=0.1)

            # 总损失
            loss = contrastive_loss_value + reg_loss
        
        # Backward pass
        optimizer.zero_grad()
        scaler.scale(loss).backward()

        # 🔧 FIXED: GradScaler使用修复
        if use_grad_clip:
            # 模式2：使用梯度裁剪 - 需要先unscale再clip
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(diffusion_model.parameters(), max_norm=1.0)
        # 模式1：不使用梯度裁剪 - 直接step，不需要unscale
        
        # Optimizer step
        scaler.step(optimizer)
        scaler.update()

        # 监控DC tokens和模型参数更新
        if step % 10 == 0 or step < 5:  # 前5步和每10步检查一次
            with torch.no_grad():
                # 检查DC tokens是否更新
                dc_updates = {}
                for name, param in diffusion_model.named_parameters():
                    # 检查是否是DC相关参数
                    is_dc_param = (
                        'dc_token' in name or
                        (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
                        (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
                    )

                    if is_dc_param and param.requires_grad:
                        if name in initial_dc_tokens:
                            diff = torch.norm(param.data - initial_dc_tokens[name]).item()
                            dc_updates[name] = diff

                # 检查基础模型参数是否被意外更新
                base_updates = {}
                for name, param in diffusion_model.named_parameters():
                    if name in initial_base_params:
                        diff = torch.norm(param.data - initial_base_params[name]).item()
                        base_updates[name] = diff

                # 检查梯度
                dc_grad_norms = {}
                base_grad_norms = {}
                for name, param in diffusion_model.named_parameters():
                    if param.grad is not None:
                        grad_norm = torch.norm(param.grad).item()

                        # 检查是否是DC相关参数
                        is_dc_param = (
                            'dc_token' in name or
                            (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
                            (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
                        )

                        if is_dc_param:
                            dc_grad_norms[name] = grad_norm
                        else:
                            base_grad_norms[name] = grad_norm

        # Compute metrics
        with torch.no_grad():
            batch_size = error_matrix.shape[0]
            diagonal_errors = torch.diag(error_matrix)
            off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]

            # Compute accuracy: how many diagonal elements are smaller than their row mean
            row_means = error_matrix.mean(dim=1)  # Mean error for each row
            correct_predictions = (diagonal_errors < row_means).float()
            accuracy = correct_predictions.mean()
        
        # Update progress bar
        progress_bar.set_postfix({
            'total_loss': f"{loss.item():.4f}",
            'cont_loss': f"{contrastive_loss_value.item():.4f}",
            'reg_loss': f"{reg_loss.item():.4f}",
            'acc': f"{accuracy.item():.3f}",
            'diag': f"{diagonal_errors.mean().item():.4f}",
            'off_diag': f"{off_diagonal_errors.mean().item():.4f}",
            'best_success': f"{best_success_rate:.1f}%",
            'grad_clip': '✓' if use_grad_clip else '✗'
        })

        # 详细日志输出
        if (step + 1) % log_freq == 0 or step < 5:
            logging.info(
                f"Step {step+1}/{steps}: "
                f"TotalLoss={loss.item():.4f}, "
                f"ContrastiveLoss={contrastive_loss_value.item():.4f}, "
                f"RegLoss={reg_loss.item():.4f}, "
                f"Acc={accuracy.item():.3f}, "
                f"DiagErr={diagonal_errors.mean().item():.4f}, "
                f"OffDiagErr={off_diagonal_errors.mean().item():.4f}, "
                f"GradClip={'ON' if use_grad_clip else 'OFF'}"
            )

            # 输出DC tokens更新情况
            if 'dc_updates' in locals():
                logging.info("🔍 DC Tokens更新情况:")
                for name, diff in dc_updates.items():
                    logging.info(f"  {name}: 变化量={diff:.6f}")

                if dc_grad_norms:
                    logging.info("📈 DC Tokens梯度范数:")
                    for name, grad_norm in dc_grad_norms.items():
                        logging.info(f"  {name}: 梯度范数={grad_norm:.10f}")
                else:
                    logging.warning("⚠️ 没有检测到DC tokens的梯度!")

                # 检查基础模型是否被意外更新
                if base_updates:
                    max_base_update = max(base_updates.values())
                    if max_base_update > 1e-8:
                        logging.warning(f"⚠️ 基础模型参数可能未被正确冻结! 最大变化量: {max_base_update:.8f}")
                    else:
                        logging.info(f"✅ 基础模型参数正确冻结 (最大变化量: {max_base_update:.8f})")

                if base_grad_norms:
                    logging.warning("⚠️ 检测到基础模型参数的梯度:")
                    for name, grad_norm in list(base_grad_norms.items())[:3]:  # 只显示前3个
                        logging.warning(f"  {name}: 梯度范数={grad_norm:.6f}")
                else:
                    logging.info("✅ 基础模型参数没有梯度 (正确冻结)")

        # Save checkpoint and evaluate
        if (step + 1) % save_freq == 0:
            # 保存当前模型
            checkpoint_path_step = Path(output_dir) / f"dc_checkpoint_step_{step+1}.pth"
            torch.save({
                'step': step + 1,
                'model_state_dict': diffusion_model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': loss.item(),
                'accuracy': accuracy.item(),
                'use_grad_clip': use_grad_clip,
                'timestamp': datetime.now().isoformat(),
            }, checkpoint_path_step)
            logging.info(f"完整模型检查点已保存: {checkpoint_path_step}")
        
        # Evaluate model
        if (step + 1) % eval_freq == 0 and checkpoint_path is not None:
            logging.info(f"🎯 开始第 {step+1} 步评估...")
            
            # 评估当前模型
            eval_results = evaluate_model(
                diffusion_model, 
                checkpoint_path,
                env_type
            )
            
            current_success_rate = eval_results['success_rate']
            current_avg_reward = eval_results['avg_reward']
            
            logging.info(f"📊 第 {step+1} 步评估结果:")
            logging.info(f"  成功率: {current_success_rate:.1f}%")
            logging.info(f"  平均奖励: {current_avg_reward:.3f}")
            
            # 检查是否是最佳模型
            is_best = False
            if current_success_rate > best_success_rate:
                is_best = True
                best_success_rate = current_success_rate
                best_avg_reward = current_avg_reward
            elif current_success_rate == best_success_rate and current_avg_reward > best_avg_reward:
                is_best = True
                best_avg_reward = current_avg_reward
            
            if is_best:
                # 更新最佳模型
                best_model_path = Path(output_dir) / "best_dc_model.pth"
                torch.save({
                    'step': step + 1,
                    'model_state_dict': diffusion_model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': loss.item(),
                    'accuracy': accuracy.item(),
                    'eval_success_rate': current_success_rate,
                    'eval_avg_reward': current_avg_reward,
                    'use_grad_clip': use_grad_clip,
                    'timestamp': datetime.now().isoformat(),
                }, best_model_path)
                
                logging.info(f"🏆 新的最佳模型! 成功率: {best_success_rate:.1f}%, 平均奖励: {best_avg_reward:.3f}")
                logging.info(f"🏆 最佳模型已保存: {best_model_path}")

    # Save final model
    final_path = Path(output_dir) / "dc_model_final.pth"
    torch.save({
        'step': steps,
        'model_state_dict': diffusion_model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss.item(),
        'accuracy': accuracy.item(),
        'use_grad_clip': use_grad_clip,
        'timestamp': datetime.now().isoformat(),
    }, final_path)
    logging.info(f"最终完整模型已保存: {final_path}")
    
    # 输出最佳模型信息
    if best_model_path and best_model_path.exists():
        logging.info("🏆 训练总结:")
        logging.info(f"  最佳模型路径: {best_model_path}")
        logging.info(f"  最佳成功率: {best_success_rate:.1f}%")
        logging.info(f"  最佳平均奖励: {best_avg_reward:.3f}")
    else:
        logging.info("⚠️ 未找到最佳模型")


def main():
    parser = argparse.ArgumentParser(description="Train DiffusionModel_DC with lerobot dataset")
    
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="Path to lerobot checkpoint directory")
    parser.add_argument("--dataset", type=str, default="lerobot/pusht",
                       help="Dataset repository ID")
    parser.add_argument("--n_dc_tokens", type=int, default=4,
                       help="Number of DC tokens per layer")
    parser.add_argument("--n_dc_layers", type=int, default=6,
                       help="Number of layers to apply DC tokens")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use for training")
    parser.add_argument("--batch_size", type=int, default=64,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-3,
                       help="Learning rate for DC token training")
    parser.add_argument("--steps", type=int, default=10000,
                       help="Number of training steps")
    parser.add_argument("--log_freq", type=int, default=100,
                       help="Logging frequency")
    parser.add_argument("--save_freq", type=int, default=5000,
                       help="Checkpoint saving frequency")
    parser.add_argument("--eval_freq", type=int, default=5000,
                       help="Evaluation frequency")
    parser.add_argument("--env_type", type=str, default="pusht",
                       help="Environment type for evaluation")
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/work/lerobot-add_transformer/dc_checkpoints",
                       help="Base output directory for checkpoints")
    parser.add_argument("--use_grad_clip", action="store_true", default=True,
                       help="Whether to use gradient clipping (recommended)")
    parser.add_argument("--no_grad_clip", action="store_true", default=False,
                       help="Disable gradient clipping")
    
    args = parser.parse_args()
    
    # 处理梯度裁剪选项
    if args.no_grad_clip:
        args.use_grad_clip = False
    
    # 验证导入
    try:
        from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
        from lerobot.configs.policies import PreTrainedConfig
        logging.info("✅ 所有必要模块导入成功")
    except ImportError as e:
        logging.error(f"❌ 导入模块失败: {e}")
        return
    
    # Create training directory with timestamp
    training_dir = create_training_directory(args.output_dir)
    args.output_dir = training_dir
    
    # Setup logging
    setup_logging(args.output_dir)
    init_logging()
    
    logging.info("=" * 60)
    logging.info("SoftREPA DC Token Training with Lerobot Dataset (FIXED)")
    logging.info("=" * 60)
    
    # Validate inputs
    if not Path(args.checkpoint).exists():
        logging.error(f"Checkpoint directory not found: {args.checkpoint}")
        return
    
    # Get device
    device = get_safe_torch_device(args.device, log=True)
    
    logging.info(f"Configuration:")
    logging.info(f"  Checkpoint: {args.checkpoint}")
    logging.info(f"  Dataset: {args.dataset}")
    logging.info(f"  DC tokens: {args.n_dc_tokens} tokens, {args.n_dc_layers} layers")
    logging.info(f"  Device: {device}")
    logging.info(f"  Batch size: {args.batch_size}")
    logging.info(f"  Learning rate: {args.learning_rate}")
    logging.info(f"  Training steps: {args.steps}")
    logging.info(f"  Evaluation frequency: {args.eval_freq}")
    logging.info(f"  Environment type: {args.env_type}")
    logging.info(f"  Training directory: {args.output_dir}")
    logging.info(f"  梯度裁剪: {'启用' if args.use_grad_clip else '禁用'}")
    logging.info("  🔧 FIXED: GradScaler错误已修复!")
    
    try:
        # Create dataset config
        dataset_config = create_dataset_config(args.dataset)
        
        # Load dataset
        logging.info("Loading dataset...")

        # Set up delta_timestamps for sequence loading
        delta_timestamps = {
            # Load 2 observation steps: previous and current
            "observation.state": [-0.1, 0.0],
            "observation.image": [-0.1, 0.0],
            # Load 16 action steps: current and 15 future actions
            "action": [i * 0.1 for i in range(16)],  # [0.0, 0.1, 0.2, ..., 1.5]
        }

        # Create LeRobotDataset directly
        from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
        dataset = LeRobotDataset(
            repo_id=dataset_config.dataset.repo_id,
            delta_timestamps=delta_timestamps,
            image_transforms=None,  # No image transforms for now
        )
        logging.info(f"Dataset loaded: {len(dataset)} samples")
        
        # Create dataloader
        dataloader = DataLoader(
            dataset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=device.type != "cpu",
            drop_last=True
        )
        
        # Load model from checkpoint
        logging.info("Loading DiffusionModel_DC from checkpoint...")
        diffusion_model = load_and_create_dc_model(
            args.checkpoint,
            args.n_dc_tokens,
            args.n_dc_layers
        )
        
        # Train DC tokens
        train_dc_tokens(
            diffusion_model=diffusion_model,
            dataloader=dataloader,
            device=device,
            learning_rate=args.learning_rate,
            steps=args.steps,
            log_freq=args.log_freq,
            save_freq=args.save_freq,
            eval_freq=args.eval_freq,
            output_dir=args.output_dir,
            checkpoint_path=args.checkpoint,
            env_type=args.env_type,
            use_grad_clip=args.use_grad_clip,  # 传递梯度裁剪选项
            args=args
        )
        
        logging.info("=" * 60)
        logging.info("Training completed successfully!")
        logging.info("=" * 60)
        logging.info(f"训练结果保存在: {args.output_dir}")
        
        # 显示pth文件
        logging.info("保存的模型文件:")
        for file_path in Path(args.output_dir).glob("*.pth"):
            file_size = file_path.stat().st_size / (1024 * 1024)  # MB
            logging.info(f"  - {file_path.name} ({file_size:.1f} MB)")
        
        logging.info(f"日志文件: {Path(args.output_dir) / 'training.log'}")
        logging.info("注意: 最佳模型保存为 best_dc_model.pth")
        logging.info("🔧 GradScaler错误已修复! 现在可以正常训练了!")
        logging.info("=" * 60)
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 