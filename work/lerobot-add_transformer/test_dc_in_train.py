#!/usr/bin/env python

"""
Test DC model loading and training within the train.py framework.
This script tests if the DC model can be properly loaded and trained using the standard training pipeline.
"""

import logging
import sys
import os
from pathlib import Path
import time

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
from safetensors.torch import load_file

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
from lerobot.common.utils.utils import init_logging, get_safe_torch_device


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('dc_test_training.log')
        ]
    )


def create_dataset_config(dataset_repo_id: str):
    """Create a minimal dataset configuration for lerobot."""
    from lerobot.configs.default import DatasetConfig
    from omegaconf import OmegaConf

    # Create a proper DatasetConfig
    dataset_config = DatasetConfig(
        repo_id=dataset_repo_id,
        root=None,  # Use default cache location
        revision=None,
        image_transforms=OmegaConf.create({
            "enable": False,  # Disable image transforms for simplicity
        })
    )

    # Wrap in the expected structure with complete policy config
    config = OmegaConf.create({
        "dataset": dataset_config,
        "policy": {
            "n_obs_steps": 2,  # Required for delta timestamps
            "n_action_steps": 16,  # Match the model's horizon
            "horizon": 16,  # Explicitly set horizon
            "observation_delta_indices": None,  # Required field
            "action_delta_indices": None,  # Required field
            "reward_delta_indices": None,  # Required field
        },
        "training": True,
    })

    return config


def load_dc_model_from_checkpoint(checkpoint_path: str, dataset, n_dc_tokens: int = 4, n_dc_layers: int = 6):
    """Load DiffusionModel_DC from checkpoint and test basic functionality."""
    
    print(f"Loading DC model from checkpoint: {checkpoint_path}")
    
    # Detect condition dimension from pretrained model
    safetensors_path = Path(checkpoint_path) / "pretrained_model" / "model.safetensors"
    if not safetensors_path.exists():
        raise FileNotFoundError(f"Safetensors file not found: {safetensors_path}")
    
    pretrained_state_dict = load_file(str(safetensors_path))
    
    # Detect condition dimension from cond_obs_emb weight
    cond_dim = None
    encoder_layers = None
    for key, tensor in pretrained_state_dict.items():
        if key == "diffusion.net.cond_obs_emb.weight":
            cond_dim = tensor.shape[1]
            print(f"Detected condition dimension from pretrained model: {cond_dim}")
        elif key.startswith("diffusion.net.encoder.layers.") and key.endswith(".self_attn.in_proj_weight"):
            # Extract layer number from key like "diffusion.net.encoder.layers.0.self_attn.in_proj_weight"
            parts = key.split(".")
            if len(parts) >= 5 and parts[4].isdigit():
                layer_num = int(parts[4])
                encoder_layers = max(encoder_layers or 0, layer_num + 1)
    
    if encoder_layers:
        print(f"Detected encoder layers: {encoder_layers}")
    
    # Create DiffusionConfig based on dataset
    # Access features from dataset metadata
    action_dim = dataset.meta.features["action"]["shape"][0]
    state_dim = dataset.meta.features["observation.state"]["shape"][0]
    has_images = "observation.image" in dataset.meta.features
    
    config_dict = {
        'horizon': 16,
        'n_obs_steps': 2,
        'use_transformer': True,
        'n_layer': encoder_layers or 12,
        'n_cond_layers': encoder_layers or 12,
        'n_head': 16,
        'diffusion_step_embed_dim': 1024,
        'vision_backbone': 'resnet18',
        'use_separate_rgb_encoder_per_camera': True,
        'input_features': {
            'observation.state': {'shape': [state_dim], 'type': 'STATE'},
        },
        'output_features': {
            'action': {'shape': [action_dim], 'type': 'ACTION'}
        }
    }
    
    if has_images:
        config_dict['input_features']['observation.image'] = {'shape': [3, 96, 96], 'type': 'VISUAL'}
    
    config = DiffusionConfig(**config_dict)
    
    # Create DC model
    diffusion_model = DiffusionModel_DC(
        config=config,
        n_dc_tokens=n_dc_tokens,
        n_dc_layers=n_dc_layers,
        use_dc_t=True,
        cond_dim=cond_dim  # Pass the detected condition dimension
    )
    
    return diffusion_model


def test_dc_training_step(model, batch, device):
    """Test a single training step with DC model."""
    
    # Move batch to device
    for key in batch:
        if isinstance(batch[key], torch.Tensor):
            batch[key] = batch[key].to(device, non_blocking=True)
    
    # Test forward pass
    model.train()
    
    try:
        # Test standard forward pass
        print("Testing standard forward pass...")
        output = model(batch)
        print(f"Standard forward output shape: {output.shape}")
        
        # Test DC forward pass
        print("Testing DC forward pass...")
        if hasattr(model, 'compute_contrastive_error'):
            error_matrix = model.compute_contrastive_error(batch, use_dc=True)
            print(f"DC error matrix shape: {error_matrix.shape}")
            print(f"DC error matrix values:\n{error_matrix}")
            
            # Test loss computation
            from lerobot.common.policies.diffusion.transformer_dc_sampler import ContrastiveLoss
            contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device=device.type)
            loss = contrastive_loss(error_matrix)
            print(f"Contrastive loss: {loss.item()}")
            
            return True, loss.item()
        else:
            print("Warning: compute_contrastive_error method not found")
            return False, None
            
    except Exception as e:
        print(f"Error during forward pass: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def main():
    setup_logging()
    
    # Configuration
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000"
    dataset_repo_id = "lerobot/pusht"
    device = get_safe_torch_device("cuda", log=True)
    
    print("=" * 60)
    print("Testing DC Model Loading and Training")
    print("=" * 60)
    
    # Load dataset
    print("Loading dataset...")
    config = create_dataset_config(dataset_repo_id)
    dataset = make_dataset(config)
    print(f"Dataset loaded: {len(dataset)} samples")
    
    # Create dataloader
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=2,
        shuffle=True,
        num_workers=0,
        pin_memory=device.type != "cpu",
        drop_last=False,
    )
    
    # Load DC model
    print("Loading DC model...")
    try:
        model = load_dc_model_from_checkpoint(
            checkpoint_path=checkpoint_path,
            dataset=dataset,
            n_dc_tokens=4,
            n_dc_layers=6
        )
        model.to(device)
        print("DC model loaded successfully!")
        
        # Print model statistics
        if hasattr(model, 'print_parameter_stats'):
            model.print_parameter_stats()
        
    except Exception as e:
        print(f"Failed to load DC model: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test training steps
    print("\nTesting training steps...")
    
    success_count = 0
    total_tests = 3
    
    for i, batch in enumerate(dataloader):
        if i >= total_tests:
            break
            
        print(f"\n--- Test {i+1}/{total_tests} ---")
        
        # Print batch info
        print("Batch structure:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} {value.dtype}")
            else:
                print(f"  {key}: {type(value)}")
        
        # Test training step
        success, loss = test_dc_training_step(model, batch, device)
        
        if success:
            success_count += 1
            print(f"✅ Test {i+1} passed! Loss: {loss:.6f}")
        else:
            print(f"❌ Test {i+1} failed!")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! DC model is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the logs for details.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
