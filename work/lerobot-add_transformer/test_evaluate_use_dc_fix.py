#!/usr/bin/env python

"""
测试evaluate_model中use_dc配置修复的效果
验证模型是否正确使用DiffusionModel_DC而不是普通的DiffusionModel
"""

import sys
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_make_policy_with_use_dc():
    """测试make_policy是否正确响应use_dc参数"""
    print("🧪 测试make_policy对use_dc参数的响应")
    
    try:
        from lerobot.configs.policies import PreTrainedConfig
        from lerobot.common.policies.factory import make_policy
        from lerobot.common.envs.configs import PushtEnv
        
        # 1. 测试不使用use_dc的情况
        print("\n1️⃣ 测试不使用use_dc（应该创建普通DiffusionModel）")
        policy_cfg = PreTrainedConfig.from_pretrained(
            "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000/pretrained_model"
        )
        policy_cfg.use_dc = False
        policy_cfg.pretrained_path = None  # 避免from_pretrained
        
        env_cfg = PushtEnv()
        policy_normal = make_policy(cfg=policy_cfg, env_cfg=env_cfg)
        
        print(f"  策略类型: {type(policy_normal).__name__}")
        print(f"  diffusion类型: {type(policy_normal.diffusion).__name__}")
        print(f"  是否有dc_tokens: {hasattr(policy_normal.diffusion, 'dc_tokens')}")
        if hasattr(policy_normal.diffusion, 'net'):
            print(f"  net类型: {type(policy_normal.diffusion.net).__name__}")
            print(f"  net是否有dc_tokens: {hasattr(policy_normal.diffusion.net, 'dc_tokens')}")
        
        # 2. 测试使用use_dc的情况
        print("\n2️⃣ 测试使用use_dc（应该创建DiffusionModel_DC）")
        policy_cfg.use_dc = True
        policy_cfg.pretrained_path = None  # 避免from_pretrained
        
        policy_dc = make_policy(cfg=policy_cfg, env_cfg=env_cfg)
        
        print(f"  策略类型: {type(policy_dc).__name__}")
        print(f"  diffusion类型: {type(policy_dc.diffusion).__name__}")
        print(f"  是否有dc_tokens: {hasattr(policy_dc.diffusion, 'dc_tokens')}")
        if hasattr(policy_dc.diffusion, 'net'):
            print(f"  net类型: {type(policy_dc.diffusion.net).__name__}")
            print(f"  net是否有dc_tokens: {hasattr(policy_dc.diffusion.net, 'dc_tokens')}")
        
        # 3. 验证difference
        print(f"\n📊 总结:")
        print(f"  普通模型diffusion类型: {type(policy_normal.diffusion).__name__}")
        print(f"  DC模型diffusion类型: {type(policy_dc.diffusion).__name__}")
        
        if type(policy_normal.diffusion).__name__ != type(policy_dc.diffusion).__name__:
            print("✅ 成功：use_dc参数正确影响了模型类型")
        else:
            print("❌ 失败：use_dc参数没有影响模型类型")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_make_policy_with_pretrained_path():
    """测试有pretrained_path时make_policy的行为"""
    print("\n🧪 测试有pretrained_path时make_policy的行为")
    
    try:
        from lerobot.configs.policies import PreTrainedConfig
        from lerobot.common.policies.factory import make_policy
        from lerobot.common.envs.configs import PushtEnv
        
        pretrained_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000/pretrained_model"
        
        # 测试：设置use_dc=True但保留pretrained_path
        print("⚠️ 测试问题场景：设置use_dc=True但保留pretrained_path")
        policy_cfg = PreTrainedConfig.from_pretrained(pretrained_path)
        policy_cfg.use_dc = True  # 手动设置
        print(f"  配置中use_dc: {getattr(policy_cfg, 'use_dc', 'None')}")
        
        # 这种情况下make_policy会忽略use_dc，使用from_pretrained
        env_cfg = PushtEnv()
        policy_problematic = make_policy(cfg=policy_cfg, env_cfg=env_cfg)
        
        print(f"  结果diffusion类型: {type(policy_problematic.diffusion).__name__}")
        print(f"  是否有DC功能: {hasattr(policy_problematic.diffusion, 'dc_tokens') or (hasattr(policy_problematic.diffusion, 'net') and hasattr(policy_problematic.diffusion.net, 'dc_tokens'))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_evaluate_model_fix():
    """测试修复后的evaluate_model函数"""
    print("\n🧪 测试修复后的evaluate_model函数")
    
    try:
        # 这里我们只测试配置创建部分，不运行完整的评估
        from lerobot.configs.policies import PreTrainedConfig
        from lerobot.common.policies.factory import make_policy
        from lerobot.common.envs.configs import PushtEnv
        
        pretrained_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000/pretrained_model"
        
        print("📋 模拟修复后的evaluate_model流程:")
        
        # 1. 加载配置并设置use_dc
        policy_cfg = PreTrainedConfig.from_pretrained(pretrained_path)
        policy_cfg.use_dc = True
        print(f"1️⃣ 设置use_dc=True: {policy_cfg.use_dc}")
        
        # 2. 应用修复：临时移除pretrained_path
        original_pretrained_path = policy_cfg.pretrained_path
        policy_cfg.pretrained_path = None
        print(f"2️⃣ 临时移除pretrained_path")
        
        # 3. 创建policy
        env_cfg = PushtEnv()
        policy = make_policy(cfg=policy_cfg, env_cfg=env_cfg)
        print(f"3️⃣ 创建policy成功")
        
        # 4. 验证结果
        print(f"✅ 结果验证:")
        print(f"  策略类型: {type(policy).__name__}")
        print(f"  diffusion类型: {type(policy.diffusion).__name__}")
        print(f"  是否包含DC功能: {hasattr(policy.diffusion, 'dc_tokens') or (hasattr(policy.diffusion, 'net') and hasattr(policy.diffusion.net, 'dc_tokens'))}")
        
        if hasattr(policy.diffusion, 'net'):
            print(f"  net类型: {type(policy.diffusion.net).__name__}")
            if 'DC' in type(policy.diffusion.net).__name__:
                print("🎉 成功：创建了包含DC功能的模型！")
                return True
            else:
                print("❌ 失败：仍然是普通模型")
                return False
        else:
            print("⚠️ 模型结构不如预期")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🔬 开始测试use_dc配置修复效果")
    print("=" * 60)
    
    tests = [
        ("基础make_policy测试", test_make_policy_with_use_dc),
        ("pretrained_path问题测试", test_make_policy_with_pretrained_path),
        ("evaluate_model修复测试", test_evaluate_model_fix),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        success = test_func()
        results.append((test_name, success))
        print(f"结果: {'✅ 通过' if success else '❌ 失败'}")
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    all_passed = all(success for _, success in results)
    print(f"\n🏆 总体结果: {'全部通过' if all_passed else '存在失败'}")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 