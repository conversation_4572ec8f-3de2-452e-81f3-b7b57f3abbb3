# 修复 evaluate_model 中 use_dc 配置被忽略的问题

## 问题描述

在 `train_dc_with_dataset.py` 的 `evaluate_model` 函数中，虽然明确设置了 `policy_cfg.use_dc = True`，但创建的模型仍然显示 `(net): TransformerForDiffusion` 而不是期望的 `TransformerForDiffusion_DC`。

## 根本原因分析

### 问题代码片段：

```python
# 加载预训练配置
policy_cfg = PreTrainedConfig.from_pretrained(pretrained_model_path)
policy_cfg.use_dc = True  # ✅ 设置成功
policy_cfg.pretrained_path = '/path/to/pretrained/model'  # ❌ 问题所在

# 创建policy
policy = make_policy(
    cfg=policy_cfg,
    env_cfg=eval_cfg.env,
)
```

### 原因：

在 `lerobot/common/policies/factory.py` 的 `make_policy` 函数中：

```python
def make_policy(cfg: PreTrainedConfig, ...):
    # ...
    if cfg.pretrained_path:  # ❌ 当存在pretrained_path时
        # 会调用from_pretrained重新加载原始配置，忽略手动修改
        policy = policy_cls.from_pretrained(
            config_path=cfg.pretrained_path,  # 重新加载原始配置
            device=cfg.device
        )
    else:
        # 只有在没有pretrained_path时才使用传入的配置
        policy = policy_cls(**kwargs)
```

**关键问题**：`from_pretrained` 会从 `pretrained_path` 重新读取原始的 `config.json`，完全覆盖你手动设置的 `use_dc=True`。

## 修复方案

### 方案 1：临时移除 pretrained_path（推荐）

我已经在 `train_dc_with_dataset.py` 中应用了这个修复：

```python
# 🔧 修复：避免make_policy因为pretrained_path而忽略use_dc设置
logging.info(f"🤖 创建策略...")

# 临时移除pretrained_path，强制使用我们修改的配置
original_pretrained_path = policy_cfg.pretrained_path
policy_cfg.pretrained_path = None  # 临时移除，避免from_pretrained覆盖配置

logging.info(f"⚙️ 使用配置创建policy (use_dc={policy_cfg.use_dc})")
policy = make_policy(
    cfg=policy_cfg,
    env_cfg=eval_cfg.env,
)

# 恢复pretrained_path（如果后续需要）
policy_cfg.pretrained_path = original_pretrained_path

# 验证创建的模型结构
logging.info(f"✅ 策略创建成功:")
logging.info(f"  策略类型: {type(policy).__name__}")
logging.info(f"  diffusion类型: {type(policy.diffusion).__name__}")
logging.info(f"  是否包含DC功能: {hasattr(policy.diffusion, 'dc_tokens') or hasattr(policy.diffusion.net, 'dc_tokens')}")

# 手动加载预训练权重（除了DC tokens）
# [详细的权重加载代码...]
```

### 方案 2：直接修改预训练配置文件

在预训练模型的 `config.json` 中添加：

```json
{
  "type": "diffusion",
  "use_dc": true,
  ...
}
```

### 方案 3：使用训练中的 DiffusionModel_DC（最简单）

直接使用训练过程中已经创建的 `DiffusionModel_DC`，避免重新创建：

```python
# 在train_dc_tokens函数中，评估时直接使用diffusion_model
eval_results = evaluate_model_simple(diffusion_model, checkpoint_path, env_type)

def evaluate_model_simple(diffusion_model: DiffusionModel_DC, checkpoint_path: str, env_type: str):
    """直接使用训练中的DiffusionModel_DC进行评估"""
    # 创建环境
    env = make_env(env_cfg, n_envs=10)
    
    # 直接使用已包含DC tokens的模型
    policy = diffusion_model
    policy.eval()
    
    # 运行评估
    with torch.no_grad():
        info = eval_policy(env, policy, 10, ...)
    
    return info
```

## 验证修复效果

修复后，你应该看到类似的日志输出：

```
✅ 策略创建成功:
  策略类型: DiffusionPolicy
  diffusion类型: DiffusionModel_DC          # ✅ 而不是 DiffusionModel
  是否包含DC功能: True                       # ✅ 而不是 False
```

在模型结构中应该看到：

```
DiffusionPolicy(
  (diffusion): DiffusionModel_DC(           # ✅ 正确的DC版本
    (net): TransformerForDiffusion_DC(      # ✅ 包含DC功能的Transformer
      ...
      (dc_tokens): Parameter containing:    # ✅ DC tokens存在
      ...
    )
  )
)
```

## 相关文件修改

1. **主要修复文件**：`train_dc_with_dataset.py` 第 624-670 行
2. **根本问题位置**：`lerobot/common/policies/factory.py` 第 142-148 行
3. **模型选择逻辑**：`lerobot/common/policies/diffusion/modeling_diffusion.py` 第 79-84 行

## 总结

这个问题的根本原因是 LeRobot 的设计：当配置中存在 `pretrained_path` 时，`make_policy` 会优先使用 `from_pretrained` 加载原始配置，而不是使用传入的修改后配置。

通过临时移除 `pretrained_path` 并手动加载预训练权重，我们可以确保 `use_dc=True` 设置生效，创建正确的 `DiffusionModel_DC` 模型。 