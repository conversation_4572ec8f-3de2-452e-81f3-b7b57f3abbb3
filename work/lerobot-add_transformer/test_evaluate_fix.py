#!/usr/bin/env python

"""
测试脚本：验证evaluate_model函数的修复
"""

import sys
import logging
import torch
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

def test_evaluate_model_fix():
    """测试evaluate_model函数的修复"""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
    
    try:
        # 导入必要的模块
        from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
        from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
        from train_dc_with_dataset import evaluate_model
        
        logging.info("✅ 模块导入成功")
        
        # 创建模拟的DiffusionModel_DC
        config = DiffusionConfig()
        config.horizon = 16
        config.n_obs_steps = 2
        config.diffusion_step_embed_dim = 256
        config.n_layer = 4
        config.n_head = 4
        config.n_cond_layers = 2
        config.use_transformer = True
        
        # 设置输入输出形状
        config.input_shapes = {"observation.state": [32]}
        config.output_shapes = {"action": [2]}
        
        logging.info("✅ 配置创建成功")
        
        # 创建DiffusionModel_DC实例
        diffusion_model = DiffusionModel_DC(
            config=config,
            n_dc_tokens=4,
            n_dc_layers=3,
            use_dc_t=False,  # 测试不使用dc_t的情况
            cond_dim=32
        )
        
        logging.info("✅ DiffusionModel_DC创建成功")
        
        # 检查DC tokens是否存在
        if hasattr(diffusion_model.net, 'dc_tokens'):
            logging.info(f"✅ DC tokens存在，形状: {diffusion_model.net.dc_tokens.shape}")
        else:
            logging.error("❌ DC tokens不存在")
            return False
        
        # 检查dc_t_tokens的状态
        if hasattr(diffusion_model.net, 'dc_t_tokens'):
            if diffusion_model.net.dc_t_tokens is None:
                logging.info("✅ dc_t_tokens正确设置为None (use_dc_t=False)")
            else:
                logging.info(f"✅ dc_t_tokens存在，形状: {diffusion_model.net.dc_t_tokens.weight.shape}")
        else:
            logging.info("✅ dc_t_tokens属性不存在")
        
        # 测试load_dc_weights_from_checkpoint函数的安全性
        logging.info("\n🧪 测试load_dc_weights_from_checkpoint的安全性")
        
        # 创建一个模拟的checkpoint
        mock_checkpoint = {
            'net.dc_tokens': torch.randn_like(diffusion_model.net.dc_tokens),
        }
        
        # 如果dc_t_tokens存在且不为None，添加到checkpoint
        if hasattr(diffusion_model.net, 'dc_t_tokens') and diffusion_model.net.dc_t_tokens is not None:
            mock_checkpoint['net.dc_t_tokens.weight'] = torch.randn_like(diffusion_model.net.dc_t_tokens.weight)
        
        # 保存模拟checkpoint
        mock_checkpoint_path = "/tmp/mock_dc_checkpoint.pth"
        torch.save(mock_checkpoint, mock_checkpoint_path)
        
        # 测试加载
        from lerobot.scripts.load_pretrained_dc import load_dc_weights_from_checkpoint
        
        try:
            load_dc_weights_from_checkpoint(diffusion_model, mock_checkpoint_path)
            logging.info("✅ load_dc_weights_from_checkpoint执行成功")
        except Exception as e:
            logging.error(f"❌ load_dc_weights_from_checkpoint失败: {e}")
            return False
        
        # 清理
        Path(mock_checkpoint_path).unlink(missing_ok=True)
        
        logging.info("\n🎉 所有测试通过! evaluate_model修复应该有效。")
        
        # 输出使用建议
        logging.info("\n📝 使用建议:")
        logging.info("1. 确保传递正确的DiffusionModel_DC实例到evaluate_model")
        logging.info("2. 确保.pth文件包含正确的DC tokens权重")
        logging.info("3. 如果使用use_dc_t=False，确保checkpoint中没有dc_t_tokens相关权重")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_evaluate_model_fix()
    if success:
        print("\n✅ 修复验证成功!")
    else:
        print("\n❌ 修复验证失败!")
        sys.exit(1)
