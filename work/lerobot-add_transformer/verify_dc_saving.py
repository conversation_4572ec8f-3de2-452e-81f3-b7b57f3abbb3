#!/usr/bin/env python

"""
验证DC tokens保存功能的简化版本
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

def test_directory_creation():
    """测试目录创建功能"""
    print("=" * 60)
    print("验证DC tokens保存功能")
    print("=" * 60)
    
    # 测试导入功能
    try:
        from train_dc_with_dataset import create_training_directory
        print("✅ 成功导入 create_training_directory 函数")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试创建训练目录
    try:
        training_dir = create_training_directory()
        print(f"✅ 成功创建训练目录: {training_dir}")
        
        # 验证目录是否存在
        if Path(training_dir).exists():
            print(f"✅ 训练目录确实存在")
        else:
            print(f"❌ 训练目录不存在")
            return False
            
        # 验证目录结构
        expected_pattern = r"training_\d{8}_\d{6}"
        dir_name = Path(training_dir).name
        import re
        if re.match(expected_pattern, dir_name):
            print(f"✅ 目录命名格式正确: {dir_name}")
        else:
            print(f"❌ 目录命名格式错误: {dir_name}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 创建训练目录失败: {e}")
        return False


def test_logging_setup():
    """测试日志设置功能"""
    try:
        from train_dc_with_dataset import setup_logging, create_training_directory
        
        # 创建测试目录
        test_dir = create_training_directory()
        print(f"测试目录: {test_dir}")
        
        # 设置日志
        setup_logging(test_dir)
        print("✅ 日志设置成功")
        
        # 检查日志文件是否被创建
        log_file = Path(test_dir) / "training.log"
        if log_file.exists():
            print(f"✅ 日志文件已创建: {log_file}")
        else:
            print(f"❌ 日志文件未创建")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 日志设置失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始验证修改后的train_dc_with_dataset.py功能")
    
    # 测试1: 目录创建
    print("\n1. 测试目录创建功能...")
    test1_pass = test_directory_creation()
    
    # 测试2: 日志设置
    print("\n2. 测试日志设置功能...")
    test2_pass = test_logging_setup()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"  目录创建: {'✅ 通过' if test1_pass else '❌ 失败'}")
    print(f"  日志设置: {'✅ 通过' if test2_pass else '❌ 失败'}")
    
    if test1_pass and test2_pass:
        print("🎉 所有基础功能测试通过!")
        print("\n修改摘要:")
        print("1. ✅ 每次训练会在dc_checkpoints下创建带时间戳的新目录")
        print("2. ✅ 日志会保存到训练目录下的training.log文件")
        print("3. ✅ 添加了只保存DC tokens的功能（需要torch才能完整测试）")
        print("4. ✅ 改进了保存逻辑，只保存必要的DC参数")
    else:
        print("❌ 部分功能测试失败")
    
    print("=" * 60)


if __name__ == "__main__":
    main() 