#!/usr/bin/env python

"""
Simple DiT Test Script
======================

Test the DiT architecture with raw observation processing.
"""

import torch
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from lerobot.common.policies.diffusion.dit_policy import DiTPolicyForDiffusion
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig


def test_dit_with_raw_obs():
    """Test DiT with raw observation processing."""
    print("🧪 Testing DiT with Raw Observation Processing")
    print("=" * 60)
    
    # Create a simple config
    config = DiffusionConfig()
    config.horizon = 16
    config.diffusion_step_embed_dim = 256
    config.n_layer = 4
    config.n_head = 4
    config.p_drop_attn = 0.1
    
    # Create DiT policy
    device = "cuda" if torch.cuda.is_available() else "cpu"
    dit_policy = DiTPolicyForDiffusion(config, cond_dim=64).to(device)
    
    print(f"✅ DiT policy created on device: {device}")
    
    # Create test data
    batch_size = 4
    
    # Sample noisy actions
    sample = torch.randn(batch_size, 16, 2, device=device)  # (B, T, action_dim)
    
    # Timesteps
    timestep = torch.randint(0, 100, (batch_size,), device=device)
    
    # Raw observations (as would come from lerobot)
    raw_obs = {
        "observation.state": torch.randn(batch_size, 4, device=device),
        "observation.environment_state": torch.randn(batch_size, 2, device=device),
    }
    
    print(f"📊 Input shapes:")
    print(f"   Sample: {sample.shape}")
    print(f"   Timestep: {timestep.shape}")
    print(f"   Raw obs state: {raw_obs['observation.state'].shape}")
    print(f"   Raw obs env: {raw_obs['observation.environment_state'].shape}")
    
    try:
        # Forward pass
        print("\n🔄 Running forward pass...")
        with torch.no_grad():
            output = dit_policy(sample, timestep, raw_obs)
        
        print(f"✅ Forward pass successful!")
        print(f"📊 Output shape: {output.shape}")
        print(f"📊 Expected shape: {sample.shape}")
        
        # Check if DiT network was initialized
        if dit_policy.dit_net is not None:
            params = sum(p.numel() for p in dit_policy.dit_net.parameters())
            print(f"📊 DiT network parameters: {params:,} ({params/1e6:.1f}M)")
        
        # Test caching
        print("\n🔄 Testing encoder caching...")
        with torch.no_grad():
            output2 = dit_policy(sample, timestep, raw_obs)
        
        print(f"✅ Caching test successful!")
        print(f"📊 Outputs equal: {torch.allclose(output, output2, atol=1e-6)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_obs_shapes():
    """Test DiT with different observation shapes."""
    print("\n🧪 Testing Different Observation Shapes")
    print("=" * 60)

    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    test_cases = [
        {
            "name": "Vector only",
            "obs": {
                "observation.state": torch.randn(2, 6, device=device),
            }
        },
        {
            "name": "Multiple vectors",
            "obs": {
                "observation.state": torch.randn(2, 4, device=device),
                "observation.environment_state": torch.randn(2, 3, device=device),
                "observation.robot_state": torch.randn(2, 7, device=device),
            }
        },
        {
            "name": "With sequence",
            "obs": {
                "observation.state": torch.randn(2, 1, 5, device=device),
                "observation.environment_state": torch.randn(2, 2, device=device),
            }
        }
    ]

    for i, test_case in enumerate(test_cases):
        print(f"\n📋 Test {i+1}: {test_case['name']}")

        # Create a new DiT policy for each test to avoid dimension conflicts
        config = DiffusionConfig()
        config.horizon = 8
        config.diffusion_step_embed_dim = 128
        config.n_layer = 2
        config.n_head = 4

        dit_policy = DiTPolicyForDiffusion(config, cond_dim=64).to(device)

        batch_size = 2
        sample = torch.randn(batch_size, 8, 2, device=device)
        timestep = torch.randint(0, 100, (batch_size,), device=device)

        try:
            with torch.no_grad():
                output = dit_policy(sample, timestep, test_case['obs'])
            print(f"   ✅ Success! Output shape: {output.shape}")

        except Exception as e:
            print(f"   ❌ Failed: {e}")
            return False
    
    return True


def main():
    print("🧪 Simple DiT Architecture Test")
    print("=" * 60)
    print("Testing DiT with raw observation processing")
    print("=" * 60)
    
    tests = [
        ("Raw Observation Processing", test_dit_with_raw_obs),
        ("Different Observation Shapes", test_different_obs_shapes),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results[test_name] = success
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"\n🎯 {test_name}: {status}")
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name:30} {status}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\n🎯 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! DiT with raw observation processing works!")
        print("\n💡 Key features verified:")
        print("   ✅ Raw observation processing")
        print("   ✅ Dynamic DiT network initialization")
        print("   ✅ Progressive encoder caching")
        print("   ✅ Multiple observation modalities")
        print("   ✅ Device compatibility")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
