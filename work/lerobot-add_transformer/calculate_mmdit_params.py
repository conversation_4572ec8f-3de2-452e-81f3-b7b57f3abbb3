#!/usr/bin/env python

"""
MMDiT Parameter Calculator
=========================

This script calculates the number of parameters for different MMDiT configurations
to help design models with target parameter counts.

Usage:
    python calculate_mmdit_params.py [--config CONFIG_NAME]
"""

import argparse
import math


def calculate_mmdit_params(
    diffusion_step_embed_dim=1024,
    n_layer=24,
    n_head=16,
    n_dc_tokens=16,
    n_dc_layers=18,
    use_dc_t=True,
    action_dim=2,
    cond_dim=132,  # This will be calculated based on vision encoder
    horizon=16,
    vision_backbone="resnet50",
    architecture="mmdit"  # "mmdit", "dit", or "transformer"
):
    """Calculate model parameters for different architectures."""

    print(f"🧮 {architecture.upper()} Parameter Calculation")
    print("=" * 50)
    
    # Action and condition projections
    action_proj_params = action_dim * diffusion_step_embed_dim
    cond_proj_params = cond_dim * diffusion_step_embed_dim
    
    # Positional embeddings
    action_pos_emb_params = horizon * diffusion_step_embed_dim
    
    # Time embedding (sinusoidal, no parameters)
    time_emb_params = 0
    
    # Architecture-specific block calculations
    transformer_block_params = 0

    if architecture == "dit":
        # Complete DiT architecture with progressive encoders and AdaLN decoders

        # Progressive encoder blocks
        encoder_params = 0
        for layer_idx in range(n_layer):
            # Self-attention encoder
            encoder_attention_params = 4 * diffusion_step_embed_dim * diffusion_step_embed_dim  # Q, K, V, O
            # MLP in encoder
            mlp_hidden_dim = 4 * diffusion_step_embed_dim
            encoder_mlp_params = (diffusion_step_embed_dim * mlp_hidden_dim + mlp_hidden_dim +
                                 mlp_hidden_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)
            # Layer norms in encoder (2 per block)
            encoder_ln_params = 2 * 2 * diffusion_step_embed_dim  # weight + bias for each

            encoder_params += encoder_attention_params + encoder_mlp_params + encoder_ln_params

        # AdaLN decoder blocks
        decoder_params = 0
        for layer_idx in range(n_layer):
            # Self-attention in decoder
            decoder_attention_params = 4 * diffusion_step_embed_dim * diffusion_step_embed_dim
            # MLP in decoder
            decoder_mlp_params = (diffusion_step_embed_dim * mlp_hidden_dim + mlp_hidden_dim +
                                 mlp_hidden_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)
            # Layer norms (2 per block)
            decoder_ln_params = 2 * 2 * diffusion_step_embed_dim
            # AdaLN modulation networks (4 modulation networks per block)
            # Each modulation network: hidden_dim -> hidden_dim
            adaln_params = 4 * (diffusion_step_embed_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)

            decoder_params += decoder_attention_params + decoder_mlp_params + decoder_ln_params + adaln_params

        # Time network
        time_net_params = (diffusion_step_embed_dim * diffusion_step_embed_dim + diffusion_step_embed_dim +  # first layer
                          diffusion_step_embed_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)   # second layer

        # Final layer with AdaLN
        final_layer_params = (diffusion_step_embed_dim * action_dim + action_dim +  # linear output
                             diffusion_step_embed_dim * (2 * diffusion_step_embed_dim) + 2 * diffusion_step_embed_dim)  # AdaLN modulation

        # Positional encodings (learnable decoder positions)
        pos_emb_params = horizon * diffusion_step_embed_dim  # dec_pos parameter

        transformer_block_params = encoder_params + decoder_params + time_net_params + final_layer_params + pos_emb_params

    elif architecture == "mmdit":
        # MMDiT blocks (corrected calculation)
        for layer_idx in range(n_layer):
            # Layer norms (4 per block: 2 for action, 2 for condition)
            # Each layer norm has 2 * dim parameters (weight + bias)
            layer_norm_params = 4 * 2 * diffusion_step_embed_dim

            # Joint attention (single attention mechanism)
            # Q, K, V projections
            attention_params = 3 * diffusion_step_embed_dim * diffusion_step_embed_dim
            # Output projection
            attention_params += diffusion_step_embed_dim * diffusion_step_embed_dim

            # Separate MLPs for action and condition
            mlp_hidden_dim = 4 * diffusion_step_embed_dim
            # Each MLP: input->hidden, hidden->output (with biases)
            mlp_params = (diffusion_step_embed_dim * mlp_hidden_dim + mlp_hidden_dim +  # first layer
                         mlp_hidden_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)  # second layer

            # Two MLPs (action and condition)
            total_mlp_params = 2 * mlp_params

            block_params = layer_norm_params + attention_params + total_mlp_params
            transformer_block_params += block_params
    else:
        # Standard transformer blocks
        for layer_idx in range(n_layer):
            # Standard transformer block calculation
            layer_norm_params = 2 * 2 * diffusion_step_embed_dim  # 2 layer norms
            attention_params = 4 * diffusion_step_embed_dim * diffusion_step_embed_dim  # Q, K, V, O
            mlp_hidden_dim = 4 * diffusion_step_embed_dim
            mlp_params = (diffusion_step_embed_dim * mlp_hidden_dim + mlp_hidden_dim +
                         mlp_hidden_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)

            block_params = layer_norm_params + attention_params + mlp_params
            transformer_block_params += block_params
    
    # DC tokens (SoftREPA) - only for MMDiT with DC
    dc_token_params = 0
    if architecture == "mmdit" and n_dc_layers > 0:
        dc_token_params = n_dc_layers * n_dc_tokens * diffusion_step_embed_dim

        if use_dc_t:
            # Time-dependent DC tokens (embedding table)
            dc_t_params = 100 * (diffusion_step_embed_dim * n_dc_layers)  # 100 timestep buckets
            dc_token_params += dc_t_params
    
    # Output layers
    output_norm_params = diffusion_step_embed_dim
    output_proj_params = diffusion_step_embed_dim * action_dim
    
    # Vision encoder (approximate)
    vision_params = 0
    if vision_backbone == "resnet18":
        vision_params = 11_689_512  # ResNet18 parameters
    elif vision_backbone == "resnet50":
        vision_params = 25_557_032  # ResNet50 parameters
    elif vision_backbone == "resnet101":
        vision_params = 44_549_160  # ResNet101 parameters
    
    # Spatial softmax and other vision components
    vision_extra_params = 1000  # Approximate
    
    # Total calculation
    if architecture == "dit":
        # Complete DiT has different structure with progressive encoders
        # Action projection: ac_dim -> ac_dim -> hidden_dim (2-layer MLP)
        action_proj_params = (action_dim * action_dim + action_dim +  # first layer
                             action_dim * diffusion_step_embed_dim + diffusion_step_embed_dim)  # second layer

        # Condition projection: cond_dim -> hidden_dim
        cond_proj_params = cond_dim * diffusion_step_embed_dim + diffusion_step_embed_dim

        transformer_params = (
            action_proj_params +
            cond_proj_params +
            transformer_block_params  # includes encoders, decoders, time net, final layer, pos emb
        )
    else:
        transformer_params = (
            action_proj_params +
            cond_proj_params +
            action_pos_emb_params +
            transformer_block_params +
            dc_token_params +
            output_norm_params +
            output_proj_params
        )
    
    total_params = transformer_params + vision_params + vision_extra_params
    
    # Print breakdown
    print(f"📊 Parameter Breakdown ({architecture.upper()}):")
    print(f"   Action projection:     {action_proj_params:,}")
    print(f"   Condition projection:  {cond_proj_params:,}")
    print(f"   Position embeddings:   {action_pos_emb_params:,}")
    print(f"   Transformer blocks:    {transformer_block_params:,}")
    if architecture == "mmdit" and dc_token_params > 0:
        print(f"   DC tokens (SoftREPA):  {dc_token_params:,}")
    if architecture != "dit":
        print(f"   Output layers:         {output_norm_params + output_proj_params:,}")
    print(f"   Vision encoder:        {vision_params:,}")
    print(f"   Vision extras:         {vision_extra_params:,}")
    print("-" * 50)
    print(f"🎯 Total Parameters:      {total_params:,} ({total_params/1e6:.1f}M)")
    
    return total_params


def suggest_config_for_target(target_params_m=200):
    """Suggest configurations to reach target parameter count."""
    
    print(f"\n🎯 Suggesting configurations for ~{target_params_m}M parameters:")
    print("=" * 60)
    
    configs = [
        # DiT configurations (more reasonable sizes)
        {
            "name": "DiT 200M",
            "diffusion_step_embed_dim": 512,
            "n_layer": 16,
            "n_head": 8,
            "n_dc_tokens": 0,
            "n_dc_layers": 0,
            "vision_backbone": "resnet50",
            "architecture": "dit"
        },
        {
            "name": "DiT Large 200M",
            "diffusion_step_embed_dim": 640,
            "n_layer": 12,
            "n_head": 10,
            "n_dc_tokens": 0,
            "n_dc_layers": 0,
            "vision_backbone": "resnet101",
            "architecture": "dit"
        },
        # MMDiT configurations
        {
            "name": "MMDiT 200M",
            "diffusion_step_embed_dim": 768,
            "n_layer": 16,
            "n_head": 12,
            "n_dc_tokens": 8,
            "n_dc_layers": 12,
            "vision_backbone": "resnet50",
            "architecture": "mmdit"
        },
        {
            "name": "MMDiT XL 200M",
            "diffusion_step_embed_dim": 640,
            "n_layer": 18,
            "n_head": 10,
            "n_dc_tokens": 10,
            "n_dc_layers": 14,
            "vision_backbone": "resnet101",
            "architecture": "mmdit"
        }
    ]
    
    for config in configs:
        print(f"\n📋 {config['name']}:")
        params = calculate_mmdit_params(**{k: v for k, v in config.items() if k != 'name'})
        print(f"   Parameters: {params/1e6:.1f}M")
        print(f"   Config: dim={config['diffusion_step_embed_dim']}, layers={config['n_layer']}, heads={config['n_head']}")
        print(f"   DC: {config['n_dc_tokens']} tokens × {config['n_dc_layers']} layers")


def main():
    parser = argparse.ArgumentParser(description="Calculate MMDiT parameters")
    parser.add_argument("--dim", type=int, default=1024, help="Hidden dimension")
    parser.add_argument("--layers", type=int, default=24, help="Number of layers")
    parser.add_argument("--heads", type=int, default=16, help="Number of attention heads")
    parser.add_argument("--dc_tokens", type=int, default=16, help="Number of DC tokens")
    parser.add_argument("--dc_layers", type=int, default=18, help="Number of DC layers")
    parser.add_argument("--vision", type=str, default="resnet50", choices=["resnet18", "resnet50", "resnet101"])
    parser.add_argument("--suggest", action="store_true", help="Suggest configurations for 200M parameters")
    
    args = parser.parse_args()
    
    if args.suggest:
        suggest_config_for_target(200)
    else:
        calculate_mmdit_params(
            diffusion_step_embed_dim=args.dim,
            n_layer=args.layers,
            n_head=args.heads,
            n_dc_tokens=args.dc_tokens,
            n_dc_layers=args.dc_layers,
            vision_backbone=args.vision
        )


if __name__ == "__main__":
    main()
