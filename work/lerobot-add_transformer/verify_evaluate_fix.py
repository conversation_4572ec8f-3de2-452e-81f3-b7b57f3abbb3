#!/usr/bin/env python

"""
简单验证：检查evaluate_model函数的修复
"""

import sys
import ast
import inspect

# 添加项目路径
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

def verify_evaluate_model_fix():
    """验证evaluate_model函数的修复"""
    
    print("🔍 验证evaluate_model函数修复...")
    
    try:
        # 导入函数
        from train_dc_with_dataset import evaluate_model
        
        # 检查函数签名
        sig = inspect.signature(evaluate_model)
        print(f"✅ 函数签名: {sig}")
        
        # 检查第一个参数是否为DiffusionModel_DC
        first_param = list(sig.parameters.values())[0]
        print(f"✅ 第一个参数: {first_param.name}: {first_param.annotation}")
        
        if 'DiffusionModel_DC' in str(first_param.annotation):
            print("✅ 第一个参数类型正确 (DiffusionModel_DC)")
        else:
            print("❌ 第一个参数类型不正确")
            return False
        
        # 检查函数源码中的关键修复
        source = inspect.getsource(evaluate_model)
        
        # 检查是否使用了传入的diffusion_model参数
        if 'load_dc_weights_from_checkpoint(diffusion_model, pth_path)' in source:
            print("✅ 正确使用传入的diffusion_model参数")
        else:
            print("❌ 未正确使用传入的diffusion_model参数")
            return False
        
        # 检查是否替换了策略的diffusion模型
        if 'base_policy.diffusion = diffusion_model' in source:
            print("✅ 正确替换策略的diffusion模型")
        else:
            print("❌ 未正确替换策略的diffusion模型")
            return False
        
        print("\n🎉 evaluate_model函数修复验证通过!")
        
        # 输出修复要点
        print("\n📋 修复要点:")
        print("1. ✅ 函数现在正确使用传入的DiffusionModel_DC参数")
        print("2. ✅ 不再重新创建普通的DiffusionPolicy")
        print("3. ✅ DC权重直接加载到传入的diffusion_model中")
        print("4. ✅ 基础策略的diffusion模型被替换为DC模型")
        
        print("\n🚀 现在应该可以正常运行train_dc_with_dataset.py了!")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_load_dc_weights_safety():
    """检查load_dc_weights_from_checkpoint的安全性"""
    
    print("\n🔍 检查load_dc_weights_from_checkpoint的安全性...")
    
    try:
        # 读取load_pretrained_dc.py文件
        with open('/home/<USER>/work/lerobot-add_transformer/lerobot/scripts/load_pretrained_dc.py', 'r') as f:
            content = f.read()
        
        # 检查是否有dc_t_tokens的安全检查
        if 'dc_t_tokens is not None' in content:
            print("✅ load_dc_weights_from_checkpoint包含dc_t_tokens安全检查")
        else:
            print("❌ load_dc_weights_from_checkpoint缺少dc_t_tokens安全检查")
            return False
        
        # 检查是否有hasattr检查
        if 'hasattr(' in content and 'dc_t_tokens' in content:
            print("✅ 包含hasattr检查")
        else:
            print("❌ 缺少hasattr检查")
            return False
        
        print("✅ load_dc_weights_from_checkpoint安全性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 安全性检查失败: {e}")
        return False


if __name__ == "__main__":
    print("🧪 开始验证evaluate_model修复...")
    
    success1 = verify_evaluate_model_fix()
    success2 = check_load_dc_weights_safety()
    
    if success1 and success2:
        print("\n🎉 所有验证通过! 修复应该有效。")
        print("\n💡 建议:")
        print("1. 重新运行train_dc_with_dataset.py")
        print("2. 如果仍有问题，检查DC checkpoint文件是否包含正确的权重")
        print("3. 确保use_dc_t参数与训练时一致")
    else:
        print("\n❌ 验证失败，需要进一步检查。")
        sys.exit(1)
