#!/usr/bin/env python
"""
简单的GradScaler修复测试脚本
测试不同的GradScaler使用模式
"""

import torch
import torch.nn as nn
from torch.cuda.amp import GradScaler, autocast

def test_gradscaler_patterns():
    """测试不同的GradScaler使用模式"""
    print("🧪 测试GradScaler使用模式")
    
    # 创建简单的测试模型
    model = nn.Linear(10, 1).cuda()
    optimizer = torch.optim.SGD(model.parameters(), lr=0.01)
    
    # 测试数据
    x = torch.randn(5, 10).cuda()
    y = torch.randn(5, 1).cuda()
    
    print("\n1. 测试错误模式（会报错）：只unscale不clip")
    try:
        scaler = GradScaler()
        optimizer.zero_grad()
        
        with autocast():
            loss = nn.functional.mse_loss(model(x), y)
        
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)  # ❌ 只unscale但不做任何操作
        scaler.step(optimizer)      # ❌ 会报错
        scaler.update()
        
        print("  ❌ 意外成功 - 这应该会报错")
    except Exception as e:
        print(f"  ✅ 预期的错误: {type(e).__name__}: {e}")
    
    print("\n2. 测试正确模式1：不使用梯度裁剪")
    try:
        scaler = GradScaler()
        optimizer.zero_grad()
        
        with autocast():
            loss = nn.functional.mse_loss(model(x), y)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)      # ✅ 直接step
        scaler.update()
        
        print("  ✅ 成功！")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    
    print("\n3. 测试正确模式2：使用梯度裁剪")
    try:
        scaler = GradScaler()
        optimizer.zero_grad()
        
        with autocast():
            loss = nn.functional.mse_loss(model(x), y)
        
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)  # ✅ 为梯度裁剪而unscale
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        scaler.step(optimizer)
        scaler.update()
        
        print("  ✅ 成功！")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    
    print("\n4. 测试修复后的条件模式（use_grad_clip=True）")
    try:
        scaler = GradScaler()
        optimizer.zero_grad()
        use_grad_clip = True
        
        with autocast():
            loss = nn.functional.mse_loss(model(x), y)
        
        scaler.scale(loss).backward()
        
        if use_grad_clip:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        print("  ✅ 成功！")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    
    print("\n5. 测试修复后的条件模式（use_grad_clip=False）")
    try:
        scaler = GradScaler()
        optimizer.zero_grad()
        use_grad_clip = False
        
        with autocast():
            loss = nn.functional.mse_loss(model(x), y)
        
        scaler.scale(loss).backward()
        
        if use_grad_clip:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        print("  ✅ 成功！")
    except Exception as e:
        print(f"  ❌ 错误: {e}")


def main():
    if torch.cuda.is_available():
        print("🚀 开始GradScaler修复测试")
        test_gradscaler_patterns()
        print("\n🎉 测试完成！")
        print("\n📝 总结:")
        print("  - 修复前：调用unscale_()但不进行梯度裁剪会导致错误")
        print("  - 修复后：通过use_grad_clip参数控制是否使用梯度裁剪")
        print("  - 推荐：use_grad_clip=True（更稳定的训练）")
    else:
        print("❌ 需要CUDA支持来测试GradScaler")


if __name__ == "__main__":
    main() 