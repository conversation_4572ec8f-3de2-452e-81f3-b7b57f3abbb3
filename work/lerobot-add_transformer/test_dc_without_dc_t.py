#!/usr/bin/env python

"""
测试脚本：验证不使用dc_t token的DC训练功能
"""

import os
import sys
import logging
import torch
import torch.nn as nn
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

def test_dc_without_dc_t():
    """测试不使用dc_t token的DC模型创建和训练"""
    
    # 设置logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(message)s'
    )
    
    try:
        # 导入必要的模块
        from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
        from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
        
        logging.info("✅ 模块导入成功")
        
        # 创建基本配置
        config = DiffusionConfig()
        # 设置基本参数
        config.horizon = 16
        config.n_obs_steps = 2
        config.diffusion_step_embed_dim = 256
        config.n_layer = 4
        config.n_head = 4
        config.n_cond_layers = 2
        config.use_transformer = True

        # 设置输入输出形状
        config.input_shapes = {
            "observation.state": [32],
        }
        config.output_shapes = {
            "action": [2],
        }

        # 设置特征配置（模拟robot_state_feature等）
        from types import SimpleNamespace
        config.robot_state_feature = SimpleNamespace(shape=[32])
        config.action_feature = SimpleNamespace(shape=[2])
        config.image_features = []  # 不使用图像特征
        
        logging.info("✅ 配置创建成功")
        
        # 测试1: 创建不使用dc_t的DiffusionModel_DC
        logging.info("\n🧪 测试1: 创建不使用dc_t的DiffusionModel_DC")
        model_without_dc_t = DiffusionModel_DC(
            config=config,
            n_dc_tokens=4,
            n_dc_layers=3,
            use_dc_t=False,  # 关键：不使用dc_t
            cond_dim=32
        )
        
        logging.info(f"✅ 模型创建成功!")
        logging.info(f"   DC tokens: {model_without_dc_t.n_dc_tokens}")
        logging.info(f"   DC layers: {model_without_dc_t.n_dc_layers}")
        logging.info(f"   Use DC_t: {model_without_dc_t.use_dc_t}")
        
        # 检查dc_t_tokens是否为None
        if hasattr(model_without_dc_t.net, 'dc_t_tokens'):
            if model_without_dc_t.net.dc_t_tokens is None:
                logging.info("✅ dc_t_tokens正确设置为None")
            else:
                logging.warning(f"⚠️ dc_t_tokens应该为None，但实际为: {type(model_without_dc_t.net.dc_t_tokens)}")
        else:
            logging.info("✅ 模型没有dc_t_tokens属性")
        
        # 测试2: 创建使用dc_t的DiffusionModel_DC进行对比
        logging.info("\n🧪 测试2: 创建使用dc_t的DiffusionModel_DC进行对比")
        model_with_dc_t = DiffusionModel_DC(
            config=config,
            n_dc_tokens=4,
            n_dc_layers=3,
            use_dc_t=True,  # 使用dc_t
            cond_dim=32
        )
        
        logging.info(f"✅ 对比模型创建成功!")
        logging.info(f"   Use DC_t: {model_with_dc_t.use_dc_t}")
        
        # 检查dc_t_tokens是否存在
        if hasattr(model_with_dc_t.net, 'dc_t_tokens') and model_with_dc_t.net.dc_t_tokens is not None:
            logging.info(f"✅ dc_t_tokens正确创建: {model_with_dc_t.net.dc_t_tokens}")
        else:
            logging.warning("⚠️ dc_t_tokens应该存在但未找到")
        
        # 测试3: 参数统计对比
        logging.info("\n🧪 测试3: 参数统计对比")
        
        def get_param_stats(model, model_name):
            total_params = sum(p.numel() for p in model.parameters())
            dc_params = sum(p.numel() for name, p in model.named_parameters() if 'dc' in name.lower())
            
            logging.info(f"{model_name}:")
            logging.info(f"  总参数量: {total_params:,}")
            logging.info(f"  DC参数量: {dc_params:,}")
            logging.info(f"  基础参数量: {total_params - dc_params:,}")
            
            # 列出DC相关参数
            dc_param_names = [name for name, p in model.named_parameters() if 'dc' in name.lower()]
            logging.info(f"  DC参数列表: {dc_param_names}")
            
            return total_params, dc_params
        
        total1, dc1 = get_param_stats(model_without_dc_t, "不使用dc_t的模型")
        total2, dc2 = get_param_stats(model_with_dc_t, "使用dc_t的模型")
        
        logging.info(f"\n📊 参数差异:")
        logging.info(f"  DC参数差异: {dc2 - dc1:,} (dc_t相关参数)")
        
        # 测试4: 前向传播测试
        logging.info("\n🧪 测试4: 前向传播测试")
        
        # 创建模拟输入
        batch_size = 2
        sample_batch = {
            'action': torch.randn(batch_size, config.horizon, config.output_shapes['action'][0]),
            'observation.state': torch.randn(batch_size, config.n_obs_steps, config.input_shapes['observation.state'][0])
        }
        
        # 测试不使用dc_t的模型
        try:
            error_matrix1 = model_without_dc_t.compute_contrastive_error(sample_batch, use_dc=True)
            logging.info(f"✅ 不使用dc_t的模型前向传播成功: {error_matrix1.shape}")
        except Exception as e:
            logging.error(f"❌ 不使用dc_t的模型前向传播失败: {e}")
        
        # 测试使用dc_t的模型
        try:
            error_matrix2 = model_with_dc_t.compute_contrastive_error(sample_batch, use_dc=True)
            logging.info(f"✅ 使用dc_t的模型前向传播成功: {error_matrix2.shape}")
        except Exception as e:
            logging.error(f"❌ 使用dc_t的模型前向传播失败: {e}")
        
        # 测试5: DC参数检查函数
        logging.info("\n🧪 测试5: DC参数检查函数")
        
        def check_dc_params_safely(model, model_name):
            logging.info(f"{model_name} DC参数检查:")
            for name, param in model.named_parameters():
                # 使用修改后的安全检查逻辑
                is_dc_param = (
                    'dc_token' in name or
                    (hasattr(model.net, 'dc_tokens') and param is model.net.dc_tokens) or
                    (hasattr(model.net, 'dc_t_tokens') and model.net.dc_t_tokens is not None and param is model.net.dc_t_tokens.weight)
                )
                
                if is_dc_param:
                    logging.info(f"  DC参数: {name}, 形状: {param.shape}, 需要梯度: {param.requires_grad}")
        
        check_dc_params_safely(model_without_dc_t, "不使用dc_t的模型")
        check_dc_params_safely(model_with_dc_t, "使用dc_t的模型")
        
        logging.info("\n✅ 所有测试完成!")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_dc_without_dc_t()
    if success:
        print("\n🎉 测试成功! 不使用dc_t token的功能正常工作。")
    else:
        print("\n💥 测试失败! 请检查错误信息。")
