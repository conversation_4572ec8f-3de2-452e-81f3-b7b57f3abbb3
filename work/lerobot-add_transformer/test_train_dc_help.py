#!/usr/bin/env python

"""
简单测试：验证train_dc_with_dataset.py的修复
"""

import subprocess
import sys

def test_train_dc_help():
    """测试train_dc_with_dataset.py的帮助信息"""
    
    print("🧪 测试train_dc_with_dataset.py的修复")
    
    try:
        # 测试帮助信息
        result = subprocess.run([
            sys.executable, "train_dc_with_dataset.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ train_dc_with_dataset.py可以正常运行")
            
            # 检查--use_dc_t参数
            if "--use_dc_t" in result.stdout:
                print("✅ --use_dc_t参数已正确添加")
                
                # 查找相关行
                lines = result.stdout.split('\n')
                for line in lines:
                    if "--use_dc_t" in line or "time-dependent DC tokens" in line:
                        print(f"   {line.strip()}")
                        
                return True
            else:
                print("❌ --use_dc_t参数未找到")
                return False
        else:
            print(f"❌ train_dc_with_dataset.py运行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_transformer_dc_sampler_import():
    """测试transformer_dc_sampler模块的导入"""
    
    print("\n🧪 测试transformer_dc_sampler模块导入")
    
    test_script = '''
import sys
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

try:
    from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
    print("✅ DiffusionModel_DC导入成功")
    
    # 检查关键方法是否存在
    methods = ['freeze_base_model', 'get_dc_parameters', 'initialize_dc_tokens', 'check_dc_tokens_status']
    for method in methods:
        if hasattr(DiffusionModel_DC, method):
            print(f"✅ 方法 {method} 存在")
        else:
            print(f"❌ 方法 {method} 不存在")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        result = subprocess.run([
            sys.executable, "-c", test_script
        ], capture_output=True, text=True, timeout=30)
        
        print("导入测试结果:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试train_dc_with_dataset.py的修复")
    
    success1 = test_train_dc_help()
    success2 = test_transformer_dc_sampler_import()
    
    if success1 and success2:
        print("\n🎉 所有测试通过! 修复成功。")
        print("\n📝 现在可以使用以下命令:")
        print("  不使用dc_t: python train_dc_with_dataset.py --checkpoint /path/to/checkpoint")
        print("  使用dc_t:   python train_dc_with_dataset.py --checkpoint /path/to/checkpoint --use_dc_t")
    else:
        print("\n❌ 部分测试失败，请检查修复。")
        sys.exit(1)
