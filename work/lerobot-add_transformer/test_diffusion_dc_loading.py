#!/usr/bin/env python3
"""
测试DiffusionModel_DC加载逻辑

验证简化后的DiffusionModel_DC类是否能正确创建和加载预训练模型
"""

import torch
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    create_diffusion_model_dc
)
from lerobot.configs.policies import PreTrainedConfig


def test_basic_creation():
    """测试1: 基本创建DiffusionModel_DC"""
    print("🧪 测试1: 基本创建DiffusionModel_DC")
    
    try:
        # 创建基本配置
        config = DiffusionConfig(
            horizon=16,
            n_obs_steps=2,
            diffusion_step_embed_dim=256,
            n_layer=4,
            n_head=4,
            n_cond_layers=2,
            use_transformer=True,
        )
        
        # 创建DiffusionModel_DC
        model = DiffusionModel_DC(
            config=config,
            n_dc_tokens=4,
            n_dc_layers=3,
            use_dc_t=True,
            cond_dim=66  # 64维状态 + 2维额外特征
        )
        
        print(f"✅ 模型创建成功!")
        print(f"   DC tokens: {model.n_dc_tokens}")
        print(f"   DC layers: {model.n_dc_layers}")
        print(f"   Use DC_t: {model.use_dc_t}")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        dc_params = sum(p.numel() for name, p in model.named_parameters() if 'dc' in name.lower())
        
        print(f"   总参数量: {total_params:,}")
        print(f"   DC参数量: {dc_params:,}")
        print(f"   基础参数量: {total_params - dc_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_forward_pass():
    """测试2: 前向传播"""
    print("\n🧪 测试2: 前向传播测试")
    
    try:
        # 创建模型
        config = DiffusionConfig(
            horizon=16,
            n_obs_steps=2,
            diffusion_step_embed_dim=128,
            n_layer=2,
            n_head=4,
            n_cond_layers=2,
            use_transformer=True,
            input_shapes={
                "observation.state": [32],
            },
            output_shapes={
                "action": [2],
            }
        )
        
        model = DiffusionModel_DC(
            config=config,
            n_dc_tokens=2,
            n_dc_layers=2,
            use_dc_t=True,
            cond_dim=32
        )
        
        model.eval()
        
        # 创建测试输入
        batch_size = 2
        horizon = 16
        action_dim = 2
        cond_dim = 32
        n_obs_steps = 2
        
        sample = torch.randn(batch_size, horizon, action_dim)
        timestep = torch.randint(0, 100, (batch_size,))
        global_cond = torch.randn(batch_size, n_obs_steps * cond_dim)
        
        print(f"   输入形状:")
        print(f"     sample: {sample.shape}")
        print(f"     timestep: {timestep.shape}")
        print(f"     global_cond: {global_cond.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = model(sample, timestep, global_cond)
        
        print(f"   输出形状: {output.shape}")
        print(f"   输出统计: min={output.min():.4f}, max={output.max():.4f}, mean={output.mean():.4f}")
        print("✅ 前向传播成功!")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pretrained_loading():
    """测试3: 预训练模型加载"""
    print("\n🧪 测试3: 预训练模型加载测试")
    
    # 检查预训练模型路径
    checkpoint_paths = [
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if Path(path).exists():
            checkpoint_path = path
            break
    
    if checkpoint_path is None:
        print("⚠️ 未找到预训练模型检查点，跳过此测试")
        return True
    
    try:
        print(f"   使用检查点: {checkpoint_path}")
        
        # 加载配置
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        if config_path.exists():
            config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        else:
            print("⚠️ 配置文件不存在，使用默认配置")
            config = DiffusionConfig(
                horizon=16,
                n_obs_steps=2,
                diffusion_step_embed_dim=256,
                n_layer=6,
                n_head=8,
                n_cond_layers=4,
                use_transformer=True,
                input_shapes={
                    "observation.state": [64],
                },
                output_shapes={
                    "action": [2],
                }
            )
        
        # 测试from_pretrained方法
        print("   测试from_pretrained方法...")
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        print("✅ 预训练模型加载成功!")
        
        # 测试模型功能
        model.eval()
        batch_size = 1
        sample = torch.randn(batch_size, 16, 2)
        timestep = torch.randint(0, 100, (batch_size,))
        global_cond = torch.randn(batch_size, 132)  # 假设条件维度
        
        with torch.no_grad():
            output = model(sample, timestep, global_cond)
        
        print(f"   预训练模型推理成功，输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预训练模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_factory_function():
    """测试4: 工厂函数"""
    print("\n🧪 测试4: create_diffusion_model_dc工厂函数")
    
    try:
        config = DiffusionConfig(
            horizon=16,
            n_obs_steps=2,
            diffusion_step_embed_dim=128,
            n_layer=3,
            n_head=4,
            n_cond_layers=2,
            use_transformer=True,
            input_shapes={
                "observation.state": [32],
            },
            output_shapes={
                "action": [2],
            }
        )
        
        # 测试不带预训练模型的工厂函数
        model = create_diffusion_model_dc(
            config=config,
            n_dc_tokens=3,
            n_dc_layers=2,
            use_dc_t=True,
            pretrained_model_path=None
        )
        
        print("✅ 工厂函数创建成功!")
        print(f"   模型类型: {type(model).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工厂函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dc_functionality():
    """测试5: DC tokens功能"""
    print("\n🧪 测试5: DC tokens功能测试")
    
    try:
        config = DiffusionConfig(
            horizon=16,
            n_obs_steps=2,
            diffusion_step_embed_dim=128,
            n_layer=4,
            n_head=4,
            n_cond_layers=2,
            use_transformer=True,
            input_shapes={
                "observation.state": [32],
            },
            output_shapes={
                "action": [2],
            }
        )
        
        model = DiffusionModel_DC(
            config=config,
            n_dc_tokens=4,
            n_dc_layers=3,
            use_dc_t=True,
            cond_dim=32
        )
        
        # 测试DC tokens初始化
        model.initialize_dc_tokens()
        print("✅ DC tokens初始化成功!")
        
        # 测试参数冻结
        model.freeze_base_model()
        print("✅ 基础模型冻结成功!")
        
        # 测试DC参数获取
        dc_params = model.get_dc_parameters()
        print(f"✅ DC参数获取成功，共{len(dc_params)}个参数")
        
        # 测试参数统计
        model.print_parameter_stats()
        print("✅ 参数统计成功!")
        
        return True
        
    except Exception as e:
        print(f"❌ DC tokens功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🚀 DiffusionModel_DC加载逻辑验证测试\n")
    
    tests = [
        ("基本创建", test_basic_creation),
        ("前向传播", test_forward_pass),
        ("预训练加载", test_pretrained_loading),
        ("工厂函数", test_factory_function),
        ("DC功能", test_dc_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print("=" * 60)
        success = test_func()
        results.append((test_name, success))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！DiffusionModel_DC加载逻辑工作正常")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
