#!/usr/bin/env python

"""
Simple test script to verify DC weight loading logic
"""

import torch
import json
import os
from pathlib import Path

def load_and_analyze_pth(pth_path: str):
    """加载并分析.pth文件"""
    print(f"=== 分析PTH文件: {pth_path} ===")
    
    if not os.path.exists(pth_path):
        print(f"错误: 文件不存在 {pth_path}")
        return None
    
    # 加载文件
    try:
        data = torch.load(pth_path, map_location='cpu')
        print(f"成功加载PTH文件")
    except Exception as e:
        print(f"加载失败: {e}")
        return None
    
    # 分析结构
    print(f"数据类型: {type(data)}")
    
    if isinstance(data, dict):
        print(f"字典键: {list(data.keys())}")
        
        # 如果是训练检查点格式
        if 'model_state_dict' in data:
            print(f"检测到训练检查点格式")
            model_weights = data['model_state_dict']
            print(f"模型权重键数量: {len(model_weights)}")
        else:
            print(f"直接的权重字典格式")
            model_weights = data
        
        # 寻找DC相关权重
        dc_keys = [k for k in model_weights.keys() if 'dc' in k.lower()]
        print(f"\nDC相关权重 ({len(dc_keys)} 个):")
        for key in dc_keys:
            weight = model_weights[key]
            print(f"  {key}: {weight.shape}, dtype={weight.dtype}")
        
        return model_weights
    else:
        print(f"非字典格式: {type(data)}")
        return None

def load_and_analyze_config(config_path: str):
    """加载并分析配置文件"""
    print(f"\n=== 分析配置文件: {config_path} ===")
    
    if not os.path.exists(config_path):
        print(f"错误: 配置文件不存在 {config_path}")
        return None
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    print(f"配置键: {list(config.keys())}")
    
    # DC相关配置
    dc_config = {}
    for key in config.keys():
        if 'dc' in key.lower() or 'use_dc' in key:
            dc_config[key] = config[key]
    
    print(f"DC相关配置:")
    for key, value in dc_config.items():
        print(f"  {key}: {value}")
    
    return config

def test_dc_weight_loading():
    """测试DC权重加载流程"""
    print("=== DC权重加载流程测试 ===")
    
    # 路径配置
    base_model_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000/pretrained_model"
    dc_pth_path = "/home/<USER>/work/lerobot-add_transformer/dc_eval_setup/model.pth"
    
    # 1. 分析基础模型配置
    config_path = os.path.join(base_model_path, "config.json")
    base_config = load_and_analyze_config(config_path)
    
    # 2. 分析DC权重文件
    dc_weights = load_and_analyze_pth(dc_pth_path)
    
    if base_config is None or dc_weights is None:
        print("无法完成测试，文件加载失败")
        return
    
    # 3. 模拟配置修改
    print(f"\n=== 模拟配置修改 ===")
    modified_config = base_config.copy()
    
    # 启用DC
    original_use_dc = modified_config.get('use_dc', False)
    modified_config['use_dc'] = True
    modified_config['use_mmdit_dc'] = False  # 确保使用TransformerForDiffusion_DC
    
    print(f"use_dc: {original_use_dc} -> {modified_config['use_dc']}")
    print(f"use_mmdit_dc: {modified_config.get('use_mmdit_dc', False)}")
    print(f"n_dc_tokens: {modified_config.get('n_dc_tokens', 'not set')}")
    print(f"n_dc_layers: {modified_config.get('n_dc_layers', 'not set')}")
    print(f"use_dc_t: {modified_config.get('use_dc_t', 'not set')}")
    
    # 4. 模拟权重匹配
    print(f"\n=== 模拟权重匹配 ===")
    dc_keys = [k for k in dc_weights.keys() if 'dc' in k.lower()]
    
    expected_keys = ['dc_tokens', 'dc_t_tokens']  # TransformerForDiffusion_DC中期望的键
    
    print(f"期望的DC权重键: {expected_keys}")
    print(f"PTH文件中的DC键: {dc_keys}")
    
    # 尝试匹配
    matches = []
    for expected_key in expected_keys:
        found = False
        for pth_key in dc_keys:
            if expected_key in pth_key:
                matches.append((expected_key, pth_key))
                found = True
                break
        if not found:
            print(f"警告: 未找到匹配 {expected_key}")
    
    print(f"匹配结果:")
    for expected, found in matches:
        weight = dc_weights[found]
        print(f"  {expected} <- {found}: {weight.shape}")
    
    print(f"\n=== 测试完成 ===")
    print(f"建议:")
    print(f"1. 确保.pth文件包含正确命名的DC权重")
    print(f"2. 在模型初始化后，手动加载这些权重")
    print(f"3. 验证权重形状匹配")

if __name__ == "__main__":
    test_dc_weight_loading() 