#!/bin/bash

# 示例脚本：展示如何使用train_dc_with_dataset.py训练不使用dc_t token的模型

echo "🚀 SoftREPA DC Token训练示例 - 不使用dc_t tokens"
echo "=================================================="

# 设置环境变量
export CUDA_VISIBLE_DEVICES=2
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export PYTHONPATH=/home/<USER>/work/lerobot-add_transformer

# 检查点路径（请根据实际情况修改）
CHECKPOINT_PATH="/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000"

# 检查检查点是否存在
if [ ! -d "$CHECKPOINT_PATH" ]; then
    echo "❌ 错误: 检查点路径不存在: $CHECKPOINT_PATH"
    echo "请修改CHECKPOINT_PATH变量为正确的路径"
    exit 1
fi

echo "✅ 使用检查点: $CHECKPOINT_PATH"

# 训练参数
DATASET="lerobot/pusht"
N_DC_TOKENS=7
N_DC_LAYERS=6
BATCH_SIZE=2
LEARNING_RATE=1e-3
STEPS=10000
LOG_FREQ=100
SAVE_FREQ=1000
EVAL_FREQ=5000

echo ""
echo "📋 训练配置:"
echo "  数据集: $DATASET"
echo "  DC tokens: $N_DC_TOKENS tokens, $N_DC_LAYERS layers"
echo "  使用dc_t tokens: 否 (--use_dc_t 未设置)"
echo "  批次大小: $BATCH_SIZE"
echo "  学习率: $LEARNING_RATE"
echo "  训练步数: $STEPS"
echo "  日志频率: $LOG_FREQ"
echo "  保存频率: $SAVE_FREQ"
echo "  评估频率: $EVAL_FREQ"
echo ""

# 运行训练（不使用dc_t tokens）
echo "🎯 开始训练 (不使用dc_t tokens)..."
python train_dc_with_dataset.py \
    --checkpoint "$CHECKPOINT_PATH" \
    --dataset "$DATASET" \
    --n_dc_tokens $N_DC_TOKENS \
    --n_dc_layers $N_DC_LAYERS \
    --batch_size $BATCH_SIZE \
    --learning_rate $LEARNING_RATE \
    --steps $STEPS \
    --log_freq $LOG_FREQ \
    --save_freq $SAVE_FREQ \
    --eval_freq $EVAL_FREQ \
    --device cuda

# 注意：没有添加 --use_dc_t 参数，所以默认为False

echo ""
echo "✅ 训练完成!"
echo ""
echo "📝 对比说明:"
echo "  本次训练: 不使用dc_t tokens (更简单，参数更少)"
echo "  如需使用dc_t tokens，请添加 --use_dc_t 参数"
echo ""
echo "🔍 训练结果将保存在 dc_checkpoints/training_YYYYMMDD_HHMMSS/ 目录中"
