#!/usr/bin/env python3
"""
诊断DC token梯度消失问题的脚本
分析梯度传播路径和数值稳定性
"""

import torch
import torch.nn.functional as F
import numpy as np
import logging

sys.path.append(str(Path(__file__).parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_contrastive_loss_gradients():
    """分析对比损失的梯度特性"""
    print("🔍 分析对比损失梯度特性")

    # 模拟误差矩阵
    batch_size = 4
    error_matrix = torch.randn(batch_size, batch_size, requires_grad=True)

    # 对比损失参数
    temp = 0.07
    scale = 4.0

    # 计算对比损失
    logits = scale * torch.exp(-error_matrix / temp)
    targets = torch.arange(batch_size)
    loss = F.cross_entropy(logits, targets)

    print(f"误差矩阵: {error_matrix.detach()}")
    print(f"损失值: {loss.item():.6f}")

    # 计算梯度
    loss.backward()
    grad_norm = torch.norm(error_matrix.grad).item()

    print(f"误差矩阵梯度范数: {grad_norm:.2e}")
    print(f"误差矩阵梯度: {error_matrix.grad}")

    return grad_norm

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_gradient_magnitudes(model, batch):
    """分析梯度大小分布"""
    print("\n🔍 分析梯度大小分布")
    
    # 创建对比损失
    contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device='cpu')
    
    # 前向传播
    error_matrix = model.compute_contrastive_error(batch, use_dc=True)
    loss = contrastive_loss(error_matrix)
    
    print(f"📊 误差矩阵统计:")
    print(f"   形状: {error_matrix.shape}")
    print(f"   均值: {error_matrix.mean().item():.6f}")
    print(f"   标准差: {error_matrix.std().item():.6f}")
    print(f"   最小值: {error_matrix.min().item():.6f}")
    print(f"   最大值: {error_matrix.max().item():.6f}")
    
    print(f"📊 损失值: {loss.item():.6f}")
    
    # 反向传播
    model.zero_grad()
    loss.backward()
    
    # 分析梯度
    print(f"\n📈 梯度分析:")
    dc_grad_info = {}
    base_grad_info = {}
    
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = torch.norm(param.grad).item()
            grad_mean = param.grad.mean().item()
            grad_std = param.grad.std().item()
            grad_max = param.grad.max().item()
            grad_min = param.grad.min().item()
            
            grad_info = {
                'norm': grad_norm,
                'mean': grad_mean,
                'std': grad_std,
                'max': grad_max,
                'min': grad_min,
                'shape': param.grad.shape
            }
            
            is_dc_param = (
                'dc_token' in name or
                (hasattr(model.net, 'dc_tokens') and param is model.net.dc_tokens) or
                (hasattr(model.net, 'dc_t_tokens') and param is model.net.dc_t_tokens.weight)
            )
            
            if is_dc_param:
                dc_grad_info[name] = grad_info
                print(f"   DC {name}: norm={grad_norm:.2e}, mean={grad_mean:.2e}, std={grad_std:.2e}")
            else:
                base_grad_info[name] = grad_info
    
    print(f"\n📊 梯度统计:")
    print(f"   DC参数梯度数量: {len(dc_grad_info)}")
    print(f"   基础参数梯度数量: {len(base_grad_info)}")
    
    if dc_grad_info:
        dc_norms = [info['norm'] for info in dc_grad_info.values()]
        print(f"   DC梯度范数: 最小={min(dc_norms):.2e}, 最大={max(dc_norms):.2e}, 均值={np.mean(dc_norms):.2e}")
    
    return dc_grad_info, base_grad_info

def analyze_temperature_effect(model, batch):
    """分析温度参数对梯度的影响"""
    print("\n🌡️ 分析温度参数效应")
    
    temperatures = [0.01, 0.05, 0.07, 0.1, 0.2, 0.5, 1.0]
    
    for temp in temperatures:
        contrastive_loss = ContrastiveLoss(temp=temp, scale=4.0, device='cpu')
        
        # 前向传播
        error_matrix = model.compute_contrastive_error(batch, use_dc=True)
        loss = contrastive_loss(error_matrix)
        
        # 反向传播
        model.zero_grad()
        loss.backward()
        
        # 计算DC梯度范数
        dc_grad_norm = 0.0
        for name, param in model.named_parameters():
            if param.grad is not None and 'dc_token' in name:
                dc_grad_norm += torch.norm(param.grad).item() ** 2
        dc_grad_norm = dc_grad_norm ** 0.5
        
        print(f"温度 {temp:.3f}: 损失={loss.item():.4f}, DC梯度范数={dc_grad_norm:.2e}")

def test_direct_dc_loss(model, batch):
    """测试直接的DC token损失"""
    print("\n🎯 测试直接DC token损失")
    
    # 创建简单的DC token损失
    def simple_dc_loss(model):
        loss = 0.0
        if hasattr(model.net, 'dc_tokens'):
            # 简单的L2损失，鼓励DC tokens不为零
            loss += torch.sum(model.net.dc_tokens ** 2)
        if hasattr(model.net, 'dc_t_tokens'):
            loss += torch.sum(model.net.dc_t_tokens.weight ** 2)
        return loss
    
    # 测试直接损失的梯度
    loss = simple_dc_loss(model)
    print(f"直接DC损失: {loss.item():.6f}")
    
    model.zero_grad()
    loss.backward()
    
    # 检查梯度
    for name, param in model.named_parameters():
        if param.grad is not None and 'dc_token' in name:
            grad_norm = torch.norm(param.grad).item()
            print(f"直接损失 - {name}: 梯度范数={grad_norm:.2e}")

def main():
    """主函数"""
    print("🚀 DC token梯度问题诊断")
    
    # 创建模型和数据
    model = create_test_model()
    batch = create_test_batch(batch_size=4)
    
    print(f"✅ 模型创建成功")
    print(f"   DC tokens形状: {model.net.dc_tokens.shape}")
    print(f"   DC_t tokens形状: {model.net.dc_t_tokens.weight.shape}")
    
    # 运行诊断
    try:
        analyze_gradient_magnitudes(model, batch)
        analyze_temperature_effect(model, batch)
        test_direct_dc_loss(model, batch)
        
        print("\n" + "="*60)
        print("📊 诊断总结:")
        print("1. 检查梯度大小分布 - 完成")
        print("2. 检查温度参数效应 - 完成")
        print("3. 检查直接DC损失 - 完成")
        
        print("\n💡 建议的解决方案:")
        print("1. 大幅增加学习率 (1000倍以上)")
        print("2. 增加温度参数 (0.5或更高)")
        print("3. 增加scale参数 (10倍以上)")
        print("4. 添加直接的DC正则化损失")
        print("5. 使用SGD而不是Adam优化器")
        print("6. 完全移除梯度裁剪")
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
