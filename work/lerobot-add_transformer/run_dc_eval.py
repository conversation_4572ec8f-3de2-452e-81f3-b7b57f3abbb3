#!/usr/bin/env python

"""
Practical DC evaluation runner
使用方法:
python run_dc_eval.py --base_model_path outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000/pretrained_model --dc_pth_path dc_eval_setup/model.pth
"""

import argparse
import json
import os
import sys
import torch
import tempfile
import shutil
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="Run evaluation with DC weights from PTH")
    parser.add_argument("--base_model_path", type=str, required=True,
                       help="Path to base model directory (containing config.json and model.safetensors)")
    parser.add_argument("--dc_pth_path", type=str, required=True,
                       help="Path to DC weights .pth file")
    parser.add_argument("--env_type", type=str, default="pusht",
                       help="Environment type for evaluation")
    parser.add_argument("--n_episodes", type=int, default=10,
                       help="Number of evaluation episodes")
    parser.add_argument("--batch_size", type=int, default=10,
                       help="Evaluation batch size")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use")
    
    args = parser.parse_args()
    
    print("=== DC评估脚本启动 ===")
    
    # 验证路径
    base_model_path = Path(args.base_model_path)
    if not base_model_path.exists():
        print(f"错误: 基础模型路径不存在: {base_model_path}")
        return 1
        
    config_path = base_model_path / "config.json"
    safetensors_path = base_model_path / "model.safetensors"
    
    if not config_path.exists():
        print(f"错误: 配置文件不存在: {config_path}")
        return 1
        
    if not safetensors_path.exists():
        print(f"错误: safetensors文件不存在: {safetensors_path}")
        return 1
    
    dc_pth_path = Path(args.dc_pth_path)
    if not dc_pth_path.exists():
        print(f"错误: DC权重文件不存在: {dc_pth_path}")
        return 1
    
    print(f"✓ 所有文件验证通过")
    print(f"  基础模型: {base_model_path}")
    print(f"  DC权重: {dc_pth_path}")
    
    # 读取并修改配置
    print("\n=== 修改配置启用DC ===")
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # 保存原始配置
    original_use_dc = config.get('use_dc', False)
    original_use_mmdit_dc = config.get('use_mmdit_dc', False)
    
    # 启用DC
    config['use_dc'] = True
    config['use_mmdit_dc'] = False  # 确保使用TransformerForDiffusion_DC
    
    print(f"use_dc: {original_use_dc} → {config['use_dc']}")
    print(f"use_mmdit_dc: {original_use_mmdit_dc} → {config['use_mmdit_dc']}")
    print(f"n_dc_tokens: {config.get('n_dc_tokens', 4)}")
    print(f"n_dc_layers: {config.get('n_dc_layers', 6)}")
    print(f"use_dc_t: {config.get('use_dc_t', True)}")
    
    # 创建临时目录和修改的配置
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        temp_model_path = temp_path / "model_dc"
        temp_model_path.mkdir()
        
        # 复制文件到临时目录
        shutil.copy2(safetensors_path, temp_model_path / "model.safetensors")
        
        # 保存修改的配置
        temp_config_path = temp_model_path / "config.json"
        with open(temp_config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n=== 准备评估命令 ===")
        print(f"临时模型目录: {temp_model_path}")
        
        # 构建评估命令
        eval_cmd = [
            "conda", "activate", "lerobot", "&&",
            "export", f"PYTHONPATH={Path.cwd()}:$PYTHONPATH", "&&",
            "CUDA_VISIBLE_DEVICES=3", "python", "lerobot/scripts/eval.py",
            f"--policy.pretrained_path={temp_model_path}",
            f"--env.type={args.env_type}",
            f"--eval.batch_size={args.batch_size}",
            f"--eval.n_episodes={args.n_episodes}",
            f"--policy.device={args.device}",
            "--policy.use_amp=false"
        ]
        
        eval_cmd_str = " ".join(eval_cmd)
        print(f"\n评估命令:")
        print(f"{eval_cmd_str}")
        
        print(f"\n=== 分析DC权重文件 ===")
        try:
            pth_data = torch.load(dc_pth_path, map_location='cpu')
            if isinstance(pth_data, dict) and 'model_state_dict' in pth_data:
                model_weights = pth_data['model_state_dict']
                print(f"检测到训练检查点格式")
            else:
                model_weights = pth_data
                print(f"直接权重格式")
            
            dc_keys = [k for k in model_weights.keys() if 'dc' in k.lower()]
            print(f"找到 {len(dc_keys)} 个DC相关权重:")
            for key in dc_keys[:5]:  # 只显示前5个
                weight = model_weights[key]
                print(f"  {key}: {weight.shape}")
            if len(dc_keys) > 5:
                print(f"  ... 还有 {len(dc_keys) - 5} 个")
                
        except Exception as e:
            print(f"DC权重文件分析失败: {e}")
            return 1
        
        print(f"\n=== 执行评估 ===")
        print(f"注意: 这将运行标准评估，DC权重需要在eval.py中手动加载")
        print(f"建议: 修改 lerobot/scripts/eval.py 在policy创建后加载DC权重")
        
        # 执行命令
        print(f"\n要执行的命令:")
        print(f"cd {Path.cwd()}")
        print(f"{eval_cmd_str}")
        
        # 询问用户是否执行
        response = input(f"\n是否现在执行评估? (y/N): ")
        if response.lower() == 'y':
            os.system(eval_cmd_str)
        else:
            print(f"取消执行。你可以手动运行上面的命令。")
    
    print(f"\n=== 脚本完成 ===")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 