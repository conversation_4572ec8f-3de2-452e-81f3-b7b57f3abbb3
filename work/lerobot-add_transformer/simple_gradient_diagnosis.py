#!/usr/bin/env python3
"""
简化的DC token梯度问题诊断脚本
"""

import torch
import torch.nn.functional as F
import numpy as np
import logging
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent))

def analyze_contrastive_loss_gradients():
    """分析对比损失的梯度特性"""
    print("🔍 分析对比损失梯度特性")
    
    # 模拟误差矩阵
    batch_size = 4
    error_matrix = torch.randn(batch_size, batch_size, requires_grad=True)
    
    # 对比损失参数
    temp = 0.07
    scale = 4.0
    
    # 计算对比损失
    logits = scale * torch.exp(-error_matrix / temp)
    targets = torch.arange(batch_size)
    loss = F.cross_entropy(logits, targets)
    
    print(f"误差矩阵均值: {error_matrix.detach().mean().item():.4f}")
    print(f"损失值: {loss.item():.6f}")
    
    # 计算梯度
    loss.backward()
    grad_norm = torch.norm(error_matrix.grad).item()
    
    print(f"误差矩阵梯度范数: {grad_norm:.2e}")
    
    return grad_norm

def analyze_temperature_effects():
    """分析温度参数对梯度的影响"""
    print("\n🌡️ 分析温度参数对梯度的影响")
    
    batch_size = 4
    temperatures = [0.01, 0.05, 0.07, 0.1, 0.2, 0.5, 1.0]
    scale = 4.0
    
    for temp in temperatures:
        # 创建误差矩阵
        error_matrix = torch.randn(batch_size, batch_size, requires_grad=True)
        
        # 计算对比损失
        logits = scale * torch.exp(-error_matrix / temp)
        targets = torch.arange(batch_size)
        loss = F.cross_entropy(logits, targets)
        
        # 计算梯度
        loss.backward()
        grad_norm = torch.norm(error_matrix.grad).item()
        
        print(f"温度 {temp:.3f}: 损失={loss.item():.4f}, 梯度范数={grad_norm:.2e}")

def analyze_scale_effects():
    """分析scale参数对梯度的影响"""
    print("\n📏 分析scale参数对梯度的影响")
    
    batch_size = 4
    temp = 0.07
    scales = [0.1, 0.5, 1.0, 2.0, 4.0, 10.0, 20.0]
    
    for scale in scales:
        # 创建误差矩阵
        error_matrix = torch.randn(batch_size, batch_size, requires_grad=True)
        
        # 计算对比损失
        logits = scale * torch.exp(-error_matrix / temp)
        targets = torch.arange(batch_size)
        loss = F.cross_entropy(logits, targets)
        
        # 计算梯度
        loss.backward()
        grad_norm = torch.norm(error_matrix.grad).item()
        
        print(f"Scale {scale:.1f}: 损失={loss.item():.4f}, 梯度范数={grad_norm:.2e}")

def simulate_deep_network_gradients():
    """模拟深层网络的梯度传播"""
    print("\n🎯 模拟深层网络梯度传播")
    
    # 模拟DC tokens
    dc_tokens = torch.randn(4, 128, requires_grad=True)  # 4个DC tokens，每个128维
    
    # 模拟通过多层网络的传播
    x = dc_tokens
    for i in range(6):  # 6层网络
        # 简单的线性变换 + 激活
        weight = torch.randn(128, 128) * 0.1  # 小权重模拟深层网络
        x = torch.matmul(x, weight)
        x = torch.tanh(x)  # 可能导致梯度消失的激活函数
    
    # 最终损失
    loss = torch.sum(x ** 2)
    
    print(f"最终损失: {loss.item():.6f}")
    
    # 反向传播
    loss.backward()
    
    dc_grad_norm = torch.norm(dc_tokens.grad).item()
    print(f"DC tokens梯度范数: {dc_grad_norm:.2e}")
    
    return dc_grad_norm

def test_learning_rate_scaling():
    """测试学习率缩放效果"""
    print("\n📈 测试学习率缩放效果")
    
    # 模拟小梯度情况
    small_grad = 1e-10
    param = torch.tensor(1.0, requires_grad=True)
    
    learning_rates = [1e-4, 1e-3, 1e-2, 1e-1, 1.0, 10.0]
    
    for lr in learning_rates:
        # 模拟梯度更新
        param_copy = param.clone().detach().requires_grad_(True)
        
        # 手动设置梯度
        param_copy.grad = torch.tensor(small_grad)
        
        # 模拟优化器步骤
        with torch.no_grad():
            param_copy -= lr * param_copy.grad
        
        update_magnitude = abs(param_copy.item() - param.item())
        print(f"学习率 {lr:.0e}: 参数更新幅度={update_magnitude:.2e}")

def analyze_gradient_flow_issues():
    """分析梯度流问题"""
    print("\n🔍 分析梯度流问题")
    
    print("问题1: 对比损失的间接性")
    print("- DC tokens不直接影响单个样本损失")
    print("- 而是通过改变样本间相对误差影响对比损失")
    print("- 这种间接影响导致梯度信号很弱")
    
    print("\n问题2: 深层网络的梯度消失")
    print("- DC tokens需要通过多层Transformer传播")
    print("- 每层都可能导致梯度衰减")
    print("- 激活函数(如tanh)在饱和区梯度接近0")
    
    print("\n问题3: 温度参数的影响")
    print("- 小温度值(0.07)导致exp(-x/temp)变化剧烈")
    print("- 在误差较大时，梯度可能接近0")
    print("- 需要更大的温度值来获得稳定梯度")

def main():
    """主函数"""
    print("🚀 DC token梯度问题诊断")
    
    try:
        analyze_contrastive_loss_gradients()
        analyze_temperature_effects()
        analyze_scale_effects()
        simulate_deep_network_gradients()
        test_learning_rate_scaling()
        analyze_gradient_flow_issues()
        
        print("\n" + "="*60)
        print("📊 诊断总结:")
        print("✅ 对比损失梯度分析 - 完成")
        print("✅ 温度参数效应分析 - 完成")
        print("✅ Scale参数效应分析 - 完成")
        print("✅ 深层网络梯度模拟 - 完成")
        print("✅ 学习率缩放测试 - 完成")
        
        print("\n💡 关键发现和建议:")
        print("1. 🌡️ 温度参数过小(0.07)导致梯度不稳定")
        print("   建议: 增加到0.5或更高")
        print("2. 📏 Scale参数影响梯度幅度")
        print("   建议: 增加到10或更高")
        print("3. 📈 梯度范数过小需要大幅增加学习率")
        print("   建议: 增加1000倍或更多")
        print("4. 🎯 添加直接的DC正则化损失")
        print("   建议: L2正则化提供直接梯度信号")
        print("5. 🚫 移除梯度裁剪")
        print("   建议: 保留所有梯度信号")
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
