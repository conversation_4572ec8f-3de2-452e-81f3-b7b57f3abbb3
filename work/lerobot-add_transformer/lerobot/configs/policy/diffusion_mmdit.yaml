# @package _global_

# Configuration for Diffusion Policy with MMDiT (Multimodal Diffusion Transformer)
# This enables SoftREPA-style soft prompt learning for robot action diffusion

defaults:
  - diffusion

# Override the policy name
_target_: lerobot.common.policies.diffusion.DiffusionPolicy

# Policy configuration
policy:
  # Policy type
  type: diffusion

  # Input/output structure
  n_obs_steps: 2
  horizon: 16
  n_action_steps: 8

  # Use transformer architecture
  use_transformer: true

  # Enable MMDiT architecture
  use_mmdit: true
  use_mmdit_dc: true  # Enable SoftREPA-style DC tokens

  # MMDiT specific parameters (scaled up for 200M parameters)
  n_dc_tokens: 16     # Increased from 4 to 16
  n_dc_layers: 18     # Increased from 6 to 18 (75% of layers)
  use_dc_t: true      # Use time-dependent DC tokens

  # Transformer architecture (200M parameters configuration)
  diffusion_step_embed_dim: 1024  # Increased from 256 to 1024
  n_layer: 24                     # Increased from 8 to 24
  n_head: 16                      # Increased from 8 to 16
  p_drop_emb: 0.1
  p_drop_attn: 0.1

  # Diffusion parameters
  noise_scheduler_type: "DDIM"
  num_train_timesteps: 100
  num_inference_steps: 10
  beta_schedule: "squaredcos_cap_v2"
  beta_start: 0.0001
  beta_end: 0.02
  prediction_type: "epsilon"
  clip_sample: true
  clip_sample_range: 1.0

  # Vision encoder
  vision_backbone: "resnet18"
  pretrained_backbone_weights: "ResNet18_Weights.IMAGENET1K_V1"
  use_group_norm: true
  spatial_softmax_num_keypoints: 32
  crop_shape: [84, 84]

  # Training
  optimizer_lr: 1e-4
  optimizer_betas: [0.95, 0.999]
  optimizer_weight_decay: 1e-6
  scheduler_name: "cosine"
  scheduler_warmup_steps: 500

# Training configuration
training:
  offline_steps: 80000
  online_steps: 0
  eval_freq: 10000
  save_freq: 10000
  log_freq: 250
  save_checkpoint: true

# Environment
env:
  fps: 10
  episode_length: 400

# Dataset
dataset_repo_id: "lerobot/pusht"
