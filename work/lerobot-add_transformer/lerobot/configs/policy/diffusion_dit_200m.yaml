# @package _global_

# Configuration for Large-Scale Complete DiT (200M parameters)
# Based on original dit-policy implementation with progressive encoders
# Optimized for high-capacity robotic policy learning

defaults:
  - diffusion

# Override the policy name
_target_: lerobot.common.policies.diffusion.DiffusionPolicy

# Policy configuration
policy:
  # Policy type
  type: diffusion

  # Input/output structure
  n_obs_steps: 2
  horizon: 16
  n_action_steps: 8

  # Use transformer architecture
  use_transformer: true

  # Enable complete DiT architecture (original dit-policy implementation)
  use_dit: true
  use_mmdit: false  # Disable MMDiT when using DiT
  use_mmdit_dc: false

  # Large-scale DiT configuration (progressive encoders + AdaLN decoders)
  diffusion_step_embed_dim: 768   # Balanced hidden dimension
  n_layer: 16                     # Deep progressive encoding/decoding
  n_head: 12                      # Multi-head attention
  p_drop_emb: 0.1                # Embedding dropout
  p_drop_attn: 0.1               # Attention dropout
  
  # Diffusion parameters
  noise_scheduler_type: "DDIM"
  num_train_timesteps: 100
  num_inference_steps: 10
  beta_schedule: "squaredcos_cap_v2"
  beta_start: 0.0001
  beta_end: 0.02
  prediction_type: "epsilon"
  clip_sample: true
  clip_sample_range: 1.0

  # Vision encoder (larger for better features)
  vision_backbone: "resnet50"
  pretrained_backbone_weights: "ResNet50_Weights.IMAGENET1K_V2"
  use_group_norm: true
  spatial_softmax_num_keypoints: 64
  crop_shape: [96, 96]

  # Training (adjusted for large model)
  optimizer_lr: 3e-5  # Lower learning rate for stability
  optimizer_betas: [0.9, 0.95]
  optimizer_weight_decay: 1e-4
  scheduler_name: "cosine"
  scheduler_warmup_steps: 2000

# Training configuration
training:
  offline_steps: 200000
  online_steps: 0
  eval_freq: 5000
  save_freq: 10000
  log_freq: 500
  save_checkpoint: true

# Environment
env:
  fps: 10
  episode_length: 400

# Dataset
dataset_repo_id: "lerobot/pusht"
