# @package _global_

# Configuration for Standalone DiT Policy
# Complete dit-policy implementation with integrated observation processing
# Paper: "The Ingredients for Robotic Diffusion Transformers"

defaults:
  - override /env: pusht
  - _self_

seed: 1000
dataset_repo_id: lerobot/pusht

override_dataset_stats:
  observation.environment_state:
    mean: [0.0, 0.0]
    std: [1.0, 1.0]
  observation.state:
    mean: [0.0, 0.0]
    std: [1.0, 1.0]
  action:
    mean: [0.0, 0.0]
    std: [1.0, 1.0]

# Policy configuration
policy:
  _target_: lerobot.common.policies.dit_policy_standalone.DiTPolicy
  
  # Input/output structure
  n_obs_steps: 2
  horizon: 16
  n_action_steps: 8
  
  # DiT architecture (progressive encoders + AdaLN decoders)
  time_dim: 256
  hidden_dim: 512
  num_blocks: 6
  dropout: 0.1
  dim_feedforward: 2048
  nhead: 8
  activation: "gelu"
  
  # Diffusion parameters
  num_train_timesteps: 100
  num_inference_steps: 10
  beta_schedule: "squaredcos_cap_v2"
  beta_start: 0.0001
  beta_end: 0.02
  prediction_type: "epsilon"
  clip_sample: true
  clip_sample_range: 1.0
  
  # Vision processing
  vision_backbone: "resnet18"
  crop_shape: [84, 84]
  
  # Training
  lr: 1e-4
  weight_decay: 1e-6

# Training configuration
training:
  offline_steps: 80000
  online_steps: 0
  eval_freq: 10000
  save_freq: 10000
  log_freq: 250
  save_checkpoint: true

# Optimizer
optimizer:
  _target_: torch.optim.AdamW
  lr: ${policy.lr}
  weight_decay: ${policy.weight_decay}
  betas: [0.9, 0.95]

# Scheduler
lr_scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  T_max: ${training.offline_steps}
  eta_min: 1e-6

# Environment
env:
  fps: 10
  episode_length: 400

# Dataset
dataset_repo_id: "lerobot/pusht"
video_backend: "pyav"

# Evaluation
eval:
  n_episodes: 50
  batch_size: 50

# Logging
wandb:
  enable: false
  project: "lerobot_dit"
  entity: null
  notes: "Standalone DiT Policy Training"

# Device
device: cuda
