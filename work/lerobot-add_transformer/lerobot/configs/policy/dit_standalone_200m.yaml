# @package _global_

# Configuration for Large-Scale Standalone DiT Policy (200M parameters)
# Complete dit-policy implementation with integrated observation processing
# Optimized for high-capacity robotic policy learning

defaults:
  - override /env: pusht
  - _self_

seed: 1000
dataset_repo_id: lerobot/pusht

override_dataset_stats:
  observation.environment_state:
    mean: [0.0, 0.0]
    std: [1.0, 1.0]
  observation.state:
    mean: [0.0, 0.0]
    std: [1.0, 1.0]
  action:
    mean: [0.0, 0.0]
    std: [1.0, 1.0]

# Policy configuration
policy:
  _target_: lerobot.common.policies.dit_policy_standalone.DiTPolicy
  
  # Input/output structure
  n_obs_steps: 2
  horizon: 16
  n_action_steps: 8
  
  # Large-scale DiT architecture (progressive encoders + AdaLN decoders)
  time_dim: 512
  hidden_dim: 768
  num_blocks: 16
  dropout: 0.1
  dim_feedforward: 3072  # 4 * hidden_dim
  nhead: 12
  activation: "gelu"
  
  # Diffusion parameters
  num_train_timesteps: 100
  num_inference_steps: 10
  beta_schedule: "squaredcos_cap_v2"
  beta_start: 0.0001
  beta_end: 0.02
  prediction_type: "epsilon"
  clip_sample: true
  clip_sample_range: 1.0
  
  # Vision processing (larger for better features)
  vision_backbone: "resnet50"
  crop_shape: [96, 96]
  
  # Training (adjusted for large model)
  lr: 3e-5  # Lower learning rate for stability
  weight_decay: 1e-4

# Training configuration
training:
  offline_steps: 200000
  online_steps: 0
  eval_freq: 5000
  save_freq: 10000
  log_freq: 500
  save_checkpoint: true

# Optimizer (adjusted for large model)
optimizer:
  _target_: torch.optim.AdamW
  lr: ${policy.lr}
  weight_decay: ${policy.weight_decay}
  betas: [0.9, 0.95]
  eps: 1e-8

# Scheduler with warmup
lr_scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
  T_0: 10000
  T_mult: 2
  eta_min: 1e-6

# Environment
env:
  fps: 10
  episode_length: 400

# Dataset
dataset_repo_id: "lerobot/pusht"
video_backend: "pyav"

# Evaluation
eval:
  n_episodes: 50
  batch_size: 32  # Smaller batch for large model

# Logging
wandb:
  enable: false
  project: "lerobot_dit_200m"
  entity: null
  notes: "Large-Scale Standalone DiT Policy Training (200M parameters)"

# Device
device: cuda

# Mixed precision training for large model
use_amp: true
gradient_clip_norm: 1.0
