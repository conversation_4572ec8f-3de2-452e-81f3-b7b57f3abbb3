# @package _global_

# Configuration for Large-Scale MMDiT (200M parameters)
# This configuration targets ~200M parameters for better performance

defaults:
  - diffusion

# Override the policy name
_target_: lerobot.common.policies.diffusion.DiffusionPolicy

# Policy configuration
policy:
  # Policy type
  type: diffusion
  
  # Input/output structure
  n_obs_steps: 2
  horizon: 16
  n_action_steps: 8

  # Use transformer architecture
  use_transformer: true
  
  # Enable MMDiT architecture
  use_mmdit: true
  use_mmdit_dc: true  # Enable SoftREPA-style DC tokens
  
  # MMDiT specific parameters (optimized for ~193M parameters)
  n_dc_tokens: 10     # Balanced number of DC tokens
  n_dc_layers: 14     # Apply DC tokens to most layers (78% of layers)
  use_dc_t: true      # Use time-dependent DC tokens

  # Transformer architecture (193M parameters configuration)
  diffusion_step_embed_dim: 640   # Balanced hidden dimension
  n_layer: 18                     # Deep enough network
  n_head: 10                      # Attention heads (64 dim per head)
  p_drop_emb: 0.1
  p_drop_attn: 0.1
  
  # Diffusion parameters
  noise_scheduler_type: "DDIM"
  num_train_timesteps: 100
  num_inference_steps: 10
  beta_schedule: "squaredcos_cap_v2"
  beta_start: 0.0001
  beta_end: 0.02
  prediction_type: "epsilon"
  clip_sample: true
  clip_sample_range: 1.0

  # Vision encoder (ResNet101 for 193M total parameters)
  vision_backbone: "resnet101"  # Large vision encoder
  pretrained_backbone_weights: "ResNet101_Weights.IMAGENET1K_V2"
  use_group_norm: true
  spatial_softmax_num_keypoints: 64  # Increased from 32
  crop_shape: [96, 96]  # Slightly larger input

  # Training (adjusted for larger model)
  optimizer_lr: 5e-5  # Lower learning rate for stability
  optimizer_betas: [0.9, 0.95]  # More conservative betas
  optimizer_weight_decay: 1e-5  # Stronger regularization
  scheduler_name: "cosine"
  scheduler_warmup_steps: 2000  # Longer warmup

# Training configuration (adjusted for large model)
training:
  offline_steps: 200000  # More training steps
  online_steps: 0
  eval_freq: 5000       # Less frequent eval to save time
  save_freq: 10000
  log_freq: 500         # More frequent logging
  save_checkpoint: true

# Environment
env:
  fps: 10
  episode_length: 400

# Dataset
dataset_repo_id: "lerobot/pusht"
