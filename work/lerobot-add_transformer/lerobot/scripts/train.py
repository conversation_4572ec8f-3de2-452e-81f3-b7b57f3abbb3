#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import logging
import time
from contextlib import nullcontext
from pprint import pformat
from typing import Any
from pathlib import Path

import torch
from termcolor import colored
from torch.amp import GradScaler
from torch.optim import Optimizer
from tqdm import tqdm, trange

from lerobot.common.datasets.factory import make_dataset
from lerobot.common.datasets.sampler import EpisodeAwareSampler
from lerobot.common.datasets.utils import cycle
from lerobot.common.envs.factory import make_env
from lerobot.common.optim.factory import make_optimizer_and_scheduler
from lerobot.common.policies.factory import make_policy
from lerobot.common.policies.pretrained import PreTrainedPolicy
from lerobot.common.policies.utils import get_device_from_parameters
from lerobot.common.utils.logging_utils import AverageMeter, MetricsTracker
from lerobot.common.utils.random_utils import set_seed
from lerobot.common.utils.train_utils import (
    get_step_checkpoint_dir,
    get_step_identifier,
    load_training_state,
    save_checkpoint,
    update_last_checkpoint,
)
from lerobot.common.utils.utils import (
    format_big_number,
    get_safe_torch_device,
    has_method,
    init_logging,
)
# from lerobot.common.utils.tqdm_utils import TrainingProgressTracker
from lerobot.common.utils.wandb_utils import WandBLogger
from lerobot.configs import parser
from lerobot.configs.train import TrainPipelineConfig
from lerobot.scripts.eval import eval_policy


def update_policy(
    train_metrics: MetricsTracker,
    policy: PreTrainedPolicy,
    batch: Any,
    optimizer: Optimizer,
    grad_clip_norm: float,
    grad_scaler: GradScaler,
    lr_scheduler=None,
    use_amp: bool = False,
    lock=None,
) -> tuple[MetricsTracker, dict]:
    start_time = time.perf_counter()
    device = get_device_from_parameters(policy)
    policy.train()
    with torch.autocast(device_type=device.type) if use_amp else nullcontext():
        loss, output_dict = policy.forward(batch)
        # TODO(rcadene): policy.unnormalize_outputs(out_dict)
    grad_scaler.scale(loss).backward()

    # Unscale the gradient of the optimizer's assigned params in-place **prior to gradient clipping**.
    grad_scaler.unscale_(optimizer)

    grad_norm = torch.nn.utils.clip_grad_norm_(
        policy.parameters(),
        grad_clip_norm,
        error_if_nonfinite=False,
    )

    # Optimizer's gradients are already unscaled, so scaler.step does not unscale them,
    # although it still skips optimizer.step() if the gradients contain infs or NaNs.
    with lock if lock is not None else nullcontext():
        grad_scaler.step(optimizer)
    # Updates the scale for next iteration.
    grad_scaler.update()

    optimizer.zero_grad()

    # Step through pytorch scheduler at every batch instead of epoch
    if lr_scheduler is not None:
        lr_scheduler.step()

    if has_method(policy, "update"):
        # To possibly update an internal buffer (for instance an Exponential Moving Average like in TDMPC).
        policy.update()

    train_metrics.loss = loss.item()
    train_metrics.grad_norm = grad_norm.item()
    train_metrics.lr = optimizer.param_groups[0]["lr"]
    train_metrics.update_s = time.perf_counter() - start_time
    return train_metrics, output_dict


@parser.wrap()
def train(cfg: TrainPipelineConfig):
    cfg.validate()
    logging.info(pformat(cfg.to_dict()))

    if cfg.wandb.enable and cfg.wandb.project:
        wandb_logger = WandBLogger(cfg)
    else:
        wandb_logger = None
        logging.info(colored("Logs will be saved locally.", "yellow", attrs=["bold"]))

    if cfg.seed is not None:
        set_seed(cfg.seed)

    # Check device is available
    device = get_safe_torch_device(cfg.policy.device, log=True)
    torch.backends.cudnn.benchmark = True
    torch.backends.cuda.matmul.allow_tf32 = True

    logging.info("Creating dataset")
    with tqdm(desc="Loading dataset", unit="step", leave=False) as dataset_pbar:
        dataset_pbar.set_description("Loading dataset metadata")
        dataset = make_dataset(cfg)
        dataset_pbar.set_description("Dataset loaded successfully")
        dataset_pbar.update(1)
        dataset_pbar.close()

    # Create environment used for evaluating checkpoints during training on simulation data.
    # On real-world data, no need to create an environment as evaluations are done outside train.py,
    # using the eval.py instead, with gym_dora environment and dora-rs.
    eval_env = None
    if cfg.eval_freq > 0 and cfg.env is not None:
        logging.info("Creating env")
        eval_env = make_env(cfg.env, n_envs=cfg.eval.batch_size)

    logging.info("Creating policy")
    with tqdm(desc="Initializing policy", unit="step", leave=False) as policy_pbar:
        policy_pbar.set_description("Creating policy architecture")
        policy = make_policy(
            cfg=cfg.policy,
            ds_meta=dataset.meta,
        )
        policy_pbar.set_description("Policy created successfully")
        policy_pbar.update(1)
        policy_pbar.close()

    logging.info("Creating optimizer and scheduler")
    optimizer, lr_scheduler = make_optimizer_and_scheduler(cfg, policy)
    grad_scaler = GradScaler(device.type, enabled=cfg.policy.use_amp)

    if cfg.resume:
        # Reconstruct the path to the checkpoint root directory based on the output directory.
        # This is a robust way to find the 'training_state' directory when resuming.
        checkpoint_root_dir = Path(cfg.output_dir) / "checkpoints" / "last"
        step, optimizer, lr_scheduler = load_training_state(checkpoint_root_dir, optimizer, lr_scheduler)
    else:
        step = 0

    num_learnable_params = sum(p.numel() for p in policy.parameters() if p.requires_grad)
    num_total_params = sum(p.numel() for p in policy.parameters())

    logging.info(colored("Output dir:", "yellow", attrs=["bold"]) + f" {cfg.output_dir}")
    if cfg.env is not None:
        logging.info(f"{cfg.env.task=}")
    logging.info(f"{cfg.steps=} ({format_big_number(cfg.steps)})")
    logging.info(f"{dataset.num_frames=} ({format_big_number(dataset.num_frames)})")
    logging.info(f"{dataset.num_episodes=}")
    logging.info(f"{num_learnable_params=} ({format_big_number(num_learnable_params)})")
    logging.info(f"{num_total_params=} ({format_big_number(num_total_params)})")

    # create dataloader for offline training
    if hasattr(cfg.policy, "drop_n_last_frames"):
        shuffle = False
        sampler = EpisodeAwareSampler(
            dataset.episode_data_index,
            drop_n_last_frames=cfg.policy.drop_n_last_frames,
            shuffle=True,
        )
    else:
        shuffle = True
        sampler = None

    dataloader = torch.utils.data.DataLoader(
        dataset,
        num_workers=cfg.num_workers,
        batch_size=cfg.batch_size,
        shuffle=shuffle,
        sampler=sampler,
        pin_memory=device.type != "cpu",
        drop_last=False,
    )
    dl_iter = cycle(dataloader)

    policy.train()

    train_metrics = {
        "loss": AverageMeter("loss", ":.3f"),
        "grad_norm": AverageMeter("grdn", ":.3f"),
        "lr": AverageMeter("lr", ":0.1e"),
        "update_s": AverageMeter("updt_s", ":.3f"),
        "dataloading_s": AverageMeter("data_s", ":.3f"),
    }

    train_tracker = MetricsTracker(
        cfg.batch_size, dataset.num_frames, dataset.num_episodes, train_metrics, initial_step=step
    )

    logging.info("Start offline training on a fixed dataset")

    # Create enhanced progress tracker
    # progress_tracker = TrainingProgressTracker(
    #     total_steps=cfg.steps,
    #     initial_step=step,
    #     log_freq=cfg.log_freq,
    #     smoothing_window=50
    # )

    # Create progress bar for training
    progress_bar = tqdm(
        range(step, cfg.steps),
        desc="Training",
        initial=step,
        total=cfg.steps,
        unit="step",
        dynamic_ncols=True,
        leave=True,
        bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]'
    )

    for current_step in progress_bar:
        start_time = time.perf_counter()
        batch = next(dl_iter)
        train_tracker.dataloading_s = time.perf_counter() - start_time

        for key in batch:
            if isinstance(batch[key], torch.Tensor):
                batch[key] = batch[key].to(device, non_blocking=True)

        train_tracker, output_dict = update_policy(
            train_tracker,
            policy,
            batch,
            optimizer,
            cfg.optimizer.grad_clip_norm,
            grad_scaler=grad_scaler,
            lr_scheduler=lr_scheduler,
            use_amp=cfg.policy.use_amp,
        )

        # Note: eval and checkpoint happens *after* the `step`th training update has completed, so we
        # increment `step` here.
        step += 1
        train_tracker.step()

        # Update progress tracker with current metrics
        current_metrics = {
            'loss': train_tracker.loss.val if hasattr(train_tracker.loss, 'val') else train_tracker.loss.avg,
            'lr': train_tracker.lr.val if hasattr(train_tracker.lr, 'val') else train_tracker.lr.avg,
            'grad_norm': train_tracker.grad_norm.val if hasattr(train_tracker.grad_norm, 'val') else train_tracker.grad_norm.avg,
        }
        # progress_tracker.step(current_metrics)

        is_log_step = cfg.log_freq > 0 and step % cfg.log_freq == 0
        is_saving_step = step % cfg.save_freq == 0 or step == cfg.steps
        is_eval_step = cfg.eval_freq > 0 and step % cfg.eval_freq == 0

        # Update progress bar with enhanced metrics
        if train_tracker.loss.count > 0:
            # progress_info = progress_tracker.get_progress_dict()
            postfix_dict = {
                'loss': f'{train_tracker.loss.avg:.4f}',
                'lr': f'{train_tracker.lr.avg:.2e}',
                'grad_norm': f'{train_tracker.grad_norm.avg:.3f}',
                'data_s': f'{train_tracker.dataloading_s.avg:.3f}',
                'update_s': f'{train_tracker.update_s.avg:.3f}'
            }

            # Add ETA if available
            # if 'eta_formatted' in progress_info:
            #     postfix_dict['ETA'] = progress_info['eta_formatted']

            # Add steps per second
            # if 'steps_per_sec' in progress_info and progress_info['steps_per_sec'] > 0:
            #     postfix_dict['steps/s'] = f'{progress_info["steps_per_sec"]:.2f}'

            progress_bar.set_postfix(postfix_dict)

        if is_log_step:
            logging.info(train_tracker)
            if wandb_logger:
                wandb_log_dict = train_tracker.to_dict()
                if output_dict:
                    wandb_log_dict.update(output_dict)
                wandb_logger.log_dict(wandb_log_dict, step)
            train_tracker.reset_averages()

        if cfg.save_checkpoint and is_saving_step:
            progress_bar.set_description(f"💾 Saving checkpoint (step {step})")
            logging.info(f"Checkpoint policy after step {step}")

            checkpoint_start_time = time.perf_counter()
            checkpoint_dir = get_step_checkpoint_dir(cfg.output_dir, cfg.steps, step)
            save_checkpoint(checkpoint_dir, step, cfg, policy, optimizer, lr_scheduler)
            update_last_checkpoint(checkpoint_dir)
            checkpoint_time = time.perf_counter() - checkpoint_start_time

            if wandb_logger:
                wandb_logger.log_policy(checkpoint_dir)

            # Update progress bar with checkpoint timing
            current_postfix = progress_bar.postfix if hasattr(progress_bar, 'postfix') else {}
            if isinstance(current_postfix, str):
                current_postfix = {}
            current_postfix['save_time'] = f'{checkpoint_time:.1f}s'
            progress_bar.set_postfix(current_postfix)
            progress_bar.set_description("🔄 Training")

        if cfg.env and is_eval_step:
            step_id = get_step_identifier(step, cfg.steps)
            progress_bar.set_description(f"🔍 Evaluating (step {step})")
            logging.info(f"Eval policy at step {step}")

            # Create evaluation progress bar
            eval_start_time = time.perf_counter()
            with (
                torch.no_grad(),
                torch.autocast(device_type=device.type) if cfg.policy.use_amp else nullcontext(),
            ):
                eval_info = eval_policy(
                    eval_env,
                    policy,
                    cfg.eval.n_episodes,
                    videos_dir=cfg.output_dir / "eval" / f"videos_step_{step_id}",
                    max_episodes_rendered=4,
                    start_seed=cfg.seed,
                )
            eval_time = time.perf_counter() - eval_start_time

            eval_metrics = {
                "avg_sum_reward": AverageMeter("∑rwrd", ":.3f"),
                "pc_success": AverageMeter("success", ":.1f"),
                "eval_s": AverageMeter("eval_s", ":.3f"),
            }
            eval_tracker = MetricsTracker(
                cfg.batch_size, dataset.num_frames, dataset.num_episodes, eval_metrics, initial_step=step
            )
            eval_tracker.eval_s = eval_info["aggregated"].pop("eval_s")
            eval_tracker.avg_sum_reward = eval_info["aggregated"].pop("avg_sum_reward")
            eval_tracker.pc_success = eval_info["aggregated"].pop("pc_success")
            logging.info(eval_tracker)

            # Update progress bar with eval results and timing
            # progress_info = progress_tracker.get_progress_dict()
            eval_postfix = {
                'loss': f'{train_tracker.loss.avg:.4f}',
                'lr': f'{train_tracker.lr.avg:.2e}',
                'success': f'{eval_tracker.pc_success.avg:.1f}%',
                'reward': f'{eval_tracker.avg_sum_reward.avg:.3f}',
                'eval_time': f'{eval_time:.1f}s'
            }

            # Add ETA if available
            # if 'eta_formatted' in progress_info:
            #     eval_postfix['ETA'] = progress_info['eta_formatted']

            progress_bar.set_postfix(eval_postfix)

            if wandb_logger:
                wandb_log_dict = {**eval_tracker.to_dict(), **eval_info}
                wandb_logger.log_dict(wandb_log_dict, step, mode="eval")
                wandb_logger.log_video(eval_info["video_paths"][0], step, mode="eval")

            # Reset description with success rate
            success_rate = eval_tracker.pc_success.avg
            if success_rate >= 80:
                progress_bar.set_description("🚀 Training (High Success)")
            elif success_rate >= 50:
                progress_bar.set_description("📈 Training (Good Progress)")
            else:
                progress_bar.set_description("🔄 Training")

    # Close progress bar
    progress_bar.close()

    if eval_env:
        eval_env.close()
    logging.info("End of training")


if __name__ == "__main__":
    init_logging()
    train()
