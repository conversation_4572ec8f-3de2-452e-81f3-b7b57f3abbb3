import torch
import torch.nn as nn
import os

def load_dc_weights_from_checkpoint(model, checkpoint_path, freeze_dc=True, freeze_dc_t=True):
    """
    从完整的预训练模型checkpoint中加载dc和dc_t权重
    
    Args:
        model: 当前训练的模型实例
        checkpoint_path: 预训练模型checkpoint路径
        freeze_dc: 是否冻结dc_tokens参数
        freeze_dc_t: 是否冻结dc_t_tokens参数
    """
    
    print(f"Loading checkpoint from {checkpoint_path}")
    
    # 加载完整的checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 获取模型状态字典
    if 'model_state_dict' in checkpoint:
        pretrained_state_dict = checkpoint['model_state_dict']
    else:
        # 如果checkpoint直接就是state_dict
        pretrained_state_dict = checkpoint
    
    # 提取dc_tokens权重（需要安全检查）
    dc_tokens_key = 'net.dc_tokens'
    if dc_tokens_key in pretrained_state_dict:
        # 检查模型是否有dc_tokens属性
        if hasattr(model.net, 'dc_tokens'):
            pretrained_dc_tokens = pretrained_state_dict[dc_tokens_key]
            current_dc_shape = model.net.dc_tokens.shape
            pretrained_dc_shape = pretrained_dc_tokens.shape

            if current_dc_shape == pretrained_dc_shape:
                model.net.dc_tokens.data.copy_(pretrained_dc_tokens)
                print(f"✓ Successfully loaded dc_tokens with shape {pretrained_dc_shape}")
            else:
                print(f"⚠ dc_tokens shape mismatch: current {current_dc_shape}, pretrained {pretrained_dc_shape}")
                # 处理形状不匹配
                _handle_shape_mismatch_3d(model.net.dc_tokens, pretrained_dc_tokens, 'dc_tokens')

            # 是否冻结dc_tokens
            if freeze_dc:
                model.net.dc_tokens.requires_grad = False
                print("🔒 Frozen dc_tokens parameters")
        else:
            print(f"⚠ Warning: Model does not have dc_tokens attribute, skipping dc_tokens loading")
    else:
        print(f"⚠ Warning: {dc_tokens_key} not found in checkpoint")
    
    # 提取dc_t_tokens权重
    dc_t_tokens_key = 'net.dc_t_tokens.weight'
    if dc_t_tokens_key in pretrained_state_dict:
        pretrained_dc_t_weight = pretrained_state_dict[dc_t_tokens_key]
        current_dc_t_shape = model.net.dc_t_tokens.weight.shape
        pretrained_dc_t_shape = pretrained_dc_t_weight.shape
        
        if current_dc_t_shape == pretrained_dc_t_shape:
            model.net.dc_t_tokens.weight.data.copy_(pretrained_dc_t_weight)
            print(f"✓ Successfully loaded dc_t_tokens.weight with shape {pretrained_dc_t_shape}")
        else:
            print(f"⚠ dc_t_tokens.weight shape mismatch: current {current_dc_t_shape}, pretrained {pretrained_dc_t_shape}")
            # 处理形状不匹配
            _handle_shape_mismatch_2d(model.net.dc_t_tokens.weight, pretrained_dc_t_weight, 'dc_t_tokens.weight')
        
        # 是否冻结dc_t_tokens
        if freeze_dc_t:
            model.net.dc_t_tokens.weight.requires_grad = False
            print("🔒 Frozen dc_t_tokens parameters")
    else:
        print(f"⚠ Warning: {dc_t_tokens_key} not found in checkpoint")

def _handle_shape_mismatch_3d(current_param, pretrained_tensor, param_name):
    """处理3D张量的形状不匹配"""
    current_shape = current_param.shape
    pretrained_shape = pretrained_tensor.shape
    
    # 计算可以复制的最小维度
    min_dim0 = min(current_shape[0], pretrained_shape[0])
    min_dim1 = min(current_shape[1], pretrained_shape[1])
    min_dim2 = min(current_shape[2], pretrained_shape[2])
    
    # 保留当前参数的初始值，只覆盖匹配的部分
    current_param.data[:min_dim0, :min_dim1, :min_dim2] = \
        pretrained_tensor[:min_dim0, :min_dim1, :min_dim2]
    
    print(f"✓ Partially loaded {param_name}: copied [{min_dim0}, {min_dim1}, {min_dim2}] portion")

def _handle_shape_mismatch_2d(current_param, pretrained_tensor, param_name):
    """处理2D张量的形状不匹配"""
    current_shape = current_param.shape
    pretrained_shape = pretrained_tensor.shape
    
    # 计算可以复制的最小维度
    min_rows = min(current_shape[0], pretrained_shape[0])
    min_cols = min(current_shape[1], pretrained_shape[1])
    
    # 保留当前参数的初始值，只覆盖匹配的部分
    current_param.data[:min_rows, :min_cols] = pretrained_tensor[:min_rows, :min_cols]
    
    print(f"✓ Partially loaded {param_name}: copied [{min_rows}, {min_cols}] portion")

def extract_dc_weights_from_checkpoint(checkpoint_path, save_dir=None):
    """
    从完整checkpoint中提取dc权重并单独保存（可选）
    
    Args:
        checkpoint_path: 预训练模型checkpoint路径
        save_dir: 保存提取权重的目录（如果为None则不保存）
    
    Returns:
        dict: 包含dc_tokens和dc_t_tokens权重的字典
    """
    
    print(f"Extracting dc weights from {checkpoint_path}")
    
    # 加载完整的checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 获取模型状态字典
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    # 提取dc相关权重
    dc_weights = {}
    
    # 提取dc_tokens
    if 'net.dc_tokens' in state_dict:
        dc_weights['dc_tokens'] = state_dict['net.dc_tokens']
        print(f"✓ Extracted dc_tokens with shape {dc_weights['dc_tokens'].shape}")
    
    # 提取dc_t_tokens.weight
    if 'net.dc_t_tokens.weight' in state_dict:
        dc_weights['dc_t_tokens_weight'] = state_dict['net.dc_t_tokens.weight']
        print(f"✓ Extracted dc_t_tokens.weight with shape {dc_weights['dc_t_tokens_weight'].shape}")
    
    # 可选：保存提取的权重
    if save_dir is not None:
        os.makedirs(save_dir, exist_ok=True)
        
        if 'dc_tokens' in dc_weights:
            dc_path = os.path.join(save_dir, 'dc_tokens.pth')
            torch.save(dc_weights['dc_tokens'], dc_path)
            print(f"💾 Saved dc_tokens to {dc_path}")
        
        if 'dc_t_tokens_weight' in dc_weights:
            dc_t_path = os.path.join(save_dir, 'dc_t_tokens_weight.pth')
            torch.save(dc_weights['dc_t_tokens_weight'], dc_t_path)
            print(f"💾 Saved dc_t_tokens_weight to {dc_t_path}")
    
    return dc_weights

def load_extracted_dc_weights(model, dc_tokens_path=None, dc_t_tokens_path=None, 
                             freeze_dc=False, freeze_dc_t=False):
    """
    加载单独保存的dc权重文件
    
    Args:
        model: 当前训练的模型实例
        dc_tokens_path: dc_tokens权重文件路径
        dc_t_tokens_path: dc_t_tokens权重文件路径
        freeze_dc: 是否冻结dc_tokens参数
        freeze_dc_t: 是否冻结dc_t_tokens参数
    """
    
    # 加载dc_tokens
    if dc_tokens_path is not None:
        dc_tokens = torch.load(dc_tokens_path, map_location='cpu')
        current_shape = model.net.dc_tokens.shape
        
        if current_shape == dc_tokens.shape:
            model.net.dc_tokens.data.copy_(dc_tokens)
            print(f"✓ Loaded dc_tokens from {dc_tokens_path}")
        else:
            print(f"⚠ Shape mismatch for dc_tokens")
            _handle_shape_mismatch_3d(model.net.dc_tokens, dc_tokens, 'dc_tokens')
        
        if freeze_dc:
            model.net.dc_tokens.requires_grad = False
            print("🔒 Frozen dc_tokens parameters")
    
    # 加载dc_t_tokens.weight
    if dc_t_tokens_path is not None:
        dc_t_weight = torch.load(dc_t_tokens_path, map_location='cpu')
        current_shape = model.net.dc_t_tokens.weight.shape
        
        if current_shape == dc_t_weight.shape:
            model.net.dc_t_tokens.weight.data.copy_(dc_t_weight)
            print(f"✓ Loaded dc_t_tokens.weight from {dc_t_tokens_path}")
        else:
            print(f"⚠ Shape mismatch for dc_t_tokens.weight")
            _handle_shape_mismatch_2d(model.net.dc_t_tokens.weight, dc_t_weight, 'dc_t_tokens.weight')
        
        if freeze_dc_t:
            model.net.dc_t_tokens.weight.requires_grad = False
            print("🔒 Frozen dc_t_tokens parameters")

# 使用示例
def example_usage():
    """使用示例"""
    
    # 方法1: 直接从完整checkpoint加载dc权重
    # model = YourModel()
    # load_dc_weights_from_checkpoint(
    #     model=model,
    #     checkpoint_path='path/to/pretrained_model.pth',
    #     freeze_dc=False,
    #     freeze_dc_t=False
    # )
    
    # 方法2: 先提取权重，然后加载
    # 提取权重（一次性操作）
    # extract_dc_weights_from_checkpoint(
    #     checkpoint_path='path/to/pretrained_model.pth',
    #     save_dir='extracted_weights'
    # )
    # 
    # 在训练时加载提取的权重
    # load_extracted_dc_weights(
    #     model=model,
    #     dc_tokens_path='extracted_weights/dc_tokens.pth',
    #     dc_t_tokens_path='extracted_weights/dc_t_tokens_weight.pth'
    # )
    
    pass

# 集成到训练代码的示例
def integrate_to_training():
    """如何集成到训练代码中"""
    
    # 在训练开始前加载
    def setup_model_with_pretrained_dc(model, pretrained_checkpoint_path):
        print("Setting up model with pretrained dc weights...")
        load_dc_weights_from_checkpoint(
            model=model,
            checkpoint_path=pretrained_checkpoint_path,
            freeze_dc=False,  # 根据需要调整
            freeze_dc_t=False
        )
        print("Model setup complete!")
        return model
    
    # 使用示例：
    # model = YourModel()
    # model = setup_model_with_pretrained_dc(model, 'pretrained_model.pth')
    # optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    # 
    # # 开始训练
    # for epoch in range(num_epochs):
    #     train_one_epoch(model, optimizer, dataloader)
    
    pass