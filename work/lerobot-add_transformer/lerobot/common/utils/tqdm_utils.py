#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Utilities for enhanced progress bars and monitoring during training."""

import time
from typing import Any, Iterator
from collections import deque

import torch
from tqdm import tqdm


class TqdmDataLoader:
    """
    A wrapper around DataLoader that provides tqdm progress bars for data loading.
    Tracks data loading speed and provides detailed statistics.
    """
    
    def __init__(self, dataloader, desc="Loading data", leave=False, smoothing_window=100):
        self.dataloader = dataloader
        self.desc = desc
        self.leave = leave
        self.smoothing_window = smoothing_window
        self.load_times = deque(maxlen=smoothing_window)
        self.batch_sizes = deque(maxlen=smoothing_window)
        
    def __iter__(self):
        """Iterate through the dataloader with progress tracking."""
        pbar = tqdm(
            desc=self.desc,
            unit="batch",
            leave=self.leave,
            dynamic_ncols=True,
            smoothing=0.1
        )
        
        total_samples = 0
        start_time = time.perf_counter()
        
        for batch_idx, batch in enumerate(self.dataloader):
            batch_start = time.perf_counter()
            
            # Calculate batch size
            if isinstance(batch, dict):
                # Assume the first tensor-like value represents batch size
                batch_size = next(
                    (v.shape[0] for v in batch.values() if hasattr(v, 'shape')), 
                    1
                )
            elif isinstance(batch, (list, tuple)):
                batch_size = len(batch[0]) if batch and hasattr(batch[0], '__len__') else 1
            else:
                batch_size = batch.shape[0] if hasattr(batch, 'shape') else 1
            
            batch_load_time = time.perf_counter() - batch_start
            self.load_times.append(batch_load_time)
            self.batch_sizes.append(batch_size)
            total_samples += batch_size
            
            # Calculate statistics
            avg_load_time = sum(self.load_times) / len(self.load_times)
            avg_batch_size = sum(self.batch_sizes) / len(self.batch_sizes)
            samples_per_sec = avg_batch_size / avg_load_time if avg_load_time > 0 else 0
            total_time = time.perf_counter() - start_time
            overall_samples_per_sec = total_samples / total_time if total_time > 0 else 0
            
            # Update progress bar
            pbar.set_postfix({
                'batch_size': f'{batch_size}',
                'load_time': f'{batch_load_time:.3f}s',
                'avg_load': f'{avg_load_time:.3f}s',
                'samples/s': f'{samples_per_sec:.1f}',
                'overall_s/s': f'{overall_samples_per_sec:.1f}'
            })
            pbar.update(1)
            
            yield batch
        
        pbar.close()


class TqdmCycleIterator:
    """
    A wrapper around cycle iterator that provides progress tracking.
    Useful for infinite data loading cycles during training.
    """
    
    def __init__(self, iterator, desc="Training", total_steps=None, initial_step=0):
        self.iterator = iterator
        self.desc = desc
        self.total_steps = total_steps
        self.initial_step = initial_step
        self.current_step = initial_step
        
    def __iter__(self):
        return self
    
    def __next__(self):
        return next(self.iterator)
    
    def get_progress_info(self):
        """Get current progress information."""
        if self.total_steps:
            progress = (self.current_step - self.initial_step) / (self.total_steps - self.initial_step)
            return {
                'step': self.current_step,
                'total': self.total_steps,
                'progress': progress,
                'remaining': self.total_steps - self.current_step
            }
        return {'step': self.current_step}
    
    def step(self):
        """Increment the step counter."""
        self.current_step += 1


class TrainingProgressTracker:
    """
    Enhanced progress tracker for training with detailed metrics and ETA estimation.
    """
    
    def __init__(self, total_steps, initial_step=0, log_freq=100, smoothing_window=50):
        self.total_steps = total_steps
        self.initial_step = initial_step
        self.current_step = initial_step
        self.log_freq = log_freq
        self.smoothing_window = smoothing_window
        
        # Timing tracking
        self.start_time = time.perf_counter()
        self.step_times = deque(maxlen=smoothing_window)
        self.last_step_time = self.start_time
        
        # Metrics tracking
        self.metrics_history = {}
        
    def step(self, metrics=None):
        """Update progress with current step metrics."""
        current_time = time.perf_counter()
        step_time = current_time - self.last_step_time
        self.step_times.append(step_time)
        self.last_step_time = current_time
        self.current_step += 1
        
        # Store metrics
        if metrics:
            for key, value in metrics.items():
                if key not in self.metrics_history:
                    self.metrics_history[key] = deque(maxlen=self.smoothing_window)
                self.metrics_history[key].append(value)
    
    def get_eta(self):
        """Calculate estimated time to completion."""
        if len(self.step_times) == 0:
            return None
        
        avg_step_time = sum(self.step_times) / len(self.step_times)
        remaining_steps = self.total_steps - self.current_step
        eta_seconds = avg_step_time * remaining_steps
        
        return eta_seconds
    
    def get_progress_dict(self):
        """Get comprehensive progress information."""
        current_time = time.perf_counter()
        elapsed_time = current_time - self.start_time
        progress = (self.current_step - self.initial_step) / (self.total_steps - self.initial_step)
        
        result = {
            'step': self.current_step,
            'total_steps': self.total_steps,
            'progress': progress,
            'elapsed_time': elapsed_time,
            'steps_per_sec': len(self.step_times) / sum(self.step_times) if self.step_times else 0,
        }
        
        # Add ETA if available
        eta = self.get_eta()
        if eta is not None:
            result['eta'] = eta
            result['eta_formatted'] = self.format_time(eta)
        
        # Add smoothed metrics
        for key, values in self.metrics_history.items():
            if values:
                result[f'avg_{key}'] = sum(values) / len(values)
        
        return result
    
    @staticmethod
    def format_time(seconds):
        """Format seconds into human-readable time string."""
        if seconds < 60:
            return f"{seconds:.0f}s"
        elif seconds < 3600:
            minutes = seconds // 60
            secs = seconds % 60
            return f"{minutes:.0f}m{secs:.0f}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours:.0f}h{minutes:.0f}m"
    
    def should_log(self):
        """Check if current step should trigger logging."""
        return self.current_step % self.log_freq == 0
    
    def format_progress_string(self):
        """Format a comprehensive progress string."""
        info = self.get_progress_dict()
        
        progress_str = f"Step {info['step']}/{info['total_steps']} ({info['progress']:.1%})"
        
        if 'eta_formatted' in info:
            progress_str += f" | ETA: {info['eta_formatted']}"
        
        if 'steps_per_sec' in info and info['steps_per_sec'] > 0:
            progress_str += f" | {info['steps_per_sec']:.2f} steps/s"
        
        return progress_str
