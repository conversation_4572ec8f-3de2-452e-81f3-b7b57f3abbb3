# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import abc
import json
import logging
import os
from pathlib import Path
from typing import Type, TypeVar

import packaging
import safetensors
from huggingface_hub import hf_hub_download
from huggingface_hub.constants import SAFETENSORS_SINGLE_FILE
from huggingface_hub.errors import HfHubHTTPError
from safetensors.torch import load_model as load_model_as_safetensor
from safetensors.torch import save_model as save_model_as_safetensor
import torch
from torch import Tensor, nn

from lerobot.common.utils.hub import HubMixin
from lerobot.configs.policies import PreTrainedConfig
from lerobot.common.policies.policy_protocol import Policy
from lerobot.common.utils.utils import get_device, get_safe_device
from lerobot.common.constants import (
    CHECKPOINTS_DIR,
    PRETRAINED_MODEL_DIR,
    SAFETENSORS_FILE_NAME,
    POLICY_CFG_FILE_NAME,
)
import pdb

T = TypeVar("T", bound="PreTrainedPolicy")

DEFAULT_POLICY_CARD = """
---
# For reference on model card metadata, see the spec: https://github.com/huggingface/hub-docs/blob/main/modelcard.md?plain=1
# Doc / guide: https://huggingface.co/docs/hub/model-cards
{{ card_data }}
---

This policy has been pushed to the Hub using [LeRobot](https://github.com/huggingface/lerobot):
- Docs: {{ docs_url | default("[More Information Needed]", true) }}
"""

logger = logging.getLogger(__name__)


class PreTrainedPolicy(nn.Module, HubMixin, abc.ABC):
    """
    Base class for policy models.
    """

    config_class: None
    name: None

    def __init__(self, config: PreTrainedConfig, *inputs, **kwargs):
        super().__init__()
        if not isinstance(config, PreTrainedConfig):
            raise ValueError(
                f"Parameter config in `{self.__class__.__name__}(config)` should be an instance of class "
                "`PreTrainedConfig`. To create a model from a pretrained model use "
                f"`model = {self.__class__.__name__}.from_pretrained(PRETRAINED_MODEL_NAME)`"
            )
        self.config = config

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if not getattr(cls, "config_class", None):
            raise TypeError(f"Class {cls.__name__} must define 'config_class'")
        if not getattr(cls, "name", None):
            raise TypeError(f"Class {cls.__name__} must define 'name'")

    def _save_pretrained(self, save_directory: Path) -> None:
        self.config._save_pretrained(save_directory)
        model_to_save = self.module if hasattr(self, "module") else self
        save_model_as_safetensor(model_to_save, str(save_directory / SAFETENSORS_SINGLE_FILE))

    @classmethod
    def from_pretrained(
        cls: Type[T],
        repo_id: str | None = None,
        config_path: Path | str | None = None,
        device: torch.device | str | None = None,
        strict: bool = False,
    ):
        """
        Instantiate a policy from a configuration file and weights from Hugging Face Hub or locally.
        The policy will be loaded in evaluation mode.
        """
        if repo_id is None and config_path is None:
            raise ValueError("repo_id and config_path cannot be None at the same time.")

        if repo_id is not None:
            # Download policy from Hugging Face Hub.
            # a/b/c is interpreted as repo_id=a/b and subdirectory=c
            parts = repo_id.split("/")
            if len(parts) > 2:
                repo_id = "/".join(parts[:2])
                subfolder = "/".join(parts[2:])
            else:
                subfolder = None
            storage_folder = hf_hub_download(
                repo_id=repo_id,
                subfolder=subfolder,
                repo_type="space",
                filename=SAFETENSORS_FILE_NAME,
            )
            # /path/to/lerobot/cache/hub/spaces-a-b/c/model.safetensors
            pretrained_model_dir = Path(storage_folder).parent
            model_file = pretrained_model_dir / SAFETENSORS_FILE_NAME
            config_file = pretrained_model_dir / POLICY_CFG_FILE_NAME

            logger.info("Loading weights from Hugging Face Hub")

        else:
            # Load policy from a local directory.
            config_path = Path(config_path)
            if config_path.is_dir():
                # outputs/train/diffusion_pusht_transformer/checkpoints/000100/
                # or outputs/train/diffusion_pusht_transformer/checkpoints/010000/pretrained_model/
                pretrained_model_dir = config_path
                config_file = pretrained_model_dir / POLICY_CFG_FILE_NAME
                model_file = pretrained_model_dir / SAFETENSORS_FILE_NAME
            elif config_path.is_file():
                # path to config.json
                # outputs/train/diffusion_pusht_transformer/checkpoints/010000/pretrained_model/config.json
                pretrained_model_dir = config_path.parent
                config_file = config_path
                model_file = pretrained_model_dir / SAFETENSORS_FILE_NAME
            else:
                raise FileNotFoundError(f"Neither directory nor file at '{config_path}'")

            # For backward compatibility, we also check if the model is in the parent directory.
            # This is for checkpoints created with older versions of the code.
            # Example old path: `outputs/train/.../checkpoints/last/model.safetensors`
            # Example new path: `outputs/train/.../checkpoints/last/pretrained_model/model.safetensors`
            if not model_file.exists():
                model_file_in_parent = pretrained_model_dir.parent / SAFETENSORS_FILE_NAME
                if model_file_in_parent.exists():
                    model_file = model_file_in_parent

            logger.info("Loading weights from local directory")

        # Load the configuration using the PreTrainedConfig base class
        # which will automatically select the correct subclass based on the 'type' field
        from lerobot.configs.policies import PreTrainedConfig as BaseConfig
        config = BaseConfig.from_pretrained(config_file)

        if device is None:
            device = get_device()

        model = cls(config)
        model = cls._load_as_safetensor(model, model_file, device, strict=False)
        model.to(device)
        model.eval()
        
        print(model)
     
        
        return model

    @classmethod
    def _load_as_safetensor(cls, model: T, model_file: str, map_location: str, strict: bool) -> T:
        if packaging.version.parse(safetensors.__version__) < packaging.version.parse("0.4.3"):
            load_model_as_safetensor(model, model_file, strict=strict)
            if map_location != "cpu":
                logging.warning(
                    "Loading model weights on other devices than 'cpu' is not supported natively in your version of safetensors."
                    " This means that the model is loaded on 'cpu' first and then copied to the device."
                    " This leads to a slower loading time."
                    " Please update safetensors to version 0.4.3 or above for improved performance."
                )
                model.to(map_location)
        else:
            safetensors.torch.load_model(model, model_file, strict=strict, device=map_location)
        return model

    # def generate_model_card(self, *args, **kwargs) -> ModelCard:
    #     card = ModelCard.from_template(
    #         card_data=self._hub_mixin_info.model_card_data,
    #         template_str=self._hub_mixin_info.model_card_template,
    #         repo_url=self._hub_mixin_info.repo_url,
    #         docs_url=self._hub_mixin_info.docs_url,
    #         **kwargs,
    #     )
    #     return card

    @abc.abstractmethod
    def get_optim_params(self) -> dict:
        """
        Returns the policy-specific parameters dict to be passed on to the optimizer.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def reset(self):
        """To be called whenever the environment is reset.

        Does things like clearing caches.
        """
        raise NotImplementedError

    # TODO(aliberts, rcadene): split into 'forward' and 'compute_loss'?
    @abc.abstractmethod
    def forward(self, batch: dict[str, Tensor]) -> tuple[Tensor, dict | None]:
        """_summary_

        Args:
            batch (dict[str, Tensor]): _description_

        Returns:
            tuple[Tensor, dict | None]: The loss and potentially other information. Apart from the loss which
                is a Tensor, all other items should be logging-friendly, native Python types.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def select_action(self, batch: dict[str, Tensor]) -> Tensor:
        """Return one action to run in the environment (potentially in batch mode).

        When the model uses a history of observations, or outputs a sequence of actions, this method deals
        with caching.
        """
        raise NotImplementedError
