#!/usr/bin/env python

# Copyright (c) 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Complete DiT-Policy Architecture Implementation

This module contains the complete implementation of the DiT-Policy architecture
from "The Ingredients for Robotic Diffusion Transformers" paper, including:
- Progressive encoders
- AdaLN modulation
- Time networks
- Complete encoder-decoder structure

Based on the original implementation at:
https://github.com/SudeepDasari/data4robotics
"""

import copy
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional

from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig


def _get_activation_fn(activation):
    """Return an activation function given a string"""
    if activation == "relu":
        return F.relu
    if activation == "gelu":
        return nn.GELU(approximate="tanh")
    if activation == "glu":
        return F.glu
    raise RuntimeError(f"activation should be relu/gelu/glu, not {activation}.")


def _with_pos_embed(tensor, pos=None):
    return tensor if pos is None else tensor + pos


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer sequences."""
    
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        # Compute the positional encodings once in log space
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2, dtype=torch.float)
            * -(np.log(10000.0) / d_model)
        )
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer("pe", pe)

    def forward(self, x):
        """
        Args:
            x: Tensor of shape (seq_len, batch_size, d_model)
        Returns:
            Tensor of shape (seq_len, batch_size, d_model) with positional encodings added
        """
        pe = self.pe[: x.shape[0]]
        pe = pe.repeat((1, x.shape[1], 1))
        return pe.detach().clone()


class TimeNetwork(nn.Module):
    """Time embedding network for diffusion timesteps."""
    
    def __init__(self, time_dim, out_dim, learnable_w=False):
        assert time_dim % 2 == 0, "time_dim must be even!"
        half_dim = int(time_dim // 2)
        super().__init__()

        w = np.log(10000) / (half_dim - 1)
        w = torch.exp(torch.arange(half_dim) * -w).float()
        self.register_parameter("w", nn.Parameter(w, requires_grad=learnable_w))

        self.out_net = nn.Sequential(
            nn.Linear(time_dim, out_dim), nn.SiLU(), nn.Linear(out_dim, out_dim)
        )

    def forward(self, x):
        assert len(x.shape) == 1, "assumes 1d input timestep array"
        x = x[:, None] * self.w[None]
        x = torch.cat((torch.cos(x), torch.sin(x)), dim=1)
        return self.out_net(x)


class SelfAttnEncoder(nn.Module):
    """Self-attention encoder block for progressive encoding."""
    
    def __init__(
        self, d_model, nhead=8, dim_feedforward=2048, dropout=0.1, activation="gelu"
    ):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=False)
        # Implementation of Feedforward model
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.linear2 = nn.Linear(dim_feedforward, d_model)

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        self.dropout3 = nn.Dropout(dropout)

        self.activation = _get_activation_fn(activation)

    def forward(self, src, pos):
        q = k = _with_pos_embed(src, pos)
        src2, _ = self.self_attn(q, k, value=src, need_weights=False)
        src = src + self.dropout1(src2)
        src = self.norm1(src)
        src2 = self.linear2(self.dropout2(self.activation(self.linear1(src))))
        src = src + self.dropout3(src2)
        src = self.norm2(src)
        return src

    def reset_parameters(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)


class ShiftScaleMod(nn.Module):
    """Shift and scale modulation for AdaLN."""
    
    def __init__(self, dim):
        super().__init__()
        self.act = nn.SiLU()
        self.scale = nn.Linear(dim, dim)
        self.shift = nn.Linear(dim, dim)

    def forward(self, x, c):
        c = self.act(c)
        return x * self.scale(c)[None] + self.shift(c)[None]

    def reset_parameters(self):
        nn.init.xavier_uniform_(self.scale.weight)
        nn.init.xavier_uniform_(self.shift.weight)
        nn.init.zeros_(self.scale.bias)
        nn.init.zeros_(self.shift.bias)


class ZeroScaleMod(nn.Module):
    """Zero-initialized scale modulation for AdaLN."""
    
    def __init__(self, dim):
        super().__init__()
        self.act = nn.SiLU()
        self.scale = nn.Linear(dim, dim)

    def forward(self, x, c):
        c = self.act(c)
        return x * self.scale(c)[None]

    def reset_parameters(self):
        nn.init.zeros_(self.scale.weight)
        nn.init.zeros_(self.scale.bias)


class DiTDecoderWithCrossAttn(nn.Module):
    """DiT decoder block with Cross-Attention following the standard DiT architecture."""
    
    def __init__(
        self, d_model, nhead, dim_feedforward=2048, dropout=0.1, activation="gelu"
    ):
        super().__init__()
        
        # Self-attention components
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=False)
        
        # Cross-attention components (No AdaLN modulation)
        self.cross_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=False)
        
        # Feedforward network
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.linear2 = nn.Linear(dim_feedforward, d_model)

        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)  # Self-attention input
        self.norm2 = nn.LayerNorm(d_model)  # Cross-attention input
        self.norm3 = nn.LayerNorm(d_model)  # FFN input

        # Dropout layers
        self.dropout1 = nn.Dropout(dropout)  # Self-attention
        self.dropout2 = nn.Dropout(dropout)  # Cross-attention
        self.dropout3 = nn.Dropout(dropout)  # FFN

        self.activation = _get_activation_fn(activation)

        # AdaLN modulation layers for self-attention branch
        self.self_attn_scale_shift = ShiftScaleMod(d_model)  # γ1, β1
        self.self_attn_scale = ZeroScaleMod(d_model)         # α1
        
        # AdaLN modulation layers for FFN branch  
        self.ffn_scale_shift = ShiftScaleMod(d_model)        # γ2, β2
        self.ffn_scale = ZeroScaleMod(d_model)               # α2

    def forward(self, x, t, cond):
        """
        Forward pass following DiT architecture:
        Input → Self-Attention (with AdaLN) → Cross-Attention (no AdaLN) → FFN (with AdaLN)
        
        Args:
            x: (seq_len, batch_size, d_model) - Input action tokens
            t: (batch_size, d_model) - Time embedding  
            cond: (cond_seq_len, batch_size, d_model) - Encoder outputs
        
        Returns:
            (seq_len, batch_size, d_model) - Enhanced features
        """
        
        # Process conditioning vector for AdaLN modulation
        cond_for_modulation = torch.mean(cond, axis=0)  # (batch_size, d_model)
        cond_for_modulation = cond_for_modulation + t   # Add time embedding
        
        # ========== Self-Attention Branch (with AdaLN) ==========
        # LayerNorm → Scale,Shift modulation (γ1, β1)
        x_norm1 = self.norm1(x)
        x_modulated = self.self_attn_scale_shift(x_norm1, cond_for_modulation)
        
        # Multi-Head Self-Attention
        x_self_attn, _ = self.self_attn(x_modulated, x_modulated, x_modulated, need_weights=False)
        
        # Scale modulation (α1) + residual connection
        x = x + self.self_attn_scale(self.dropout1(x_self_attn), cond_for_modulation)
        
        # ========== Cross-Attention Branch (Standard, no AdaLN) ==========
        # LayerNorm
        x_norm2 = self.norm2(x)
        
        # Multi-Head Cross-Attention (Query from x, Key&Value from encoder outputs)
        x_cross_attn, attn_weights = self.cross_attn(
            query=x_norm2,       # (seq_len, batch_size, d_model)
            key=cond,            # (cond_seq_len, batch_size, d_model)  
            value=cond,          # (cond_seq_len, batch_size, d_model)
            need_weights=False
        )
        
        # Simple residual connection (no modulation)
        x = x + self.dropout2(x_cross_attn)
        
        # ========== Feedforward Branch (with AdaLN) ==========
        # LayerNorm → Scale,Shift modulation (γ2, β2)
        x_norm3 = self.norm3(x)
        x_ffn_input = self.ffn_scale_shift(x_norm3, cond_for_modulation)
        
        # Pointwise Feedforward
        x_ffn = self.linear2(self.dropout3(self.activation(self.linear1(x_ffn_input))))
        
        # Scale modulation (α2) + final residual connection
        x = x + self.ffn_scale(x_ffn, cond_for_modulation)

        return x

    def reset_parameters(self):
        """Initialize parameters following DiT conventions."""
        # Standard transformer initialization
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

        # Reset AdaLN modulation layers (only for self-attention and FFN)
        for mod in (self.self_attn_scale_shift, self.self_attn_scale, 
                   self.ffn_scale_shift, self.ffn_scale):
            mod.reset_parameters()


class FinalLayer(nn.Module):
    """Final output layer with AdaLN modulation."""
    
    def __init__(self, hidden_size, out_size):
        super().__init__()
        self.norm_final = nn.LayerNorm(hidden_size, elementwise_affine=False, eps=1e-6)
        self.linear = nn.Linear(hidden_size, out_size, bias=True)
        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(), nn.Linear(hidden_size, 2 * hidden_size, bias=True)
        )

    def forward(self, x, t, cond):
        # process the conditioning vector first
        cond = torch.mean(cond, axis=0)
        cond = cond + t

        shift, scale = self.adaLN_modulation(cond).chunk(2, dim=1)
        x = x * scale[None] + shift[None]
        x = self.linear(x)
        return x.transpose(0, 1)

    def reset_parameters(self):
        for p in self.parameters():
            nn.init.zeros_(p)


class TransformerEncoder(nn.Module):
    """Progressive transformer encoder."""
    
    def __init__(self, base_module, num_layers):
        super().__init__()
        self.layers = nn.ModuleList(
            [copy.deepcopy(base_module) for _ in range(num_layers)]
        )

        for l in self.layers:
            l.reset_parameters()

    def forward(self, src, pos):
        x, outputs = src, []
        for layer in self.layers:
            x = layer(x, pos)
            outputs.append(x)
        return outputs


class TransformerDecoder(TransformerEncoder):
    """Progressive transformer decoder."""

    def forward(self, src, t, all_conds):
        x = src
        for layer, cond in zip(self.layers, all_conds):
            x = layer(x, t, cond)
        return x


class DiTNoiseNet(nn.Module):
    """
    Complete DiT Noise Network with Progressive Encoders.

    This is the core architecture from dit-policy that includes:
    - Progressive encoder for observations
    - Time embedding network
    - AdaLN-modulated decoder for actions
    - Final epsilon prediction layer
    """

    def __init__(
        self,
        ac_dim,
        ac_chunk,
        cond_dim,
        time_dim=256,
        hidden_dim=512,
        num_blocks=6,
        dropout=0.1,
        dim_feedforward=2048,
        nhead=8,
        activation="gelu",
    ):
        super().__init__()

        self.ac_dim = ac_dim
        self.ac_chunk = ac_chunk
        self.hidden_dim = hidden_dim

        # positional encoding blocks
        self.enc_pos = PositionalEncoding(hidden_dim)
        self.register_parameter(
            "dec_pos",
            nn.Parameter(torch.empty(ac_chunk, 1, hidden_dim), requires_grad=True),
        )
        nn.init.xavier_uniform_(self.dec_pos.data)

        # input encoder mlps
        self.time_net = TimeNetwork(time_dim, hidden_dim)
        self.ac_proj = nn.Sequential(
            nn.Linear(ac_dim, ac_dim),
            nn.GELU(approximate="tanh"),
            nn.Linear(ac_dim, hidden_dim),
        )

        # condition projection
        self.cond_proj = nn.Linear(cond_dim, hidden_dim)

        # encoder blocks (progressive encoding)
        encoder_module = SelfAttnEncoder(
            hidden_dim,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            activation=activation,
        )
        self.encoder = TransformerEncoder(encoder_module, num_blocks)

        # decoder blocks (AdaLN modulated)
        decoder_module = DiTDecoderWithCrossAttn(
            hidden_dim,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            activation=activation,
        )
        self.decoder = TransformerDecoder(decoder_module, num_blocks)

        # turns predicted tokens into epsilons
        self.eps_out = FinalLayer(hidden_dim, ac_dim)

        print(
            f"DiT Noise Network parameters: {sum(p.numel() for p in self.parameters()):,}"
        )

    def forward(self, noise_actions, time, obs_enc, enc_cache=None):
        """
        Forward pass through the DiT network.

        Args:
            noise_actions: (B, T, ac_dim) noisy action sequence
            time: (B,) diffusion timestep
            obs_enc: (B, seq_len, cond_dim) observation encoding
            enc_cache: Optional cached encoder outputs

        Returns:
            tuple: (enc_cache, predicted_noise)
        """
        if enc_cache is None:
            enc_cache = self.forward_enc(obs_enc)
        return enc_cache, self.forward_dec(noise_actions, time, enc_cache)

    def forward_enc(self, obs_enc):
        """Progressive encoding of observations."""
        # Project observations to hidden dimension
        obs_enc = self.cond_proj(obs_enc)  # (B, seq_len, hidden_dim)
        obs_enc = obs_enc.transpose(0, 1)  # (seq_len, B, hidden_dim)

        # Add positional encoding
        pos = self.enc_pos(obs_enc)

        # Progressive encoding through multiple layers
        enc_cache = self.encoder(obs_enc, pos)
        return enc_cache

    def forward_dec(self, noise_actions, time, enc_cache):
        """AdaLN-modulated decoding of actions."""
        # Time embedding
        time_enc = self.time_net(time)  # (B, hidden_dim)

        # Action token projection
        ac_tokens = self.ac_proj(noise_actions)  # (B, T, hidden_dim)
        ac_tokens = ac_tokens.transpose(0, 1)  # (T, B, hidden_dim)
        dec_in = ac_tokens + self.dec_pos

        # Apply decoder with progressive conditioning
        dec_out = self.decoder(dec_in, time_enc, enc_cache)

        # Apply final epsilon prediction layer
        return self.eps_out(dec_out, time_enc, enc_cache[-1])


class DiTPolicyForDiffusion(nn.Module):
    """
    Complete DiT Policy wrapper for lerobot integration.

    This class adapts the DiT architecture to work with lerobot's
    diffusion policy interface while preserving all original features.
    Now handles raw observations internally with specialized encoders.
    """

    def __init__(self, config: DiffusionConfig, cond_dim: int):
        super().__init__()
        self.config = config

        # Extract action dimensions from output features
        action_feature = None
        for key, feature in config.output_features.items():
            if "action" in key:
                action_feature = feature
                break

        if action_feature is None:
            # Fallback: assume 2D action space
            action_dim = 2
        else:
            action_dim = action_feature.shape[0]

        horizon = config.horizon

        # Build specialized observation encoders
        self._build_observation_encoders()

        # Calculate actual condition dimension from encoders
        self.raw_cond_dim = self._calculate_encoded_obs_dim()

        # Create the DiT noise network
        self.dit_net = None
        self.action_dim = action_dim
        self.horizon = horizon

        # Cache for encoder outputs (optimization)
        self._enc_cache = None
        self._last_raw_obs = None

        # Store config parameters for delayed initialization
        self.time_dim = config.diffusion_step_embed_dim
        self.hidden_dim = config.diffusion_step_embed_dim
        self.num_blocks = config.n_layer
        self.dropout = config.p_drop_attn
        self.dim_feedforward = 4 * config.diffusion_step_embed_dim
        self.nhead = config.n_head

    def _build_observation_encoders(self):
        """Build specialized encoders for different observation modalities."""
        # Initialize encoder containers
        self.rgb_encoders = nn.ModuleDict()  # For separate encoders per camera
        self.shared_rgb_encoder = None       # For shared encoder
        self.state_encoders = nn.ModuleDict()
        self.generic_state_encoder = nn.ModuleDict()

        # Image encoder settings
        self.use_separate_rgb_encoders = getattr(self.config, 'use_separate_rgb_encoder_per_camera', True)
        self.image_encoder_built = False

        # State encoders will be built dynamically based on observation names and dimensions
        print(f"DiT: Observation encoders will be built dynamically")
        print(f"DiT: Use separate RGB encoders: {self.use_separate_rgb_encoders}")

    def _calculate_encoded_obs_dim(self) -> int:
        """Calculate the total dimension of encoded observations."""
        # Since we're building encoders dynamically, return None for now
        # The actual dimension will be determined from the first batch
        return None

    def _create_rgb_encoder_config(self, image_shape):
        """Create a config for RGB encoder based on image shape."""
        # Create a simple namespace object that mimics the config
        class SimpleConfig:
            def __init__(self):
                self.crop_shape = [84, 84]
                self.crop_is_random = False
                self.vision_backbone = 'resnet18'
                self.pretrained_backbone_weights = None
                self.use_group_norm = False
                self.spatial_softmax_num_keypoints = 32

                # Mock image features
                class MockImageFeature:
                    def __init__(self, shape):
                        self.shape = shape

                self.image_features = {
                    'dummy_image': MockImageFeature(list(image_shape[1:]))  # Remove batch dimension
                }

        return SimpleConfig()

    def _create_rgb_encoder(self, encoder_config):
        """Create a DiffusionRgbEncoder with the given config."""
        from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionRgbEncoder

        try:
            return DiffusionRgbEncoder(encoder_config)
        except Exception as e:
            print(f"Warning: Failed to create DiffusionRgbEncoder: {e}")
            print("Falling back to simple image processing")
            # Fallback: create a simple CNN encoder
            return self._create_simple_image_encoder()

    def _create_simple_rgb_encoder(self):
        """Create a simple CNN encoder for RGB images as fallback."""
        return nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=8, stride=4),
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=4, stride=2),
            nn.ReLU(),
            nn.Conv2d(64, 64, kernel_size=3, stride=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((4, 4)),
            nn.Flatten(),
            nn.Linear(64 * 4 * 4, 256),
            nn.ReLU(),
            nn.Linear(256, 128)
        )

    def _process_raw_observations(self, raw_obs_dict: dict) -> torch.Tensor:
        """
        Process raw observation dictionary using specialized encoders.

        Args:
            raw_obs_dict: Dictionary of raw observations from lerobot

        Returns:
            Processed observation tensor (B, seq_len, obs_dim)
        """
        encoded_obs_list = []
        batch_size = None
        device = None

        # Get batch size and device
        for value in raw_obs_dict.values():
            if batch_size is None:
                batch_size = value.shape[0]
                device = value.device
                break

        # Process RGB image observations using DiffusionRgbEncoder
        for key, value in raw_obs_dict.items():
            if key.startswith("observation.") and len(value.shape) == 4:  # (B, C, H, W)
                # Only process RGB images (3 channels)
                if value.shape[1] != 3:
                    print(f"DiT: Skipping non-RGB image {key} with {value.shape[1]} channels")
                    continue

                camera_name = key.replace("observation.", "")

                if self.use_separate_rgb_encoders:
                    # Use separate DiffusionRgbEncoder for each camera
                    if camera_name not in self.rgb_encoders:
                        try:
                            # Create DiffusionRgbEncoder for this camera
                            encoder_config = self._create_rgb_encoder_config(value.shape)
                            self.rgb_encoders[camera_name] = self._create_rgb_encoder(encoder_config).to(device)
                            print(f"DiT: Created DiffusionRgbEncoder for {camera_name} (shape={value.shape})")
                        except Exception as e:
                            print(f"DiT: Failed to create DiffusionRgbEncoder for {camera_name}: {e}")
                            # Fallback to simple encoder
                            self.rgb_encoders[camera_name] = self._create_simple_rgb_encoder().to(device)
                            print(f"DiT: Using simple RGB encoder for {camera_name}")

                    encoded_img = self.rgb_encoders[camera_name](value)
                    encoded_obs_list.append(encoded_img)
                else:
                    # Use shared DiffusionRgbEncoder for all cameras
                    if self.shared_rgb_encoder is None:
                        try:
                            encoder_config = self._create_rgb_encoder_config(value.shape)
                            self.shared_rgb_encoder = self._create_rgb_encoder(encoder_config).to(device)
                            print(f"DiT: Created shared DiffusionRgbEncoder (shape={value.shape})")
                        except Exception as e:
                            print(f"DiT: Failed to create shared DiffusionRgbEncoder: {e}")
                            # Fallback to simple encoder
                            self.shared_rgb_encoder = self._create_simple_rgb_encoder().to(device)
                            print(f"DiT: Using simple shared RGB encoder")

                    encoded_img = self.shared_rgb_encoder(value)
                    encoded_obs_list.append(encoded_img)

        # Process state observations (simplified)
        for key, value in raw_obs_dict.items():
            if not key.startswith("observation."):
                continue

            # Skip images (already processed above)
            if len(value.shape) == 4:
                continue

            # Handle different observation types
            if len(value.shape) == 2:  # Vector observations (B, dim)
                # Create simple encoder for this observation type if needed
                obs_name = key.replace("observation.", "")
                if obs_name not in self.generic_state_encoder:
                    obs_dim = value.shape[1]
                    self.generic_state_encoder[obs_name] = nn.Sequential(
                        nn.Linear(obs_dim, obs_dim),
                        nn.ReLU(),
                        nn.Linear(obs_dim, obs_dim)
                    ).to(device)
                    print(f"DiT: Created encoder for {obs_name} (dim={obs_dim})")

                # Convert boolean tensors to float before passing to linear layers
                if value.dtype == torch.bool:
                    value = value.float()

                encoded_state = self.generic_state_encoder[obs_name](value)
                encoded_obs_list.append(encoded_state)

            elif len(value.shape) == 3:  # Sequence observations (B, T, dim)
                # Take the last timestep or use mean pooling
                if value.shape[1] == 1:
                    encoded_obs_list.append(value.squeeze(1))
                else:
                    pooled_obs = torch.mean(value, dim=1)
                    encoded_obs_list.append(pooled_obs)

        if encoded_obs_list:
            # Concatenate all encoded observations
            combined_obs = torch.cat(encoded_obs_list, dim=-1)  # (B, total_encoded_dim)
            # Add sequence dimension for DiT encoder
            obs_seq = combined_obs.unsqueeze(1)  # (B, 1, total_encoded_dim)
        else:
            # Fallback: create dummy observations
            obs_seq = torch.zeros(batch_size, 1, self.raw_cond_dim or 64, device=device)

        return obs_seq

    def _initialize_dit_net(self, obs_dim: int, device: torch.device):
        """Initialize DiT network with correct observation dimension."""
        if self.dit_net is None:
            self.dit_net = DiTNoiseNet(
                ac_dim=self.action_dim,
                ac_chunk=self.horizon,
                cond_dim=obs_dim,
                time_dim=self.time_dim,
                hidden_dim=self.hidden_dim,
                num_blocks=self.num_blocks,
                dropout=self.dropout,
                dim_feedforward=self.dim_feedforward,
                nhead=self.nhead,
                activation="gelu",
            ).to(device)
            self.raw_cond_dim = obs_dim
            print(f"DiT network initialized with obs_dim={obs_dim} on device={device}")

    def _reinitialize_dit_net_if_needed(self, obs_dim: int, device: torch.device):
        """Reinitialize DiT network if observation dimension changes."""
        if self.dit_net is not None and self.raw_cond_dim != obs_dim:
            print(f"⚠️ Observation dimension changed from {self.raw_cond_dim} to {obs_dim}, reinitializing DiT network...")
            self.dit_net = None
            self._enc_cache = None
            self._last_raw_obs = None
            self._initialize_dit_net(obs_dim, device)

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: dict, **kwargs):
        """
        Forward pass compatible with lerobot's diffusion interface.
        Now handles raw observations for complete dit-policy architecture.

        Args:
            sample: (B, T, action_dim) noisy action sequence
            timestep: (B,) diffusion timestep
            global_cond: Dictionary of raw observations from lerobot

        Returns:
            (B, T, action_dim) predicted noise
        """
        batch_size = sample.shape[0]

        # Process raw observations
        obs_seq = self._process_raw_observations(global_cond)  # (B, seq_len, obs_dim)

        # Initialize or reinitialize DiT network if needed
        obs_dim = obs_seq.shape[-1]
        device = sample.device

        if self.dit_net is None:
            self._initialize_dit_net(obs_dim, device)
        else:
            self._reinitialize_dit_net_if_needed(obs_dim, device)

        # Use cached encoder outputs if observations haven't changed
        enc_cache = None
        if (self._last_raw_obs is not None and
            self._obs_equal(global_cond, self._last_raw_obs)):
            enc_cache = self._enc_cache

        # Forward through DiT network
        enc_cache, predicted_noise = self.dit_net(sample, timestep, obs_seq, enc_cache)

        # Cache encoder outputs for next iteration
        self._enc_cache = enc_cache
        self._last_raw_obs = self._copy_obs_dict(global_cond)

        return predicted_noise

    def _obs_equal(self, obs1: dict, obs2: dict) -> bool:
        """Check if two observation dictionaries are equal."""
        if set(obs1.keys()) != set(obs2.keys()):
            return False

        for key in obs1.keys():
            if not torch.equal(obs1[key], obs2[key]):
                return False
        return True

    def _copy_obs_dict(self, obs_dict: dict) -> dict:
        """Create a deep copy of observation dictionary."""
        return {key: value.clone() for key, value in obs_dict.items()}
