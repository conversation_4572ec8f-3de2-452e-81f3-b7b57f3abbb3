#!/usr/bin/env python

"""
测试脚本：验证evaluate_model函数的最终修复
"""

import sys
import logging
import torch
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

def test_load_dc_weights_safety():
    """测试load_dc_weights_from_checkpoint的安全性"""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
    
    try:
        from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
        from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
        from lerobot.scripts.load_pretrained_dc import load_dc_weights_from_checkpoint
        
        logging.info("✅ 模块导入成功")
        
        # 创建基本配置
        config = DiffusionConfig()
        config.horizon = 16
        config.n_obs_steps = 2
        config.diffusion_step_embed_dim = 256
        config.n_layer = 4
        config.n_head = 4
        config.n_cond_layers = 2
        config.use_transformer = True
        config.input_shapes = {"observation.state": [32]}
        config.output_shapes = {"action": [2]}
        
        logging.info("✅ 配置创建成功")
        
        # 测试1: 创建DiffusionModel_DC（有DC tokens）
        logging.info("\n🧪 测试1: 创建有DC tokens的模型")
        dc_model = DiffusionModel_DC(
            config=config,
            n_dc_tokens=4,
            n_dc_layers=3,
            use_dc_t=False,
            cond_dim=32
        )
        
        # 检查DC tokens是否存在
        if hasattr(dc_model.net, 'dc_tokens'):
            logging.info(f"✅ DC模型有dc_tokens，形状: {dc_model.net.dc_tokens.shape}")
        else:
            logging.error("❌ DC模型没有dc_tokens")
            return False
        
        # 创建模拟checkpoint
        mock_checkpoint = {
            'net.dc_tokens': torch.randn_like(dc_model.net.dc_tokens),
        }
        
        # 保存模拟checkpoint
        mock_checkpoint_path = "/tmp/mock_dc_checkpoint.pth"
        torch.save(mock_checkpoint, mock_checkpoint_path)
        
        # 测试加载到DC模型
        try:
            load_dc_weights_from_checkpoint(dc_model, mock_checkpoint_path)
            logging.info("✅ DC模型加载权重成功")
        except Exception as e:
            logging.error(f"❌ DC模型加载权重失败: {e}")
            return False
        
        # 测试2: 创建普通模型（没有DC tokens）
        logging.info("\n🧪 测试2: 创建没有DC tokens的普通模型")
        
        # 创建一个模拟的普通模型（没有dc_tokens属性）
        class MockModel:
            def __init__(self):
                self.net = MockNet()
        
        class MockNet:
            def __init__(self):
                # 故意不添加dc_tokens属性
                self.some_other_param = torch.nn.Parameter(torch.randn(10, 10))
        
        normal_model = MockModel()
        
        # 检查普通模型没有DC tokens
        if not hasattr(normal_model.net, 'dc_tokens'):
            logging.info("✅ 普通模型确实没有dc_tokens")
        else:
            logging.error("❌ 普通模型不应该有dc_tokens")
            return False
        
        # 测试加载到普通模型（应该安全跳过）
        try:
            load_dc_weights_from_checkpoint(normal_model, mock_checkpoint_path)
            logging.info("✅ 普通模型安全跳过DC权重加载")
        except Exception as e:
            logging.error(f"❌ 普通模型加载失败（不应该失败）: {e}")
            return False
        
        # 清理
        Path(mock_checkpoint_path).unlink(missing_ok=True)
        
        logging.info("\n🎉 所有测试通过! load_dc_weights_from_checkpoint现在是安全的。")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_evaluate_model_logic():
    """测试evaluate_model函数的逻辑"""
    
    logging.info("\n🔍 测试evaluate_model函数逻辑...")
    
    try:
        # 检查函数签名
        from train_dc_with_dataset import evaluate_model
        import inspect
        
        sig = inspect.signature(evaluate_model)
        logging.info(f"✅ 函数签名: {sig}")
        
        # 检查第一个参数是否为DiffusionModel_DC
        first_param = list(sig.parameters.values())[0]
        if 'DiffusionModel_DC' in str(first_param.annotation):
            logging.info("✅ 第一个参数类型正确 (DiffusionModel_DC)")
        else:
            logging.warning(f"⚠️ 第一个参数类型: {first_param.annotation}")
        
        # 检查函数源码中的关键修复
        source = inspect.getsource(evaluate_model)
        
        # 检查是否使用了传入的diffusion_model参数
        if 'load_dc_weights_from_checkpoint(diffusion_model, pth_path)' in source:
            logging.info("✅ 正确使用传入的diffusion_model参数")
        else:
            logging.error("❌ 未正确使用传入的diffusion_model参数")
            return False
        
        # 检查是否替换了策略的diffusion模型
        if 'base_policy.diffusion = diffusion_model' in source:
            logging.info("✅ 正确替换策略的diffusion模型")
        else:
            logging.error("❌ 未正确替换策略的diffusion模型")
            return False
        
        logging.info("✅ evaluate_model函数逻辑检查通过")
        return True
        
    except Exception as e:
        logging.error(f"❌ evaluate_model逻辑检查失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试evaluate_model的最终修复...")
    
    success1 = test_load_dc_weights_safety()
    success2 = test_evaluate_model_logic()
    
    if success1 and success2:
        print("\n🎉 所有测试通过! 修复应该有效。")
        print("\n📝 修复总结:")
        print("1. ✅ evaluate_model现在正确使用传入的DiffusionModel_DC")
        print("2. ✅ load_dc_weights_from_checkpoint现在有安全检查")
        print("3. ✅ 不会再出现'TransformerForDiffusion' object has no attribute 'dc_tokens'错误")
        print("\n🚀 现在可以重新运行train_dc_with_dataset.py了!")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
        sys.exit(1)
