# 支持 .pth 权重文件的 eval.py 修改说明

## 概述

本文档说明了如何修改 `lerobot/scripts/eval.py` 以支持加载 `.pth` 格式的预训练权重文件。修改后的脚本保持了与原有 `.safetensors` 格式的完全兼容性，同时增加了对 `.pth` 文件的支持。

## 修改内容

### 1. 新增函数

#### `load_pth_weights_to_policy()`
- **功能**: 从 `.pth` 文件加载权重到已创建的 policy 实例
- **参数**: 
  - `policy`: 已创建的 policy 实例
  - `pth_path`: .pth 权重文件路径
  - `strict`: 是否严格匹配权重键名 (默认 False)
- **特性**: 
  - 自动处理嵌套的权重结构 (`model`, `state_dict`)
  - 详细的加载状态反馈
  - 错误处理和容错机制

#### `check_and_handle_pth_weights()`
- **功能**: 检查是否存在 .pth 权重文件，并返回相应的配置
- **支持的文件名**: 
  - `model.pth`
  - `pytorch_model.pth`
  - `weights.pth`
- **搜索位置**: 
  - 指定目录内
  - 父目录内
  - 直接指定的 .pth 文件路径

### 2. 修改的主函数

#### `eval_main()`
- 在创建 policy 之前检查 .pth 文件
- 如果找到 .pth 文件，采用两步加载流程：
  1. 创建不带预训练权重的 policy
  2. 加载 .pth 权重
- 如果 .pth 加载失败，自动回退到标准加载方式

## 使用方法

### 1. 基本用法

```bash
# 直接指定 .pth 文件
python lerobot/scripts/eval.py \
    --policy.path=path/to/model.pth \
    --env.type=pusht \
    --eval.batch_size=10 \
    --eval.n_episodes=10 \
    --device=cuda

# 指定包含 .pth 文件的目录
python lerobot/scripts/eval.py \
    --policy.path=path/to/model/directory \
    --env.type=pusht \
    --eval.batch_size=10 \
    --eval.n_episodes=10 \
    --device=cuda
```

### 2. 使用预训练模型目录

```bash
# 使用 use_pretrained_model 目录中的模型
python lerobot/scripts/eval.py \
    --policy.path=use_pretrained_model/saved_models \
    --env.type=pusht \
    --eval.batch_size=5 \
    --eval.n_episodes=5 \
    --device=cuda
```

### 3. 推荐的目录结构

```
model_directory/
├── config.json          # 模型配置文件 (必需)
├── model.pth            # PyTorch 权重文件
└── other_files...       # 其他文件 (可选)
```

或者：

```
checkpoint_directory/
├── pretrained_model/
│   ├── config.json      # 模型配置文件
│   └── model.safetensors # 标准格式 (如果存在)
├── model.pth            # PyTorch 权重文件
└── other_files...
```

## 兼容性

### 支持的权重文件格式
- ✅ `.safetensors` (原有格式，完全兼容)
- ✅ `.pth` (新增支持)

### 支持的 .pth 文件结构
- 直接的 state_dict: `torch.load('model.pth')`
- 嵌套结构: `{'model': state_dict}` 或 `{'state_dict': state_dict}`

### 支持的文件命名
- `model.pth`
- `pytorch_model.pth`
- `weights.pth`

## 错误处理

### 1. 自动回退机制
如果 .pth 文件加载失败，脚本会：
1. 显示错误信息
2. 自动回退到标准的 .safetensors 加载方式
3. 继续正常执行

### 2. 详细的状态反馈
- 🔍 文件发现提示
- 🔄 加载进度提示
- ✅ 成功加载确认
- ⚠️ 部分匹配警告
- ❌ 错误信息显示

## 注意事项

1. **配置文件必需**: 确保 `config.json` 文件存在且包含正确的模型配置
2. **权重格式**: .pth 文件应包含模型的 state_dict
3. **键名匹配**: 使用 `strict=False` 允许部分权重不匹配
4. **设备兼容**: 权重会自动加载到 CPU 后转移到目标设备
5. **向后兼容**: 完全兼容现有的 .safetensors 格式

## 示例脚本

运行 `eval_with_pth_example.py` 查看详细的使用示例：

```bash
python eval_with_pth_example.py
```

## 测试

### 1. 创建测试环境
```bash
python eval_with_pth_example.py
# 选择 'y' 创建测试目录结构
```

### 2. 测试 .pth 加载
```bash
# 将您的 .pth 文件放入 test_pth_eval 目录
python lerobot/scripts/eval.py \
    --policy.path=test_pth_eval \
    --env.type=pusht \
    --eval.batch_size=2 \
    --eval.n_episodes=2 \
    --device=cpu
```

## 故障排除

### 常见问题

1. **找不到配置文件**
   - 确保 `config.json` 与 .pth 文件在同一目录或相关目录中
   - 检查文件路径是否正确

2. **权重加载失败**
   - 检查 .pth 文件是否损坏
   - 确认权重文件与模型架构匹配
   - 查看详细的错误信息

3. **键名不匹配**
   - 这是正常的，脚本使用 `strict=False` 允许部分不匹配
   - 检查是否有关键层的权重缺失

### 调试技巧

1. 使用较小的批次大小进行测试
2. 首先在 CPU 上测试，确认权重加载正常
3. 检查控制台输出的详细信息
4. 如果问题持续，可以回退到 .safetensors 格式

## 总结

修改后的 `eval.py` 提供了：
- 🔄 无缝的 .pth 权重支持
- 🛡️ 强大的错误处理机制
- 🔍 智能的文件检测
- 📊 详细的状态反馈
- 🔙 自动回退机制
- 🤝 完全的向后兼容性

这些改进使得在 LeRobot 框架中使用 .pth 格式的预训练权重变得简单而可靠。 