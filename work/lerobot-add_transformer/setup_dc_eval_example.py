#!/usr/bin/env python3
"""
设置 DiffusionModel_DC .pth 权重评估的示例脚本

这个脚本说明如何正确设置目录结构和配置文件来使用 diffusionmodel_dc 训练保存的 .pth 权重
"""

import os
import json
import shutil
from pathlib import Path


def create_dc_eval_setup(pth_file_path: str, output_dir: str = "dc_eval_setup"):
    """
    为 DiffusionModel_DC .pth 权重创建评估设置
    
    Args:
        pth_file_path: .pth 权重文件的路径
        output_dir: 输出目录名称
    """
    
    print("🔧 为 DiffusionModel_DC .pth 权重创建评估设置...")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 1. 复制 .pth 文件
    pth_source = Path(pth_file_path)
    if pth_source.exists():
        pth_dest = output_path / "model.pth"
        shutil.copy2(pth_source, pth_dest)
        print(f"✅ 复制权重文件: {pth_source} -> {pth_dest}")
    else:
        print(f"❌ 找不到权重文件: {pth_file_path}")
        return None
    
    # 2. 创建 diffusion_dc 配置文件
    dc_config = {
        "type": "diffusion_dc",
        "n_obs_steps": 2,
        "normalization_mapping": {
            "VISUAL": "MEAN_STD",
            "STATE": "MIN_MAX",
            "ACTION": "MIN_MAX"
        },
        "input_features": {
            "observation.image": {
                "type": "VISUAL",
                "shape": [3, 96, 96]
            },
            "observation.state": {
                "type": "STATE",
                "shape": [2]
            }
        },
        "output_features": {
            "action": {
                "type": "ACTION",
                "shape": [2]
            }
        },
        "device": "cuda",
        "use_amp": False,
        "horizon": 16,
        "n_action_steps": 8,
        "drop_n_last_frames": 7,
        "vision_backbone": "resnet18",
        "crop_shape": [84, 84],
        "crop_is_random": True,
        "pretrained_backbone_weights": None,
        "use_group_norm": True,
        "spatial_softmax_num_keypoints": 32,
        "use_separate_rgb_encoder_per_camera": True,
        "down_dims": [512, 1024, 2048],
        "kernel_size": 5,
        "n_groups": 8,
        "use_film_scale_modulation": True,
        "noise_scheduler_type": "DDPM",
        "num_train_timesteps": 100,
        "beta_schedule": "squaredcos_cap_v2",
        "beta_start": 0.0001,
        "beta_end": 0.02,
        "prediction_type": "epsilon",
        "clip_sample": True,
        "clip_sample_range": 1.0,
        "use_transformer": True,
        "n_layer": 12,
        "n_head": 16,
        "p_drop_emb": 0.0,
        "p_drop_attn": 0.3,
        "causal_attn": True,
        "n_cond_layers": 12,
        "use_dit": False,
        "use_dit_separate_encoders": True,
        "use_mmdit": False,
        "use_mmdit_dc": False,
        "n_dc_tokens": 4,
        "n_dc_layers": 6,
        "use_dc_t": True,
        "dc_temp": 0.07,
        "dc_scale": 4.0,
        "dc_dweight": 0.0,
        "diffusion_step_embed_dim": 1024,
        "num_inference_steps": None,
        "do_mask_loss_for_padding": False,
        "optimizer_lr": 0.0001,
        "optimizer_betas": [0.95, 0.999],
        "optimizer_eps": 1e-08,
        "optimizer_weight_decay": 1e-06,
        "scheduler_name": "cosine",
        "scheduler_warmup_steps": 500
    }
    
    config_file = output_path / "config.json"
    with open(config_file, 'w') as f:
        json.dump(dc_config, f, indent=2)
    
    print(f"✅ 创建配置文件: {config_file}")
    
    # 3. 显示目录结构
    print(f"\n📂 创建的目录结构:")
    print(f"{output_dir}/")
    print("├── config.json          # DiffusionModel_DC 配置文件")
    print("└── model.pth            # DiffusionModel_DC 权重文件")
    
    return output_path


def show_eval_commands(setup_dir: str):
    """
    显示评估命令示例
    """
    print(f"\n🚀 使用 DiffusionModel_DC .pth 权重进行评估:")
    print("=" * 60)
    
    # 基本评估命令
    eval_command = f"""
python lerobot/scripts/eval.py \\
    --policy.path={setup_dir} \\
    --env.type=pusht \\
    --eval.batch_size=10 \\
    --eval.n_episodes=10 \\
    --use_amp=false \\
    --device=cuda
"""
    
    print("📋 评估命令:")
    print(eval_command)
    
    # CPU 测试命令
    cpu_command = f"""
python lerobot/scripts/eval.py \\
    --policy.path={setup_dir} \\
    --env.type=pusht \\
    --eval.batch_size=2 \\
    --eval.n_episodes=2 \\
    --use_amp=false \\
    --device=cpu
"""
    
    print("💻 CPU 测试命令 (推荐先用这个测试):")
    print(cpu_command)


def check_existing_pth_files():
    """
    检查项目中现有的 .pth 文件
    """
    print("🔍 扫描项目中的 .pth 文件...")
    
    pth_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".pth"):
                pth_files.append(os.path.join(root, file))
    
    if pth_files:
        print(f"找到 {len(pth_files)} 个 .pth 文件:")
        for i, pth_file in enumerate(pth_files, 1):
            print(f"  {i}. {pth_file}")
        return pth_files
    else:
        print("未找到 .pth 文件")
        return []


def main():
    """
    主函数
    """
    print("🤖 DiffusionModel_DC .pth 权重评估设置助手")
    print("=" * 50)
    
    # 扫描现有的 .pth 文件
    existing_pth = check_existing_pth_files()
    
    if existing_pth:
        print(f"\n请选择要使用的 .pth 文件:")
        for i, pth_file in enumerate(existing_pth, 1):
            print(f"  {i}. {pth_file}")
        
        try:
            choice = int(input(f"\n输入序号 (1-{len(existing_pth)}): ")) - 1
            if 0 <= choice < len(existing_pth):
                selected_pth = existing_pth[choice]
                print(f"✅ 选择的文件: {selected_pth}")
                
                # 创建评估设置
                setup_dir = create_dc_eval_setup(selected_pth)
                if setup_dir:
                    show_eval_commands(str(setup_dir))
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    else:
        print("\n⚠️  未找到 .pth 文件，请确保您已经训练并保存了 DiffusionModel_DC 模型")
        print("\n💡 如果您有 .pth 文件，可以手动指定路径:")
        print("python setup_dc_eval_example.py")
        
        # 创建示例设置
        example_pth = "path/to/your/model.pth"
        print(f"\n📝 示例设置 (使用占位符路径):")
        create_dc_eval_setup(example_pth, "dc_eval_example")
    
    print("\n📚 重要说明:")
    print("1. 配置文件类型必须是 'diffusion_dc' 而不是 'diffusion'")
    print("2. DC 相关参数 (n_dc_tokens, n_dc_layers, use_dc_t) 已经设置")
    print("3. 权重加载使用 strict=False，允许部分参数不匹配")
    print("4. 建议先在 CPU 上测试，确认权重加载正常")


if __name__ == "__main__":
    main() 