# DC权重评估指南

## 概述

这个指南解释了如何使用 DC (Dynamic Contrastive) 权重进行模型评估。主要思路是：

1. **从标准检查点加载基础模型** (safetensors格式)
2. **修改配置启用DC功能** (use_dc=True)  
3. **从.pth文件加载DC相关权重** (dc_tokens, dc_t_tokens)
4. **运行评估**

## 问题诊断

你之前遇到的问题是模型配置中 `use_dc=False`，这导致模型使用标准的 `TransformerForDiffusion` 而不是 `TransformerForDiffusion_DC`。

### 配置检查

你的当前配置：
```json
{
    "type": "diffusion",
    "use_dc": false,          // ❌ 这导致没有DC层
    "use_mmdit_dc": false,
    "n_dc_tokens": 4,
    "n_dc_layers": 6, 
    "use_dc_t": true
}
```

需要的配置：
```json
{
    "type": "diffusion",
    "use_dc": true,           // ✅ 启用DC功能
    "use_mmdit_dc": false,    // ✅ 使用TransformerForDiffusion_DC
    "n_dc_tokens": 4,
    "n_dc_layers": 6,
    "use_dc_t": true
}
```

## 文件结构

```
lerobot-add_transformer/
├── outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000/pretrained_model/
│   ├── config.json          # 基础模型配置
│   └── model.safetensors    # 基础模型权重
├── dc_eval_setup/
│   └── model.pth           # DC权重文件
├── lerobot/scripts/eval.py  # 修改后的评估脚本
└── run_dc_eval_final.py    # 最终运行脚本
```

## 使用方法

### 方法1: 使用一键运行脚本 (推荐)

```bash
cd /home/<USER>/work/lerobot-add_transformer
conda activate lerobot
export PYTHONPATH=/home/<USER>/work/lerobot-add_transformer:$PYTHONPATH
python run_dc_eval_final.py
```

### 方法2: 手动运行评估

```bash
cd /home/<USER>/work/lerobot-add_transformer
conda activate lerobot
export PYTHONPATH=/home/<USER>/work/lerobot-add_transformer:$PYTHONPATH

CUDA_VISIBLE_DEVICES=3 python lerobot/scripts/eval.py \
    --policy.pretrained_path=outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000/pretrained_model \
    --policy.dc_pth_path=dc_eval_setup/model.pth \
    --env.type=pusht \
    --eval.batch_size=10 \
    --eval.n_episodes=10 \
    --policy.device=cuda \
    --policy.use_amp=false
```

## 代码修改说明

### 1. eval.py 修改

主要添加了以下功能：

```python
def load_dc_weights_from_pth(policy, pth_path: str, device: str = "cuda"):
    """从.pth文件加载DC权重到已初始化的policy中"""
    # 检查模型是否有DC功能
    if not hasattr(policy.model.net, 'dc_tokens'):
        return False
    
    # 加载并过滤DC相关权重
    pth_data = torch.load(pth_path, map_location='cpu')
    if 'model_state_dict' in pth_data:
        model_weights = pth_data['model_state_dict']
    else:
        model_weights = pth_data
    
    # 加载dc_tokens和dc_t_tokens等DC相关参数
    ...
```

在policy创建后添加DC权重加载：

```python
# 在policy创建后
if hasattr(cfg.policy, 'dc_pth_path') and cfg.policy.dc_pth_path:
    success = load_dc_weights_from_pth(policy, cfg.policy.dc_pth_path, device=str(device))
```

### 2. 临时配置修改

脚本会：
1. 读取原始配置文件
2. 设置 `use_dc=True` 
3. 创建临时目录保存修改的配置
4. 使用临时配置运行评估

## 预期输出

成功运行时应该看到：

```
=== LeRobot DC评估脚本 ===
✅ 所有文件验证成功
   基础模型: /path/to/pretrained_model
   DC权重: /path/to/model.pth

=== 当前模型配置 ===
模型类型: diffusion
use_dc: False
use_mmdit_dc: False

=== 需要启用DC配置 ===
✓ 已设置 use_dc = True
✓ 已设置 use_mmdit_dc = False

[评估] 检测到DC权重路径: /path/to/model.pth
[DC权重加载] 开始从 /path/to/model.pth 加载DC权重...
[DC权重加载] 检测到训练检查点格式
[DC权重加载] 找到 X 个DC相关权重:
  - dc_tokens: torch.Size([6, 4, 1024])
  - dc_t_tokens: torch.Size([100, 4, 1024])
  ✓ 成功加载: dc_tokens
  ✓ 成功加载: dc_t_tokens
[DC权重加载] 成功: 2/2 个权重已加载
[评估] ✓ DC权重加载成功

[模型检查] Policy类型: <class 'lerobot.common.policies.diffusion.modeling_diffusion.DiffusionPolicy'>
[模型检查] 模型类型: <class 'lerobot.common.policies.diffusion.modeling_diffusion.DiffusionModel'>
[模型检查] 网络类型: <class 'lerobot.common.policies.diffusion.modeling_diffusion.TransformerForDiffusion_DC'>
[DC验证] ✓ 模型具有DC tokens: torch.Size([6, 4, 1024])
[DC验证] ✓ 模型具有DC-T tokens: torch.Size([100, 4, 1024])
```

## 故障排除

### 1. 如果看不到DC层

检查配置文件中的 `use_dc` 设置：
```bash
cat outputs/train/diffusion_pusht_transformer_edit/checkpoints/200000/pretrained_model/config.json | grep use_dc
```

### 2. 如果DC权重加载失败

检查.pth文件结构：
```bash
python pth_reader.py --path dc_eval_setup/model.pth
```

### 3. 如果模型类型不对

确保在modeling_diffusion.py的第218行附近有：
```python
elif hasattr(config, 'use_dc') and config.use_dc:
    self.net = TransformerForDiffusion_DC(config, cond_dim=global_cond_dim)
```

## 下一步

现在你可以：

1. **运行脚本进行测试**：`python run_dc_eval_final.py`
2. **检查DC权重是否正确加载**
3. **比较DC模型和标准模型的性能**
4. **根据需要调整DC参数**

如果一切正常，你应该能看到模型中包含了DC和dc_t相关的层，并且能够从.pth文件中加载预训练的DC权重。 