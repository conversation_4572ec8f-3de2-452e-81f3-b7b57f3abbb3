# MMDiT + SoftREPA 机器人策略学习指南

## 🎯 概述

本指南介绍如何在lerobot中使用MMDiT (Multimodal Diffusion Transformer) 架构，并结合SoftREPA风格的软提示学习技术，实现高效的机器人策略学习和域适应。

## 🏗️ 架构特点

### 1. **MMDiT核心特性**
- **分离权重**: 动作和条件使用独立的参数
- **联合注意力**: 动作和条件token在同一attention中交互
- **多模态处理**: 原生支持视觉、状态等多模态输入

### 2. **SoftREPA集成**
- **DC Tokens**: 可学习的判别/对比token
- **时间依赖**: 支持时间相关的软提示
- **参数高效**: 只需训练少量DC参数

## 🚀 使用方法

### 基础配置

```yaml
# 启用MMDiT
use_transformer: true
use_mmdit: true
use_mmdit_dc: true

# MMDiT参数
n_dc_tokens: 4      # 每层DC token数量
n_dc_layers: 6      # 注入DC token的层数
use_dc_t: true      # 启用时间依赖DC tokens
```

### 训练命令

```bash
# 基础训练
python lerobot/scripts/train.py \
    policy=diffusion_mmdit \
    env=pusht

# 自定义参数
python lerobot/scripts/train.py \
    policy=diffusion_mmdit \
    policy.n_dc_tokens=8 \
    policy.n_dc_layers=4 \
    env=pusht
```

## 🔧 SoftREPA域适应

### 1. **预训练阶段**
```python
# 在源域训练基础模型
model = DiffusionPolicy(config)
# 正常训练...
```

### 2. **域适应阶段**
```python
# 冻结主网络，只训练DC tokens
for param in model.diffusion.net.parameters():
    param.requires_grad = False

# 只训练DC相关参数
for param in model.diffusion.net.dc_tokens:
    param.requires_grad = True
if hasattr(model.diffusion.net, 'dc_t_tokens'):
    for param in model.diffusion.net.dc_t_tokens.parameters():
        param.requires_grad = True
```

### 3. **迁移学习**
```python
# 从预训练模型初始化DC tokens
pretrained_dc = torch.load('pretrained_dc_tokens.pt')
model.diffusion.net.initialize_dc(
    dc_tokens=pretrained_dc['dc_tokens'],
    dc_t_tokens=pretrained_dc.get('dc_t_tokens', None)
)
```

## 📊 架构对比

| 特性 | 标准Diffusion | MMDiT | MMDiT+SoftREPA |
|------|---------------|-------|----------------|
| **参数效率** | 全网络训练 | 全网络训练 | 只训练DC tokens |
| **多模态处理** | 交叉注意力 | 联合注意力 | 增强联合注意力 |
| **域适应** | 困难 | 中等 | 高效 |
| **推理速度** | 标准 | 略慢 | 标准 |

## 🎛️ 超参数调优

### DC Tokens配置
- `n_dc_tokens`: 4-8个通常效果最好
- `n_dc_layers`: 建议为总层数的50-75%
- `use_dc_t`: 对时序任务建议启用

### 训练策略
- **学习率**: DC tokens可以使用更高的学习率 (1e-3)
- **正则化**: 适当的dropout (0.1-0.2)
- **预热**: 建议使用学习率预热

## 🔍 调试和监控

### 关键指标
- DC token的激活分布
- 不同层DC token的贡献
- 时间依赖性的变化

### 可视化
```python
# 可视化DC token的注意力权重
attention_weights = model.get_attention_weights()
plot_attention_heatmap(attention_weights)
```

## 📈 性能优化

### 内存优化
- 使用梯度检查点
- 混合精度训练
- 批量大小调整

### 计算优化
- Flash Attention (如果可用)
- 编译优化
- 多GPU训练

## 🎯 应用场景

### 适合使用MMDiT+SoftREPA的场景
- ✅ 需要快速域适应
- ✅ 多模态输入复杂
- ✅ 计算资源有限
- ✅ 需要参数高效微调

### 不适合的场景
- ❌ 简单的单模态任务
- ❌ 数据量极小
- ❌ 对推理速度要求极高

## 🚨 注意事项

1. **内存使用**: MMDiT比标准transformer使用更多内存
2. **收敛速度**: 可能需要更多训练步数
3. **超参敏感**: DC token数量对性能影响较大
4. **版本兼容**: 确保使用兼容的PyTorch版本

## 📚 参考资料

- [Stable Diffusion 3 论文](https://arxiv.org/abs/2403.03206)
- [SoftREPA 论文](相关链接)
- [Diffusion Policy 原论文](https://arxiv.org/abs/2303.04137)
