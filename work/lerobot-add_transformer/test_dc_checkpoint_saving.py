#!/usr/bin/env python

"""
测试DC tokens保存和加载功能
"""

import sys
import os
from pathlib import Path
import torch
from datetime import datetime

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

from train_dc_with_dataset import (
    create_training_directory, 
    save_dc_tokens_only, 
    load_dc_tokens,
    setup_logging
)


class MockDiffusionModel:
    """模拟DiffusionModel_DC进行测试"""
    def __init__(self):
        self.net = MockNet()
    
    def named_parameters(self):
        return self.net.named_parameters()


class MockNet:
    """模拟网络结构"""
    def __init__(self):
        self.n_dc_tokens = 4
        self.n_dc_layers = 6
        self.use_dc_t = True
        self.hidden_dim = 256
        
        # 创建模拟的DC tokens
        self.dc_tokens = torch.randn(self.n_dc_layers, self.n_dc_tokens, self.hidden_dim)
        self.dc_t_tokens = torch.nn.Embedding(100, self.hidden_dim * self.n_dc_layers)
        
        # 模拟其他参数
        self.other_param = torch.randn(100, 50)
    
    def named_parameters(self):
        return [
            ('net.dc_tokens', torch.nn.Parameter(self.dc_tokens)),
            ('net.dc_t_tokens.weight', torch.nn.Parameter(self.dc_t_tokens.weight)),
            ('net.other_param', torch.nn.Parameter(self.other_param))
        ]


def test_dc_checkpoint_functionality():
    """测试DC checkpoint功能"""
    print("=" * 60)
    print("测试DC tokens保存和加载功能")
    print("=" * 60)
    
    # 1. 测试创建训练目录
    print("1. 测试创建训练目录...")
    training_dir = create_training_directory()
    print(f"   创建的训练目录: {training_dir}")
    assert Path(training_dir).exists(), "训练目录创建失败"
    
    # 2. 设置日志
    print("2. 设置日志...")
    setup_logging(training_dir)
    
    # 3. 创建模拟模型
    print("3. 创建模拟模型...")
    model = MockDiffusionModel()
    
    # 4. 测试保存DC tokens
    print("4. 测试保存DC tokens...")
    checkpoint_path = Path(training_dir) / "test_dc_checkpoint.pth"
    save_dc_tokens_only(
        model, 
        str(checkpoint_path), 
        step=1000, 
        loss=0.5, 
        accuracy=0.8
    )
    assert checkpoint_path.exists(), "DC checkpoint保存失败"
    
    # 5. 测试加载DC tokens
    print("5. 测试加载DC tokens...")
    loaded_checkpoint = load_dc_tokens(str(checkpoint_path))
    
    # 6. 验证保存的内容
    print("6. 验证保存的内容...")
    assert 'dc_tokens' in loaded_checkpoint, "缺少dc_tokens"
    assert 'dc_config' in loaded_checkpoint, "缺少dc_config"
    assert 'training_info' in loaded_checkpoint, "缺少training_info"
    
    dc_tokens = loaded_checkpoint['dc_tokens']
    print(f"   保存的DC参数数量: {len(dc_tokens)}")
    
    # 验证只保存了DC相关参数
    dc_param_names = set(dc_tokens.keys())
    expected_dc_names = {'net.dc_tokens', 'net.dc_t_tokens.weight'}
    assert expected_dc_names.issubset(dc_param_names), f"DC参数不完整: {dc_param_names}"
    assert 'net.other_param' not in dc_param_names, "保存了非DC参数"
    
    # 验证训练信息
    training_info = loaded_checkpoint['training_info']
    assert training_info['step'] == 1000, "训练步数不正确"
    assert training_info['loss'] == 0.5, "损失值不正确"
    assert training_info['accuracy'] == 0.8, "准确率不正确"
    
    print("✅ 所有测试通过!")
    print(f"测试文件保存在: {training_dir}")
    
    return training_dir


if __name__ == "__main__":
    test_dir = test_dc_checkpoint_functionality()
    print(f"\n测试完成，查看结果:")
    print(f"  训练目录: {test_dir}")
    print(f"  日志文件: {Path(test_dir) / 'training.log'}")
    print(f"  检查点文件: {Path(test_dir) / 'test_dc_checkpoint.pth'}") 