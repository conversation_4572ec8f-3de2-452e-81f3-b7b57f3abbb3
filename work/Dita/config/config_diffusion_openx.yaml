epoch: 5000000
iter_per_epoch: 36000
task_name: 'dit policy'
tensorboard_output_dir: ''
batch_size: 64
ckpt_path: None
ckpt_path1: None
lr: 0.0001
prediction_type: 'epsilon'
abs_pose: 0
abs_sup: 0
num_pred_action: 7
n_action_steps: 1
scheduler_type: 0 # diffusion scheduler
num_inference_steps: 100
attn_implementation: 'eager'
add_weight: 0
use_droid: False
dataname: 'oxe_magic_soup_plus'
batchfy: False
shuffle_buffer_size: 64000
use_action_head_diff: 0
data_path: 's3://openx'

optimizer:
  name: 'adamw'
  weight_decay: 0.05
  betas_0: 0.9
  betas_1: 0.95
  
scheduler:
  sched: 'step'
  warmup_lr: 0
  warmup_epochs: 1
  num_epochs: 100000
  decay_epochs: 100000
  step_on_epochs: False

defaults:
  - dataset: openx
  - model:  llama_dp
  - _self_

action_spec:
  world_vector: 
    tensor: 3
    minimum: -0.064
    maximum: 0.064
  rotation_delta:
    tensor: 4
    minimum: -0.064
    maximum: 0.064
  gripper_closedness_action:
    tensor: 1
    minimum: -1.0
    maximum: 1.0
  terminate_episode:
    tensor: 3
    minimum: 0
    maximum: 1

use_close_loop_eval: False

close_loop_eval:
  eval_iters: 10000
  test_episodes_num: 5

hydra:
  job_logging:
    root:
      level: ERROR 
