droid:
  data_path: "s3://Anonymous/Droid_episodes"
  language_embedding_path: "s3://Anonymous/Droid_language_embeddings"
  extrinsic_path: "/xxx/xxx/share_data/Anonymous/Droid_extrinsics"
  traj_per_episode: 16
  traj_length: 2
  cameras_per_scene: 6
  use_baseframe_action: false
  split_type: /xxx/xxx/share_data/Anonymous/select/selected_train_L2_S3_N1_T100.pkl
  train_data_list: '/xxx/xxx/share_data/Anonymous/droid/available_list_(new)_train_S3_L40_N1.pkl' 
  # available_list_sampled(ins)_train_S3_L40_N1.pkl, available_list_(new)_train_S3_L40_N1.pkl
  eval_data_list: null # '/xxx/xxx/share_data/Anonymous/droid/available_list_eval_stride_3_len_40.pkl'
  stride: 3
  gripper_type: 'next_observation' # 'current_action' or 'next_observation'

lab:
  data_path: '/xxx/xxx/share_data/Anonymous/Dataset/LabData_fixgripper'
  language_embedding_path: '/xxx/xxx/share_data/Anonymous/Dataset/LabData_language_embeddings_77token_v0'
  traj_per_episode: 16
  traj_length: 2
  cameras_per_scene: 20
  use_baseframe_action: false
  split_type: 'fix_traj'
  train_data_list: '/xxx/xxx/share_data/Anonymous/Dataset/Sim_Data/FilePath/labdata_train_list_repeat_50times.pkl'
  eval_data_list: '/xxx/xxx/share_data/Anonymous/Dataset/Sim_Data/FilePath/labdata_eval_list.pkl'
  stride: 4

action_spec:
  world_vector:
    tensor: 3
    minimum: -0.1024
    maximum: 0.1024
  rotation_delta:
    tensor: 4
    minimum: -0.1024
    maximum: 0.1024
  gripper_closedness_action:
    tensor: 1
    minimum: 0.0
    maximum: 1.0
  terminate_episode:
    tensor: 3
    minimum: 0
    maximum: 1