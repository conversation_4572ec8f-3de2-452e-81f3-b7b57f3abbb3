{"file_format_version": "1.0.0", "layer": {"name": "VK_LAYER_NV_optimus", "type": "INSTANCE", "library_path": "libGLX_nvidia.so.0", "api_version": "1.2.155", "implementation_version": "1", "description": "NVIDIA Optimus layer", "functions": {"vkGetInstanceProcAddr": "vk_optimusGetInstanceProcAddr", "vkGetDeviceProcAddr": "vk_optimusGetDeviceProcAddr"}, "enable_environment": {"__NV_PRIME_RENDER_OFFLOAD": "1"}, "disable_environment": {"DISABLE_LAYER_NV_OPTIMUS_1": ""}}}